// Import main styles
import '../scss/main.scss';

// Import Chart.js and plugins
import Chart from 'chart.js/auto';
import { DateTime } from 'luxon';
import 'chartjs-adapter-luxon';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import annotationPlugin from 'chartjs-plugin-annotation';

// Register Chart.js plugins
Chart.register(ChartDataLabels, annotationPlugin);

// Initialize common UI elements
document.addEventListener('DOMContentLoaded', () => {
  initializeSidebar();
  initializeAnimations();
});

/**
 * Initialize sidebar functionality
 */
function initializeSidebar() {
  // Mobile sidebar toggle
  const sidebarToggle = document.getElementById('sidebarToggle');
  const sidebar = document.querySelector('.sidebar');
  
  if (sidebarToggle && sidebar) {
    sidebarToggle.addEventListener('click', () => {
      sidebar.classList.toggle('sidebar-open');
    });
  }
  
  // Close sidebar when clicking outside on mobile
  document.addEventListener('click', (e) => {
    if (window.innerWidth <= 992 && 
        sidebar && 
        sidebar.classList.contains('sidebar-open') && 
        !sidebar.contains(e.target) && 
        e.target !== sidebarToggle) {
      sidebar.classList.remove('sidebar-open');
    }
  });
}

/**
 * Initialize animations for UI elements
 */
function initializeAnimations() {
  // Add animation classes to elements
  const animatedElements = document.querySelectorAll('.status-box, .card, .data-visualization');
  
  animatedElements.forEach(element => {
    element.addEventListener('mouseenter', () => {
      element.classList.add('element-hover');
    });
    
    element.addEventListener('mouseleave', () => {
      element.classList.remove('element-hover');
    });
  });
  
  // Animate status indicators
  const statusIndicators = document.querySelectorAll('.status-indicator');
  statusIndicators.forEach(indicator => {
    if (indicator.classList.contains('online')) {
      indicator.style.animation = 'pulse 2s infinite';
    }
  });
}

// Export common utilities
export const utils = {
  /**
   * Format number with specified decimal places
   * @param {number} value - The number to format
   * @param {number} decimals - Number of decimal places
   * @returns {string} Formatted number
   */
  formatNumber: (value, decimals = 3) => {
    if (value === undefined || value === null) return '0'.padEnd(decimals + 2, '0');
    try {
      return parseFloat(value).toFixed(decimals);
    } catch (e) {
      return '0'.padEnd(decimals + 2, '0');
    }
  },
  
  /**
   * Format timestamp for display in Indian Standard Time (IST)
   * @param {string} timestamp - The timestamp to format
   * @returns {string} Formatted timestamp
   */
  formatTimestamp: (timestamp) => {
    if (!timestamp) return 'N/A';
    try {
      // Try parsing as SQL timestamp first (YYYY-MM-DD HH:mm:ss)
      let date = DateTime.fromSQL(timestamp);

      // If that fails, try ISO format
      if (!date.isValid) {
        date = DateTime.fromISO(timestamp);
      }

      // If still invalid, try JavaScript Date parsing
      if (!date.isValid) {
        date = DateTime.fromJSDate(new Date(timestamp));
      }

      // If still invalid, return original timestamp
      if (!date.isValid) {
        console.warn('Could not parse timestamp:', timestamp);
        return timestamp;
      }

      // Format with explicit IST timezone
      return date.setZone('Asia/Kolkata').toFormat('dd LLL yyyy, hh:mm:ss a');
    } catch (e) {
      console.error('Error formatting timestamp:', e);
      return timestamp;
    }
  },
  
  /**
   * Show a message to the user
   * @param {string} message - The message to display
   * @param {string} type - Message type (info, success, warning, error)
   * @param {HTMLElement} container - Container element
   * @param {number} timeout - Auto-hide timeout in ms (0 for no auto-hide)
   */
  showMessage: (message, type = 'info', container, timeout = 5000) => {
    if (!container) return;
    
    const messageEl = document.createElement('div');
    messageEl.className = `message ${type}`;
    messageEl.innerHTML = message;
    
    // Clear existing messages
    container.innerHTML = '';
    container.appendChild(messageEl);
    
    // Auto-hide success messages
    if (timeout > 0 && (type === 'success' || type === 'info')) {
      setTimeout(() => {
        messageEl.style.opacity = '0';
        messageEl.style.transform = 'translateY(-10px)';
        
        setTimeout(() => {
          if (container.contains(messageEl)) {
            container.removeChild(messageEl);
          }
        }, 300);
      }, timeout);
    }
  },
  
  /**
   * Create a debounced function
   * @param {Function} func - The function to debounce
   * @param {number} wait - Wait time in ms
   * @returns {Function} Debounced function
   */
  debounce: (func, wait = 300) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
};
