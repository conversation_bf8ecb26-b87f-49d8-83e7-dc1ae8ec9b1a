import{D as _,u as o,C as p}from"./main-f3094cb4.js";let i={};const s={analysisType:"comprehensive",timeRange:"1h",analysisData:null,charts:{},isLoading:!1};function C(){i={analysisTypeButtons:document.querySelectorAll(".analysis-type-btn"),timeRangeSelect:document.getElementById("timeRange"),runAnalysisBtn:document.getElementById("runAnalysisBtn"),metricsContainer:document.getElementById("metricsContainer"),chartsContainer:document.getElementById("chartsContainer"),recommendationsContainer:document.getElementById("recommendationsContainer"),loadingIndicator:document.getElementById("loadingIndicator"),messageContainer:document.getElementById("messageContainer")},B(),f()}function B(){i.analysisTypeButtons&&i.analysisTypeButtons.forEach(e=>{e.addEventListener("click",()=>{T(e.dataset.type)})}),i.timeRangeSelect&&i.timeRangeSelect.addEventListener("change",e=>{s.timeRange=e.target.value}),i.runAnalysisBtn&&i.runAnalysisBtn.addEventListener("click",f)}function T(e){s.analysisType=e,i.analysisTypeButtons.forEach(t=>{t.classList.remove("active"),t.dataset.type===e&&t.classList.add("active")})}async function f(){if(!s.isLoading)try{s.isLoading=!0,b(!0),P();const e=S(s.timeRange),t=await fetch(`../backend/stability_analysis.php?type=${s.analysisType}&start=${encodeURIComponent(e.start)}&end=${encodeURIComponent(e.end)}`);if(!t.ok)throw new Error(`HTTP error! Status: ${t.status}`);const a=await t.json();if(a.error)throw new Error(a.error);s.analysisData=a,w(a),h("Stability analysis completed successfully","success")}catch(e){console.error("Error running stability analysis:",e),h(`Error: ${e.message}`,"error")}finally{s.isLoading=!1,b(!1)}}function S(e){const t=_.now().setZone("Asia/Kolkata");let a=t;switch(e){case"1h":a=t.minus({hours:1});break;case"6h":a=t.minus({hours:6});break;case"24h":a=t.minus({hours:24});break;case"7d":a=t.minus({days:7});break;case"30d":a=t.minus({days:30});break;default:a=t.minus({hours:1})}return{start:a.toFormat("yyyy-MM-dd HH:mm:ss"),end:t.toFormat("yyyy-MM-dd HH:mm:ss")}}function w(e){s.analysisType==="comprehensive"?$(e):D(e,s.analysisType)}function $(e){g(e),M(e),R(e.recommendations)}function g(e){if(!i.metricsContainer)return;const t=e.stability_score||{score:0,grade:"N/A"};let a=`
        <div class="stability-metric-card">
            <div class="metric-header">
                <div class="metric-icon">
                    <span class="material-icons-round">analytics</span>
                </div>
                <h3 class="metric-title">Overall Stability Score</h3>
            </div>
            <div class="stability-score-indicator" style="--score-angle: ${t.score/100*360}deg;">
                <div class="score-circle">
                    <div class="score-text">
                        <div class="score-number">${t.score}</div>
                        <div class="score-label">Score</div>
                    </div>
                </div>
            </div>
            <div class="metric-description">Grade: ${t.grade}</div>
        </div>
    `;[{key:"voltage_stability",title:"Voltage Stability",icon:"bolt"},{key:"current_stability",title:"Current Stability",icon:"electric_bolt"},{key:"frequency_stability",title:"Frequency Stability",icon:"speed"},{key:"power_stability",title:"Power Stability",icon:"power"}].forEach(r=>{const n=e[r.key];n&&(a+=k(r,n))}),i.metricsContainer.innerHTML=a}function k(e,t){let a="N/A";return t.voltage_1&&t.voltage_1.stability?a=t.voltage_1.stability.stability_score||"N/A":t.statistics&&(a=Math.round(100-(t.statistics.coefficient_of_variation||0))),`
        <div class="stability-metric-card">
            <div class="metric-header">
                <div class="metric-icon">
                    <span class="material-icons-round">${e.icon}</span>
                </div>
                <h3 class="metric-title">${e.title}</h3>
            </div>
            <div class="metric-value">${a}${typeof a=="number"?"%":""}</div>
            <div class="metric-description">
                ${A(e.key)}
            </div>
            <div class="metric-details">
                ${E(t)}
            </div>
        </div>
    `}function A(e,t){switch(e){case"voltage_stability":return"Voltage stability across all three phases";case"current_stability":return"Current stability and load balance";case"frequency_stability":return"System frequency stability and quality";case"power_stability":return"Power factor and total power stability";default:return"Stability analysis metric"}}function E(e){let t="";return e.statistics&&(t+=`
            <div class="metric-detail">
                <span class="metric-detail-label">Mean</span>
                <span class="metric-detail-value">${o.formatNumber(e.statistics.mean)}</span>
            </div>
            <div class="metric-detail">
                <span class="metric-detail-label">Std Dev</span>
                <span class="metric-detail-value">${o.formatNumber(e.statistics.std_dev)}</span>
            </div>
        `),e.phase_balance&&(t+=`
            <div class="metric-detail">
                <span class="metric-detail-label">Balance</span>
                <span class="metric-detail-value">${e.phase_balance.balance_quality}</span>
            </div>
        `),t}function M(e){if(!i.chartsContainer)return;let t=`
        <div class="analysis-chart-card">
            <div class="chart-header">
                <h4 class="chart-title">Stability Trends</h4>
            </div>
            <div class="chart-container">
                <canvas id="stabilityTrendChart"></canvas>
            </div>
        </div>
        
        <div class="analysis-chart-card">
            <div class="chart-header">
                <h4 class="chart-title">Phase Balance</h4>
            </div>
            <div class="chart-container">
                <canvas id="phaseBalanceChart"></canvas>
            </div>
        </div>
    `;i.chartsContainer.innerHTML=t,setTimeout(()=>{L(e),I(e)},100)}function L(e){var r,n,l,d,y,m,u,v;const t=document.getElementById("stabilityTrendChart");if(!t)return;const a=t.getContext("2d");s.charts.stabilityTrend&&s.charts.stabilityTrend.destroy();const c={labels:["Voltage","Current","Power","Frequency"],datasets:[{label:"Stability Score",data:[((n=(r=e.stability_score)==null?void 0:r.factors)==null?void 0:n.voltage_stability)||0,((d=(l=e.stability_score)==null?void 0:l.factors)==null?void 0:d.current_stability)||0,((m=(y=e.stability_score)==null?void 0:y.factors)==null?void 0:m.power_stability)||0,((v=(u=e.stability_score)==null?void 0:u.factors)==null?void 0:v.frequency_stability)||0],backgroundColor:"rgba(6, 182, 212, 0.2)",borderColor:"#06b6d4",borderWidth:2,tension:.4}]};s.charts.stabilityTrend=new p(a,{type:"radar",data:c,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{labels:{color:"#f8fafc"}}},scales:{r:{beginAtZero:!0,max:100,grid:{color:"rgba(148, 163, 184, 0.15)"},pointLabels:{color:"#cbd5e1"},ticks:{color:"#64748b"}}}}})}function I(e){var n,l;const t=document.getElementById("phaseBalanceChart");if(!t)return;const a=t.getContext("2d");s.charts.phaseBalance&&s.charts.phaseBalance.destroy();const c=((l=(n=e.load_balance)==null?void 0:n.voltage_balance)==null?void 0:l.phase_averages)||{},r={labels:["Phase 1","Phase 2","Phase 3"],datasets:[{label:"Voltage (V)",data:[c.voltage_1||0,c.voltage_2||0,c.voltage_3||0],backgroundColor:["#06b6d4","#3b82f6","#8b5cf6"],borderColor:["#06b6d4","#3b82f6","#8b5cf6"],borderWidth:2}]};s.charts.phaseBalance=new p(a,{type:"bar",data:r,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{labels:{color:"#f8fafc"}}},scales:{x:{grid:{color:"rgba(148, 163, 184, 0.15)"},ticks:{color:"#cbd5e1"}},y:{grid:{color:"rgba(148, 163, 184, 0.15)"},ticks:{color:"#cbd5e1"}}}}})}function R(e){if(!i.recommendationsContainer||!e)return;let t=`
        <h3 class="recommendations-title">
            <span class="material-icons-round">lightbulb</span>
            Recommendations
        </h3>
        <div class="recommendations-grid">
    `;e.immediate_actions&&e.immediate_actions.length>0&&(t+=`
            <div class="recommendation-category">
                <h4>Immediate Actions</h4>
                <ul class="recommendation-list">
                    ${e.immediate_actions.map(a=>`<li>${a}</li>`).join("")}
                </ul>
            </div>
        `),e.preventive_measures&&e.preventive_measures.length>0&&(t+=`
            <div class="recommendation-category">
                <h4>Preventive Measures</h4>
                <ul class="recommendation-list">
                    ${e.preventive_measures.map(a=>`<li>${a}</li>`).join("")}
                </ul>
            </div>
        `),e.optimization_opportunities&&e.optimization_opportunities.length>0&&(t+=`
            <div class="recommendation-category">
                <h4>Optimization Opportunities</h4>
                <ul class="recommendation-list">
                    ${e.optimization_opportunities.map(a=>`<li>${a}</li>`).join("")}
                </ul>
            </div>
        `),t+="</div>",i.recommendationsContainer.innerHTML=t}function D(e,t){g({[t]:e})}function b(e){i.loadingIndicator&&(i.loadingIndicator.style.display=e?"flex":"none")}function h(e,t="info"){i.messageContainer&&o.showMessage(e,t,i.messageContainer)}function P(){i.messageContainer&&(i.messageContainer.innerHTML="")}document.addEventListener("DOMContentLoaded",C);
