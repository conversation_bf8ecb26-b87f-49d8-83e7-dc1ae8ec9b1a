<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Historical Data Viewer</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }

        body {
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }

        header h1 {
            color: #2c3e50;
            font-size: 24px;
        }

        .back-link {
            color: #3498db;
            text-decoration: none;
            display: flex;
            align-items: center;
        }

        .back-link:hover {
            text-decoration: underline;
        }

        .filters {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            margin-bottom: 5px;
            font-weight: bold;
            font-size: 14px;
            color: #555;
        }

        input, select, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        button {
            background-color: #3498db;
            color: white;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #2980b9;
        }

        button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }

        .view-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .view-mode {
            display: flex;
            gap: 10px;
        }

        .data-container {
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            margin-top: 20px;
        }

        .page-info {
            font-size: 14px;
            color: #555;
        }

        .message {
            padding: 20px;
            text-align: center;
            background-color: #f8f9fa;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .message.error {
            background-color: #fee;
            color: #e74c3c;
            border: 1px solid #e74c3c;
        }

        .message.warning {
            background-color: #fef9e7;
            color: #f39c12;
            border: 1px solid #f39c12;
        }

        .message.info {
            background-color: #eef7fb;
            color: #3498db;
            border: 1px solid #3498db;
        }

        .raw-data {
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            overflow: auto;
            max-height: 500px;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 12px;
        }

        .debug-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #ddd;
        }

        .debug-info h3 {
            margin-bottom: 10px;
            color: #555;
            font-size: 16px;
        }

        .debug-info pre {
            font-family: monospace;
            font-size: 12px;
            overflow: auto;
            max-height: 200px;
            background-color: #fff;
            padding: 10px;
            border-radius: 3px;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            font-size: 18px;
            color: #555;
        }

        .sample-date-btn {
            background-color: #2ecc71;
            color: white;
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }

        .sample-date-btn:hover {
            background-color: #27ae60;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Historical Data Viewer</h1>
            <a href="pro_dashboard.php" class="back-link">← Back to Dashboard</a>
        </header>

        <div class="filters">
            <div class="filter-group">
                <label for="startDate">Start Date</label>
                <input type="datetime-local" id="startDate" name="startDate">
            </div>

            <div class="filter-group">
                <label for="endDate">End Date</label>
                <input type="datetime-local" id="endDate" name="endDate">
            </div>

            <div class="filter-group">
                <label for="limit">Records Per Page</label>
                <select id="limit" name="limit">
                    <option value="10">10</option>
                    <option value="25" selected>25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
            </div>

            <div class="filter-group">
                <label>&nbsp;</label>
                <button id="filterBtn">Apply Filters</button>
            </div>

            <div class="filter-group">
                <label>&nbsp;</label>
                <button id="exportBtn">Export CSV</button>
            </div>
        </div>

        <div class="view-controls">
            <div class="view-mode">
                <button id="tableViewBtn" class="active">Table View</button>
                <button id="rawViewBtn">Raw Data View</button>
            </div>
            <div id="recordCount"></div>
        </div>

        <div id="messageContainer"></div>

        <div id="tableView" class="data-container">
            <table>
                <thead>
                    <tr>
                        <th>Timestamp</th>
                        <th>V12 (V)</th>
                        <th>V23 (V)</th>
                        <th>V31 (V)</th>
                        <th>Ia (A)</th>
                        <th>Ib (A)</th>
                        <th>Ic (A)</th>
                        <th>PF1</th>
                        <th>PF2</th>
                        <th>PF3</th>
                        <th>Total kW</th>
                        <th>Frequency (Hz)</th>
                    </tr>
                </thead>
                <tbody id="dataRows">
                    <tr>
                        <td colspan="12" class="loading">Loading data...</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div id="rawView" class="data-container" style="display: none;">
            <div class="raw-data" id="rawData">No data available</div>
        </div>

        <div class="pagination">
            <button id="prevBtn" disabled>Previous</button>
            <span id="pageInfo" class="page-info">Page 1 of 1</span>
            <button id="nextBtn" disabled>Next</button>
        </div>

        <div id="debugContainer" class="debug-info" style="display: none;">
            <h3>Debug Information</h3>
            <div id="debugContent"></div>
        </div>
    </div>

    <script>
        // State management
        const state = {
            currentPage: 1,
            totalPages: 1,
            data: [],
            isLoading: false,
            error: null,
            viewMode: 'table', // 'table' or 'raw'
            debugMode: false
        };

        // DOM elements
        const elements = {
            startDate: document.getElementById('startDate'),
            endDate: document.getElementById('endDate'),
            limit: document.getElementById('limit'),
            filterBtn: document.getElementById('filterBtn'),
            exportBtn: document.getElementById('exportBtn'),
            tableViewBtn: document.getElementById('tableViewBtn'),
            rawViewBtn: document.getElementById('rawViewBtn'),
            tableView: document.getElementById('tableView'),
            rawView: document.getElementById('rawView'),
            dataRows: document.getElementById('dataRows'),
            rawData: document.getElementById('rawData'),
            prevBtn: document.getElementById('prevBtn'),
            nextBtn: document.getElementById('nextBtn'),
            pageInfo: document.getElementById('pageInfo'),
            messageContainer: document.getElementById('messageContainer'),
            recordCount: document.getElementById('recordCount'),
            debugContainer: document.getElementById('debugContainer'),
            debugContent: document.getElementById('debugContent')
        };

        // Utility functions
        const utils = {
            // Format date for datetime-local input (in IST - Indian Standard Time)
            formatDateForInput: (date) => {
                // Convert to IST by adding 5 hours and 30 minutes to UTC
                const istDate = new Date(date.getTime() + (5.5 * 60 * 60 * 1000));
                return istDate.toISOString().slice(0, 16);
            },

            // Format date for server (YYYY-MM-DD HH:MM:SS)
            formatDateForServer: (dateStr) => {
                const date = new Date(dateStr);
                return date.toISOString().slice(0, 19).replace('T', ' ');
            },

            // Format number with 2 decimal places
            formatNumber: (value) => {
                if (value === undefined || value === null) return '0.00';
                try {
                    return parseFloat(value).toFixed(2);
                } catch (e) {
                    return '0.00';
                }
            },

            // Format timestamp for display
            formatTimestamp: (timestamp) => {
                if (!timestamp) return 'N/A';
                try {
                    const date = new Date(timestamp);
                    if (isNaN(date.getTime())) return timestamp;

                    const options = {
                        timeZone: 'Asia/Kolkata',
                        year: 'numeric',
                        month: 'numeric',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: true
                    };
                    return date.toLocaleString('en-IN', options);
                } catch (e) {
                    console.error('Error formatting timestamp:', e);
                    return timestamp;
                }
            },

            // Show a message to the user
            showMessage: (message, type = 'info') => {
                elements.messageContainer.innerHTML = `
                    <div class="message ${type}">
                        ${message}
                    </div>
                `;
            },

            // Clear any displayed messages
            clearMessage: () => {
                elements.messageContainer.innerHTML = '';
            },

            // Toggle view mode
            setViewMode: (mode) => {
                state.viewMode = mode;

                if (mode === 'table') {
                    elements.tableView.style.display = 'block';
                    elements.rawView.style.display = 'none';
                    elements.tableViewBtn.classList.add('active');
                    elements.rawViewBtn.classList.remove('active');
                } else {
                    elements.tableView.style.display = 'none';
                    elements.rawView.style.display = 'block';
                    elements.tableViewBtn.classList.remove('active');
                    elements.rawViewBtn.classList.add('active');
                }
            },

            // Update record count display
            updateRecordCount: () => {
                elements.recordCount.textContent = `${state.data.length} records found`;
            },

            // Show debug information
            showDebugInfo: (debugData) => {
                if (!debugData) {
                    elements.debugContainer.style.display = 'none';
                    return;
                }

                elements.debugContainer.style.display = 'block';
                elements.debugContent.innerHTML = `<pre>${JSON.stringify(debugData, null, 2)}</pre>`;
            }
        };

        // API functions
        const api = {
            // Fetch historical data from the backend
            fetchHistoricalData: async () => {
                try {
                    state.isLoading = true;
                    utils.clearMessage();
                    elements.dataRows.innerHTML = '<tr><td colspan="12" class="loading">Loading data...</td></tr>';

                    // Get filter values
                    const startDate = elements.startDate.value;
                    const endDate = elements.endDate.value;
                    const limit = elements.limit.value;
                    const offset = (state.currentPage - 1) * parseInt(limit);

                    // Format dates for server
                    const startFormatted = utils.formatDateForServer(startDate);
                    const endFormatted = utils.formatDateForServer(endDate);

                    // Add cache buster
                    const cacheBuster = new Date().getTime();

                    // Build URL
                    const url = `../../app/backend/api/api/get_historical_data.php?start=${encodeURIComponent(startFormatted)}&end=${encodeURIComponent(endFormatted)}&limit=${limit}&offset=${offset}&_=${cacheBuster}`;

                    console.log('Fetching data with URL:', url);

                    // Fetch data
                    const response = await fetch(url);
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }

                    // Parse response
                    const responseText = await response.text();
                    console.log('Raw response:', responseText);

                    let data;
                    try {
                        data = JSON.parse(responseText);
                    } catch (parseError) {
                        throw new Error(`Failed to parse JSON response: ${parseError.message}`);
                    }

                    console.log('Parsed data:', data);

                    // Handle error in response
                    if (data.error) {
                        utils.showMessage(data.error, 'error');
                        utils.showDebugInfo(data.debug);
                        state.error = data.error;
                        state.data = [];
                        return null;
                    }

                    // Process data
                    if (Array.isArray(data.data)) {
                        state.data = data.data;
                    } else if (data.data && typeof data.data === 'object') {
                        state.data = [data.data];
                    } else {
                        state.data = [];
                    }

                    // Update state
                    state.totalPages = Math.ceil(data.total / parseInt(limit));
                    state.error = null;

                    // Check if we have no data but sample data exists
                    if (state.data.length === 0 && data.debug && data.debug.test_query_result && data.debug.test_query_result.length > 0) {
                        const sampleData = data.debug.test_query_result[0];
                        const sampleTimestamp = sampleData.timestamp;

                        // Format the sample timestamp for display
                        const formattedTimestamp = utils.formatTimestamp(sampleTimestamp);

                        // Create a more detailed message with the sample data
                        let sampleDataHtml = '<table style="margin-top: 10px; width: 100%; border-collapse: collapse;">';
                        sampleDataHtml += '<tr style="background-color: #f0f0f0;"><th colspan="12" style="padding: 8px; text-align: left;">Sample Data Available:</th></tr>';
                        sampleDataHtml += '<tr>';
                        sampleDataHtml += `<td style="padding: 8px; border: 1px solid #ddd;">${formattedTimestamp}</td>`;
                        sampleDataHtml += `<td style="padding: 8px; border: 1px solid #ddd;">${utils.formatNumber(sampleData.voltage_1)}</td>`;
                        sampleDataHtml += `<td style="padding: 8px; border: 1px solid #ddd;">${utils.formatNumber(sampleData.voltage_2)}</td>`;
                        sampleDataHtml += `<td style="padding: 8px; border: 1px solid #ddd;">${utils.formatNumber(sampleData.voltage_3)}</td>`;
                        sampleDataHtml += `<td style="padding: 8px; border: 1px solid #ddd;">${utils.formatNumber(sampleData.current_1)}</td>`;
                        sampleDataHtml += `<td style="padding: 8px; border: 1px solid #ddd;">${utils.formatNumber(sampleData.current_2)}</td>`;
                        sampleDataHtml += `<td style="padding: 8px; border: 1px solid #ddd;">${utils.formatNumber(sampleData.current_3)}</td>`;
                        sampleDataHtml += `<td style="padding: 8px; border: 1px solid #ddd;">${utils.formatNumber(sampleData.pf_1)}</td>`;
                        sampleDataHtml += `<td style="padding: 8px; border: 1px solid #ddd;">${utils.formatNumber(sampleData.pf_2)}</td>`;
                        sampleDataHtml += `<td style="padding: 8px; border: 1px solid #ddd;">${utils.formatNumber(sampleData.pf_3)}</td>`;
                        sampleDataHtml += `<td style="padding: 8px; border: 1px solid #ddd;">${utils.formatNumber(sampleData.total_kw)}</td>`;
                        sampleDataHtml += `<td style="padding: 8px; border: 1px solid #ddd;">${utils.formatNumber(sampleData.frequency)}</td>`;
                        sampleDataHtml += '</tr>';
                        sampleDataHtml += '</table>';

                        utils.showMessage(`
                            <h3>No data found for the selected time range</h3>
                            <p>There is data in the database, but not for the specific time range you selected.</p>
                            <p>We found data at: <strong>${formattedTimestamp}</strong></p>
                            ${sampleDataHtml}
                            <div style="margin-top: 15px; text-align: center;">
                                <button id="useSampleDateBtn" class="sample-date-btn">View Data Around This Time</button>
                            </div>
                        `, 'warning');

                        // Add event listener to the button
                        document.getElementById('useSampleDateBtn').addEventListener('click', () => {
                            // Parse the sample timestamp
                            const sampleDate = new Date(sampleTimestamp);

                            // Set start date to 5 minutes before the sample
                            const startDate = new Date(sampleDate);
                            startDate.setMinutes(startDate.getMinutes() - 5);

                            // Set end date to 5 minutes after the sample
                            const endDate = new Date(sampleDate);
                            endDate.setMinutes(endDate.getMinutes() + 5);

                            // Format dates for the input fields
                            elements.startDate.value = utils.formatDateForInput(startDate);
                            elements.endDate.value = utils.formatDateForInput(endDate);

                            // Show a message that we're updating
                            utils.showMessage('Updating to show data around ' + formattedTimestamp + '...', 'info');

                            // Fetch data with the new date range
                            api.fetchHistoricalData();
                        });
                    } else if (state.data.length === 0) {
                        utils.showMessage('No data found for the selected period', 'info');
                    } else {
                        utils.clearMessage();
                    }

                    // Update UI
                    ui.renderTable();
                    ui.updatePagination();
                    ui.updateRawView();
                    utils.updateRecordCount();
                    utils.showDebugInfo(state.debugMode ? data.debug : null);

                    return data;
                } catch (error) {
                    console.error('Error fetching historical data:', error);
                    utils.showMessage(`Error: ${error.message}`, 'error');
                    state.error = error.message;
                    state.data = [];
                    return null;
                } finally {
                    state.isLoading = false;
                }
            },

            // Export data as CSV
            exportCSV: () => {
                if (state.data.length === 0) {
                    alert('No data to export');
                    return;
                }

                // Create CSV header
                let csv = 'Timestamp,V12 (V),V23 (V),V31 (V),Ia (A),Ib (A),Ic (A),PF1,PF2,PF3,Total kW,Frequency (Hz)\n';

                // Add data rows
                state.data.forEach(row => {
                    const timestamp = utils.formatTimestamp(row.timestamp);
                    csv += `"${timestamp}",${row.voltage_1},${row.voltage_2},${row.voltage_3},${row.current_1},${row.current_2},${row.current_3},${row.pf_1},${row.pf_2},${row.pf_3},${row.total_kw},${row.frequency}\n`;
                });

                // Create download link
                const blob = new Blob([csv], { type: 'text/csv' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.setAttribute('hidden', '');
                a.setAttribute('href', url);
                a.setAttribute('download', `electrical_data_${new Date().toISOString().slice(0, 10)}.csv`);
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            }
        };

        // UI functions
        const ui = {
            // Render the data table
            renderTable: () => {
                if (state.data.length === 0) {
                    elements.dataRows.innerHTML = '<tr><td colspan="12" style="text-align: center;">No data available</td></tr>';
                    return;
                }

                let html = '';
                state.data.forEach(row => {
                    const timestamp = utils.formatTimestamp(row.timestamp);
                    html += `<tr>
                        <td>${timestamp}</td>
                        <td>${utils.formatNumber(row.voltage_1)}</td>
                        <td>${utils.formatNumber(row.voltage_2)}</td>
                        <td>${utils.formatNumber(row.voltage_3)}</td>
                        <td>${utils.formatNumber(row.current_1)}</td>
                        <td>${utils.formatNumber(row.current_2)}</td>
                        <td>${utils.formatNumber(row.current_3)}</td>
                        <td>${utils.formatNumber(row.pf_1)}</td>
                        <td>${utils.formatNumber(row.pf_2)}</td>
                        <td>${utils.formatNumber(row.pf_3)}</td>
                        <td>${utils.formatNumber(row.total_kw)}</td>
                        <td>${utils.formatNumber(row.frequency)}</td>
                    </tr>`;
                });

                elements.dataRows.innerHTML = html;
            },

            // Update the raw data view
            updateRawView: () => {
                elements.rawData.textContent = JSON.stringify(state.data, null, 2);
            },

            // Update pagination controls
            updatePagination: () => {
                elements.pageInfo.textContent = `Page ${state.currentPage} of ${state.totalPages || 1}`;
                elements.prevBtn.disabled = state.currentPage <= 1;
                elements.nextBtn.disabled = state.currentPage >= state.totalPages;
            },

            // Initialize the page
            init: () => {
                // Set default date range using the exact timestamp from the database (2025-04-23 09:59:05)
                const sampleDate = new Date('2025-04-23 09:59:05');

                if (!isNaN(sampleDate.getTime())) {
                    // Set start date to 5 minutes before the sample
                    const startDate = new Date(sampleDate);
                    startDate.setMinutes(startDate.getMinutes() - 5);

                    // Set end date to 5 minutes after the sample
                    const endDate = new Date(sampleDate);
                    endDate.setMinutes(endDate.getMinutes() + 5);

                    console.log('Setting date range around sample data:',
                        startDate.toISOString(), 'to', endDate.toISOString());

                    elements.startDate.value = utils.formatDateForInput(startDate);
                    elements.endDate.value = utils.formatDateForInput(endDate);
                } else {
                    // Fallback to current time
                    const now = new Date();
                    const yesterday = new Date(now);
                    yesterday.setDate(yesterday.getDate() - 1);

                    elements.startDate.value = utils.formatDateForInput(yesterday);
                    elements.endDate.value = utils.formatDateForInput(now);
                }

                // Add event listeners
                elements.filterBtn.addEventListener('click', () => {
                    state.currentPage = 1;
                    api.fetchHistoricalData();
                });

                elements.exportBtn.addEventListener('click', api.exportCSV);

                elements.prevBtn.addEventListener('click', () => {
                    if (state.currentPage > 1) {
                        state.currentPage--;
                        api.fetchHistoricalData();
                    }
                });

                elements.nextBtn.addEventListener('click', () => {
                    if (state.currentPage < state.totalPages) {
                        state.currentPage++;
                        api.fetchHistoricalData();
                    }
                });

                elements.tableViewBtn.addEventListener('click', () => {
                    utils.setViewMode('table');
                });

                elements.rawViewBtn.addEventListener('click', () => {
                    utils.setViewMode('raw');
                });

                // Toggle debug mode with keyboard shortcut (Ctrl+Shift+D)
                document.addEventListener('keydown', (e) => {
                    if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                        state.debugMode = !state.debugMode;
                        utils.showDebugInfo(state.debugMode ? 'Debug mode enabled' : null);
                        console.log('Debug mode:', state.debugMode);
                    }
                });

                // Initial data fetch
                api.fetchHistoricalData();
            }
        };

        // Initialize the page
        document.addEventListener('DOMContentLoaded', ui.init);
    </script>
</body>
</html>
