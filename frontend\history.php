<?php
// Initialize the session
session_start();

// Check if the user is logged in, if not redirect to login page
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: login.php");
    exit;
}

// Include Vite helper functions
require_once 'vite-helper.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Historical Data - Online Data Logger System</title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="images/online-data-logger-logo-new.svg">

    <!-- Vite Assets -->
    <?php echo viteClientScript(); ?>
    <?php echo viteCssTag('history'); ?>
    <?php echo viteScriptTag('history'); ?>


</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar Navigation -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="images/online-data-logger-sidebar.svg" alt="Online Data Logger System" class="company-logo" width="180">
            </div>

            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="advanced_dashboard.php" id="dashboardLink">
                            <span class="material-icons-round">dashboard</span>
                            <span>Live Dashboard</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="history.php" id="historyLink">
                            <span class="material-icons-round">history</span>
                            <span>Historical Data</span>
                        </a>
                    </li>
                    <li>
                        <a href="monk-data.php" id="monkDataLink">
                            <span class="material-icons-round">analytics</span>
                            <span>Monk Data Analysis</span>
                        </a>
                    </li>
                    <li>
                        <a href="test-data.php" id="testDataLink">
                            <span class="material-icons-round">science</span>
                            <span>Test Data</span>
                        </a>
                    </li>
                    <li>
                        <a href="stability_analysis.php">
                            <span class="material-icons-round">analytics</span>
                            <span>Stability Analysis</span>
                        </a>
                    </li>
                    <li>
                        <a href="alerts_events.php">
                            <span class="material-icons-round">warning</span>
                            <span>Alerts & Events</span>
                        </a>
                    </li>
                    <li>
                        <a href="reports.php">
                            <span class="material-icons-round">insights</span>
                            <span>Reports</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" id="settingsLink">
                            <span class="material-icons-round">settings</span>
                            <span>System Settings</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <div class="system-status">
                    <span class="status-indicator online"></span>
                    <span>System Online</span>
                </div>
                <a href="logout.php" class="logout-button">
                    <span class="material-icons-round">logout</span>
                    <span>Logout</span>
                </a>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Status Header -->
            <header class="status-header">
                <div class="status-title">
                    <div class="header-logo-container">
                        <img src="images/online-data-logger-logo-new.svg" alt="Online Data Logger System" class="header-logo">
                    </div>
                    <div class="subtitle">Historical Data Analysis</div>
                </div>

                <div class="status-boxes">
                    <div class="status-box system">
                        <div class="status-icon">
                            <span class="material-icons-round">history</span>
                        </div>
                        <div class="status-text">
                            <div class="status-label">Data Status</div>
                            <div class="status-value">Available</div>
                        </div>
                    </div>

                    <div class="status-box">
                        <div class="status-icon">
                            <span class="material-icons-round">schedule</span>
                        </div>
                        <div class="status-text">
                            <div class="status-label">Timezone</div>
                            <div class="status-value">IST (UTC+5:30)</div>
                        </div>
                    </div>
                </div>
            </header>

            <div id="messageContainer"></div>

            <div class="history-container">
                <div class="quick-filters">
                    <div class="quick-filter" data-range="1h">Last 1 Hour</div>
                    <div class="quick-filter" data-range="6h">Last 6 Hours</div>
                    <div class="quick-filter" data-range="24h">Last 24 Hours</div>
                    <div class="quick-filter" data-range="7d">Last 7 Days</div>
                    <div class="quick-filter" data-range="30d">Last 30 Days</div>
                    <div class="quick-filter" data-range="custom">Custom Range</div>
                </div>

                <div class="filters">
                    <div class="filter-group">
                        <label for="startDate">Start Date & Time</label>
                        <input type="datetime-local" id="startDate" name="startDate">
                    </div>

                    <div class="filter-group">
                        <label for="endDate">End Date & Time</label>
                        <input type="datetime-local" id="endDate" name="endDate">
                    </div>

                    <div class="filter-group">
                        <label for="limit">Records Per Page</label>
                        <select id="limit" name="limit">
                            <option value="10">10</option>
                            <option value="25" selected>25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>

                    <div class="filter-actions">
                        <button id="filterBtn" class="button button-primary">
                            <span class="material-icons-round">filter_alt</span>
                            Apply Filters
                        </button>

                        <button id="exportBtn" class="button">
                            <span class="material-icons-round">file_download</span>
                            Export CSV
                        </button>
                    </div>
                </div>

                <div class="data-visualization">
                    <div class="visualization-controls">
                        <div class="visualization-title">Data Visualization</div>
                        <div class="visualization-actions">
                            <button class="action-button" id="lineChartBtn" title="Line Chart">
                                <span class="material-icons-round">show_chart</span>
                            </button>
                            <button class="action-button" id="barChartBtn" title="Bar Chart">
                                <span class="material-icons-round">bar_chart</span>
                            </button>
                            <button class="action-button" id="tableViewBtn" title="Table View">
                                <span class="material-icons-round">table_chart</span>
                            </button>
                        </div>
                    </div>
                    <canvas id="historyChart"></canvas>
                </div>

                <div class="data-table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Timestamp</th>
                                <th>V1N (V)</th>
                                <th>V2N (V)</th>
                                <th>V3N (V)</th>
                                <th>Ia (A)</th>
                                <th>Ib (A)</th>
                                <th>Ic (A)</th>
                                <th>PF1</th>
                                <th>PF2</th>
                                <th>PF3</th>
                                <th>Total kW</th>
                                <th>Frequency (Hz)</th>
                            </tr>
                        </thead>
                        <tbody id="dataRows">
                            <tr>
                                <td colspan="12" class="loading">Loading data...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="pagination">
                    <div class="page-info" id="pageInfo">Page 1 of 1</div>
                    <div class="pagination-controls">
                        <button id="prevBtn" class="button button-secondary" disabled>
                            <span class="material-icons-round">arrow_back</span>
                            Previous
                        </button>
                        <button id="nextBtn" class="button button-secondary" disabled>
                            Next
                            <span class="material-icons-round">arrow_forward</span>
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

</body>
</html>
