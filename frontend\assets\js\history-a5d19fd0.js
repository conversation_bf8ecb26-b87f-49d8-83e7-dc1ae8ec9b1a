import{D as p,u as s,C as H}from"./main-f3094cb4.js";let t={};const a={currentPage:1,totalPages:1,data:[],chartInstance:null,chartType:"line",activeParameter:"voltage_1",isVisualizationVisible:!0};function I(){t={startDate:document.getElementById("startDate"),endDate:document.getElementById("endDate"),limit:document.getElementById("limit"),filterBtn:document.getElementById("filterBtn"),exportBtn:document.getElementById("exportBtn"),dataRows:document.getElementById("dataRows"),prevBtn:document.getElementById("prevBtn"),nextBtn:document.getElementById("nextBtn"),pageInfo:document.getElementById("pageInfo"),messageContainer:document.getElementById("messageContainer"),historyChart:document.getElementById("historyChart"),lineChartBtn:document.getElementById("lineChartBtn"),barChartBtn:document.getElementById("barChartBtn"),tableViewBtn:document.getElementById("tableViewBtn"),quickFilters:document.querySelectorAll(".quick-filter")},v("1h"),D("1h"),$()}function $(){t.filterBtn&&t.filterBtn.addEventListener("click",y),t.exportBtn&&t.exportBtn.addEventListener("click",V),t.prevBtn&&t.prevBtn.addEventListener("click",()=>{a.currentPage>1&&(a.currentPage--,y())}),t.nextBtn&&t.nextBtn.addEventListener("click",()=>{a.currentPage<a.totalPages&&(a.currentPage++,y())}),t.lineChartBtn&&t.lineChartBtn.addEventListener("click",()=>{a.chartType="line",b()}),t.barChartBtn&&t.barChartBtn.addEventListener("click",()=>{a.chartType="bar",b()}),t.tableViewBtn&&t.tableViewBtn.addEventListener("click",()=>{const n=document.querySelector(".data-visualization");n&&(a.isVisualizationVisible=!a.isVisualizationVisible,a.isVisualizationVisible?(n.style.display="block",t.tableViewBtn.innerHTML='<span class="material-icons-round">table_chart</span>'):(n.style.display="none",t.tableViewBtn.innerHTML='<span class="material-icons-round">insert_chart</span>'))}),t.quickFilters&&t.quickFilters.forEach(n=>{n.addEventListener("click",()=>{const e=n.dataset.range;D(e),v(e)})})}function v(n){const e=p.now().setZone("Asia/Kolkata"),i=e;let o=e;switch(n){case"1h":o=e.minus({hours:1});break;case"6h":o=e.minus({hours:6});break;case"24h":o=e.minus({hours:24});break;case"7d":o=e.minus({days:7});break;case"30d":o=e.minus({days:30});break;case"custom":return;default:o=e.minus({hours:1})}t.startDate&&(t.startDate.value=o.toFormat("yyyy-MM-dd'T'HH:mm")),t.endDate&&(t.endDate.value=i.toFormat("yyyy-MM-dd'T'HH:mm")),y()}function D(n){t.quickFilters&&t.quickFilters.forEach(e=>{e.classList.remove("active"),e.dataset.range===n&&e.classList.add("active")})}async function y(){if(!(!t.startDate||!t.endDate||!t.dataRows))try{x(),t.dataRows.innerHTML='<tr><td colspan="12" class="loading">Loading data...</td></tr>';const n=t.startDate.value,e=t.endDate.value;if(!n||!e)return l("Please select both start and end dates","error"),null;const i=p.fromISO(n),o=p.fromISO(e),r=i.toFormat("yyyy-MM-dd HH:mm:ss"),c=o.toFormat("yyyy-MM-dd HH:mm:ss"),m=t.limit?t.limit.value:25,f=(a.currentPage-1)*parseInt(m),u=new Date().getTime(),E=`../backend/get_historical_data.php?start=${encodeURIComponent(r)}&end=${encodeURIComponent(c)}&limit=${m}&offset=${f}&_=${u}`,g=await fetch(E);if(!g.ok)throw new Error(`HTTP error! Status: ${g.status}`);const M=await g.text();let d;try{d=JSON.parse(M)}catch(h){throw new Error(`Failed to parse JSON response: ${h.message}`)}if(d.error)return l(`Error: ${d.error}`,"error"),null;if(a.data=Array.isArray(d.data)?d.data:[],a.totalPages=Math.ceil(d.total/parseInt(m)),a.data.length===0&&d.debug&&d.debug.test_query_result&&d.debug.test_query_result.length>0){const B=d.debug.test_query_result[0].timestamp;l(`
        <h3>No data found for the selected time range</h3>
        <p>There is data in the database, but not for the specific time range you selected.</p>
        <p>We found data at: <strong>${B}</strong></p>
        <button id="useSampleDateBtn" class="button button-primary" style="margin-top: 10px;">
          <span class="material-icons-round">schedule</span>
          View Data Around This Time
        </button>
      `,"warning"),document.getElementById("useSampleDateBtn").addEventListener("click",()=>{const C=p.fromSQL(B),T=C.minus({hours:1}),k=C.plus({hours:1});t.startDate.value=T.toFormat("yyyy-MM-dd'T'HH:mm"),t.endDate.value=k.toFormat("yyyy-MM-dd'T'HH:mm"),y()})}else a.data.length===0?l("No data found for the selected period","warning"):l(`Found ${a.data.length} records`,"success");return _(),L(),b(),d}catch(n){return console.error("Error fetching historical data:",n),l(`Error: ${n.message}`,"error"),null}}function _(){if(!t.dataRows)return;if(a.data.length===0){t.dataRows.innerHTML='<tr><td colspan="12" style="text-align: center;">No data available</td></tr>';return}let n="";a.data.forEach(e=>{const i=s.formatTimestamp(e.timestamp);n+=`<tr>
      <td>${i}</td>
      <td>${s.formatNumber(e.voltage_1)}</td>
      <td>${s.formatNumber(e.voltage_2)}</td>
      <td>${s.formatNumber(e.voltage_3)}</td>
      <td>${s.formatNumber(e.current_1)}</td>
      <td>${s.formatNumber(e.current_2)}</td>
      <td>${s.formatNumber(e.current_3)}</td>
      <td>${s.formatNumber(e.pf_1)}</td>
      <td>${s.formatNumber(e.pf_2)}</td>
      <td>${s.formatNumber(e.pf_3)}</td>
      <td>${s.formatNumber(e.total_kw)}</td>
      <td>${s.formatNumber(e.frequency)}</td>
    </tr>`}),t.dataRows.innerHTML=n}function L(){!t.pageInfo||!t.prevBtn||!t.nextBtn||(t.pageInfo.textContent=`Page ${a.currentPage} of ${a.totalPages||1}`,t.prevBtn.disabled=a.currentPage<=1,t.nextBtn.disabled=a.currentPage>=a.totalPages)}function b(){if(!t.historyChart||(a.chartInstance&&a.chartInstance.destroy(),a.data.length===0))return;const n=a.data.map(r=>p.fromSQL(r.timestamp).toJSDate()),e=(r,c,m)=>({label:c,data:a.data.map(f=>f[r]),borderColor:m,backgroundColor:m+"33",borderWidth:2,tension:.4,pointRadius:0,pointHoverRadius:6,pointBackgroundColor:"#0f172a",pointBorderWidth:2,pointBorderColor:m,shadowBlur:10,shadowColor:m});let i=[];(a.chartType==="line"||a.chartType==="bar")&&(i=[e("voltage_1","V1N (V)","#06b6d4"),e("voltage_2","V2N (V)","#3b82f6"),e("voltage_3","V3N (V)","#8b5cf6")]);const o=t.historyChart.getContext("2d");a.chartInstance=new H(o,{type:a.chartType,data:{labels:n,datasets:i},options:{responsive:!0,maintainAspectRatio:!1,plugins:{tooltip:{mode:"index",intersect:!1,backgroundColor:"rgba(15, 23, 42, 0.85)",titleColor:"#f8fafc",bodyColor:"#cbd5e1",borderColor:"rgba(148, 163, 184, 0.2)",borderWidth:1,padding:12,displayColors:!0,cornerRadius:8,callbacks:{label:r=>{let c=r.dataset.label||"";return c&&(c+=": "),r.parsed.y!==null&&(c+=r.parsed.y.toFixed(3)),c},labelTextColor:r=>r.dataset.borderColor}},legend:{position:"top",labels:{color:"#f8fafc",usePointStyle:!0,pointStyle:"rectRounded",padding:15,font:{family:"'JetBrains Mono', monospace",size:11}}}},scales:{x:{type:"time",time:{unit:"minute",displayFormats:{minute:"HH:mm"},tooltipFormat:"yyyy-MM-dd HH:mm:ss"},grid:{color:"rgba(148, 163, 184, 0.15)",lineWidth:.5},border:{display:!1},ticks:{color:"#cbd5e1",font:{family:"'JetBrains Mono', monospace",size:10},maxRotation:0}},y:{grid:{color:"rgba(148, 163, 184, 0.15)",lineWidth:.5},border:{display:!1},ticks:{color:"#cbd5e1",font:{family:"'JetBrains Mono', monospace",size:10},callback:function(r){return r.toFixed(2)}}}},animation:{duration:1e3,easing:"easeOutQuart"},elements:{line:{tension:.4},point:{radius:0,hoverRadius:6}},interaction:{mode:"index",intersect:!1}}})}async function V(){if(!(!t.startDate||!t.endDate||!t.exportBtn))try{l("Preparing CSV export of all data...","info");const n=t.exportBtn.innerHTML;t.exportBtn.disabled=!0,t.exportBtn.innerHTML='<span class="spinner"></span> Exporting...';const e=t.startDate.value,i=t.endDate.value;if(!e||!i){l("Please select both start and end dates","error"),t.exportBtn.disabled=!1,t.exportBtn.innerHTML=n;return}const o=p.fromISO(e),r=p.fromISO(i),c=o.toFormat("yyyy-MM-dd HH:mm:ss"),m=r.toFormat("yyyy-MM-dd HH:mm:ss"),f=`../backend/export_csv.php?parameter=all&start=${encodeURIComponent(c)}&end=${encodeURIComponent(m)}`;l("Downloading all data as CSV...","info");const u=document.createElement("a");u.style.display="none",u.href=f,u.download=`electrical_data_${p.now().toFormat("yyyy-MM-dd")}.csv`,document.body.appendChild(u),u.click(),setTimeout(()=>{document.body.removeChild(u),t.exportBtn.disabled=!1,t.exportBtn.innerHTML=n,l("CSV export initiated. If the file is large, it may take a moment to download.","success")},3e3)}catch(n){console.error("Error exporting CSV:",n),l(`Error exporting CSV: ${n.message}`,"error"),t.exportBtn.disabled=!1,t.exportBtn.innerHTML='<span class="material-icons-round">file_download</span> Export CSV'}}function l(n,e="info"){t.messageContainer&&(t.messageContainer.innerHTML=`
    <div class="message ${e}">
      ${n}
    </div>
  `,e==="success"&&setTimeout(()=>{t.messageContainer.querySelector(".message.success")&&x()},5e3))}function x(){t.messageContainer&&(t.messageContainer.innerHTML="")}document.addEventListener("DOMContentLoaded",I);
