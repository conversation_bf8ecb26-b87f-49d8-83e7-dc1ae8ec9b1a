// Import styles
import '../scss/main.scss';
import '../scss/reports.scss';

// Import utilities from main
import { utils } from './main';
import { DateTime } from 'luxon';

// DOM Elements
let elements = {};

// State
const state = {
    reportType: 'daily',
    reportFormat: 'pdf',
    currentPage: 1,
    totalPages: 1,
    reports: [],
    schedules: [],
    isGenerating: false
};

/**
 * Initialize the reports page
 */
function initReports() {
    // Get DOM elements
    elements = {
        reportType: document.getElementById('reportType'),
        startDate: document.getElementById('startDate'),
        endDate: document.getElementById('endDate'),
        timeRange: document.getElementById('timeRange'),
        reportFormat: document.getElementById('reportFormat'),
        generateReportBtn: document.getElementById('generateReportBtn'),
        addScheduleBtn: document.getElementById('addScheduleBtn'),
        templatesContainer: document.getElementById('templatesContainer'),
        schedulesContainer: document.getElementById('schedulesContainer'),
        reportsTableBody: document.getElementById('reportsTableBody'),
        analyticsContainer: document.getElementById('analyticsContainer'),
        prevBtn: document.getElementById('prevBtn'),
        nextBtn: document.getElementById('nextBtn'),
        pageInfo: document.getElementById('pageInfo'),
        messageContainer: document.getElementById('messageContainer'),
        reportModal: document.getElementById('reportModal'),
        closeModal: document.getElementById('closeModal'),
        progressFill: document.getElementById('progressFill'),
        progressText: document.getElementById('progressText')
    };

    // Set default date range
    setTimeRange('24h');

    // Bind events
    bindEvents();
    
    // Load initial data
    loadReportTemplates();
    loadScheduledReports();
    loadRecentReports();
    loadReportAnalytics();
}

/**
 * Bind event listeners
 */
function bindEvents() {
    // Time range selector
    if (elements.timeRange) {
        elements.timeRange.addEventListener('change', (e) => {
            if (e.target.value !== 'custom') {
                setTimeRange(e.target.value);
            }
        });
    }

    // Report type selector
    if (elements.reportType) {
        elements.reportType.addEventListener('change', (e) => {
            state.reportType = e.target.value;
        });
    }

    // Report format selector
    if (elements.reportFormat) {
        elements.reportFormat.addEventListener('change', (e) => {
            state.reportFormat = e.target.value;
        });
    }

    // Generate report button
    if (elements.generateReportBtn) {
        elements.generateReportBtn.addEventListener('click', generateReport);
    }

    // Add schedule button
    if (elements.addScheduleBtn) {
        elements.addScheduleBtn.addEventListener('click', addReportSchedule);
    }

    // Modal close
    if (elements.closeModal) {
        elements.closeModal.addEventListener('click', closeReportModal);
    }

    // Pagination
    if (elements.prevBtn) {
        elements.prevBtn.addEventListener('click', () => {
            if (state.currentPage > 1) {
                state.currentPage--;
                loadRecentReports();
            }
        });
    }

    if (elements.nextBtn) {
        elements.nextBtn.addEventListener('click', () => {
            if (state.currentPage < state.totalPages) {
                state.currentPage++;
                loadRecentReports();
            }
        });
    }
}

/**
 * Set time range based on quick filter
 */
function setTimeRange(range) {
    const now = DateTime.now().setZone('Asia/Kolkata');
    const endDate = now;
    let startDate = now;
    
    switch(range) {
        case '24h':
            startDate = now.minus({ hours: 24 });
            break;
        case '7d':
            startDate = now.minus({ days: 7 });
            break;
        case '30d':
            startDate = now.minus({ days: 30 });
            break;
        case '90d':
            startDate = now.minus({ days: 90 });
            break;
        case '1y':
            startDate = now.minus({ years: 1 });
            break;
        case 'custom':
            return;
        default:
            startDate = now.minus({ hours: 24 });
    }
    
    if (elements.startDate) {
        elements.startDate.value = startDate.toFormat("yyyy-MM-dd'T'HH:mm");
    }
    
    if (elements.endDate) {
        elements.endDate.value = endDate.toFormat("yyyy-MM-dd'T'HH:mm");
    }
}

/**
 * Generate report
 */
async function generateReport() {
    if (state.isGenerating) return;
    
    try {
        state.isGenerating = true;
        
        // Validate inputs
        const timeRange = getTimeRange();
        
        // Show modal
        showReportModal();
        
        // Prepare request data
        const requestData = {
            type: state.reportType,
            format: state.reportFormat,
            start: timeRange.start,
            end: timeRange.end
        };
        
        // Start generation
        updateProgress(0, 'Initializing report generation...');
        
        const response = await fetch('../backend/generate_report.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestData)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.error) {
            throw new Error(data.error);
        }
        
        // Simulate progress updates
        await simulateProgress(data.reportId);
        
        showMessage('Report generated successfully', 'success');
        loadRecentReports();
        
    } catch (error) {
        console.error('Error generating report:', error);
        showMessage(`Error: ${error.message}`, 'error');
    } finally {
        state.isGenerating = false;
        closeReportModal();
    }
}

/**
 * Get time range from form inputs
 */
function getTimeRange() {
    if (!elements.startDate || !elements.endDate) {
        const now = DateTime.now().setZone('Asia/Kolkata');
        return {
            start: now.minus({ hours: 24 }).toFormat('yyyy-MM-dd HH:mm:ss'),
            end: now.toFormat('yyyy-MM-dd HH:mm:ss')
        };
    }
    
    const startDate = elements.startDate.value;
    const endDate = elements.endDate.value;
    
    if (!startDate || !endDate) {
        throw new Error('Please select both start and end dates');
    }
    
    const startDateObj = DateTime.fromISO(startDate);
    const endDateObj = DateTime.fromISO(endDate);
    
    if (!startDateObj.isValid || !endDateObj.isValid) {
        throw new Error('Invalid date format');
    }
    
    if (startDateObj >= endDateObj) {
        throw new Error('Start date must be before end date');
    }
    
    return {
        start: startDateObj.toFormat('yyyy-MM-dd HH:mm:ss'),
        end: endDateObj.toFormat('yyyy-MM-dd HH:mm:ss')
    };
}

/**
 * Load report templates
 */
function loadReportTemplates() {
    if (!elements.templatesContainer) return;
    
    const templates = [
        {
            name: 'Daily Summary',
            description: 'Comprehensive daily electrical system summary',
            icon: 'today',
            type: 'daily'
        },
        {
            name: 'Power Quality Report',
            description: 'Detailed power quality analysis and compliance',
            icon: 'high_quality',
            type: 'power_quality'
        },
        {
            name: 'Energy Efficiency',
            description: 'Energy consumption and efficiency analysis',
            icon: 'energy_savings_leaf',
            type: 'energy_efficiency'
        },
        {
            name: 'Stability Analysis',
            description: 'System stability and predictive maintenance',
            icon: 'analytics',
            type: 'stability'
        },
        {
            name: 'Compliance Report',
            description: 'Regulatory compliance and standards verification',
            icon: 'verified',
            type: 'compliance'
        },
        {
            name: 'Custom Report',
            description: 'Build your own custom report template',
            icon: 'build',
            type: 'custom'
        }
    ];
    
    let html = '';
    templates.forEach(template => {
        html += `
            <div class="template-card" onclick="selectTemplate('${template.type}')">
                <div class="template-icon">
                    <span class="material-icons-round">${template.icon}</span>
                </div>
                <div class="template-content">
                    <h4>${template.name}</h4>
                    <p>${template.description}</p>
                </div>
                <div class="template-actions">
                    <button class="action-btn">
                        <span class="material-icons-round">play_arrow</span>
                    </button>
                </div>
            </div>
        `;
    });
    
    elements.templatesContainer.innerHTML = html;
}

/**
 * Load scheduled reports
 */
function loadScheduledReports() {
    if (!elements.schedulesContainer) return;
    
    // Sample scheduled reports
    const schedules = [
        {
            id: 1,
            name: 'Daily Power Quality',
            type: 'power_quality',
            frequency: 'Daily at 6:00 AM',
            format: 'PDF',
            status: 'Active',
            nextRun: '2025-01-21 06:00:00'
        },
        {
            id: 2,
            name: 'Weekly Summary',
            type: 'weekly',
            frequency: 'Weekly on Monday',
            format: 'Excel',
            status: 'Active',
            nextRun: '2025-01-27 08:00:00'
        }
    ];
    
    let html = '';
    schedules.forEach(schedule => {
        const nextRun = utils.formatTimestamp(schedule.nextRun);
        
        html += `
            <div class="schedule-item">
                <div class="schedule-info">
                    <h4>${schedule.name}</h4>
                    <div class="schedule-details">
                        <span class="schedule-frequency">${schedule.frequency}</span>
                        <span class="schedule-format">${schedule.format}</span>
                        <span class="schedule-status ${schedule.status.toLowerCase()}">${schedule.status}</span>
                    </div>
                    <div class="schedule-next">Next run: ${nextRun}</div>
                </div>
                <div class="schedule-actions">
                    <button class="action-btn" onclick="editSchedule(${schedule.id})">
                        <span class="material-icons-round">edit</span>
                    </button>
                    <button class="action-btn" onclick="runScheduleNow(${schedule.id})">
                        <span class="material-icons-round">play_arrow</span>
                    </button>
                    <button class="action-btn danger" onclick="deleteSchedule(${schedule.id})">
                        <span class="material-icons-round">delete</span>
                    </button>
                </div>
            </div>
        `;
    });
    
    elements.schedulesContainer.innerHTML = html;
}

/**
 * Load recent reports
 */
function loadRecentReports() {
    if (!elements.reportsTableBody) return;
    
    // Sample recent reports
    const reports = [
        {
            id: 1,
            generated: '2025-01-20 14:30:00',
            type: 'Daily Summary',
            period: '2025-01-19 to 2025-01-20',
            format: 'PDF',
            size: '2.4 MB',
            status: 'Completed'
        },
        {
            id: 2,
            generated: '2025-01-20 06:00:00',
            type: 'Power Quality',
            period: '2025-01-19 to 2025-01-20',
            format: 'Excel',
            size: '1.8 MB',
            status: 'Completed'
        },
        {
            id: 3,
            generated: '2025-01-19 18:45:00',
            type: 'Energy Efficiency',
            period: '2025-01-12 to 2025-01-19',
            format: 'PDF',
            size: '3.2 MB',
            status: 'Completed'
        }
    ];
    
    let html = '';
    reports.forEach(report => {
        const generated = utils.formatTimestamp(report.generated);
        
        html += `
            <tr>
                <td>${generated}</td>
                <td>${report.type}</td>
                <td>${report.period}</td>
                <td>${report.format}</td>
                <td>${report.size}</td>
                <td><span class="status-badge ${report.status.toLowerCase()}">${report.status}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="downloadReport(${report.id})">
                            <span class="material-icons-round">file_download</span>
                        </button>
                        <button class="action-btn" onclick="viewReport(${report.id})">
                            <span class="material-icons-round">visibility</span>
                        </button>
                        <button class="action-btn" onclick="shareReport(${report.id})">
                            <span class="material-icons-round">share</span>
                        </button>
                        <button class="action-btn danger" onclick="deleteReport(${report.id})">
                            <span class="material-icons-round">delete</span>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    elements.reportsTableBody.innerHTML = html;
}

/**
 * Load report analytics
 */
function loadReportAnalytics() {
    if (!elements.analyticsContainer) return;
    
    const analytics = [
        {
            title: 'Reports Generated',
            value: '156',
            change: '+12%',
            trend: 'up',
            icon: 'description'
        },
        {
            title: 'Total Downloads',
            value: '1,234',
            change: '+8%',
            trend: 'up',
            icon: 'file_download'
        },
        {
            title: 'Storage Used',
            value: '2.4 GB',
            change: '+15%',
            trend: 'up',
            icon: 'storage'
        },
        {
            title: 'Scheduled Reports',
            value: '8',
            change: '+2',
            trend: 'up',
            icon: 'schedule'
        }
    ];
    
    let html = '';
    analytics.forEach(item => {
        html += `
            <div class="analytics-card">
                <div class="analytics-icon">
                    <span class="material-icons-round">${item.icon}</span>
                </div>
                <div class="analytics-content">
                    <div class="analytics-value">${item.value}</div>
                    <div class="analytics-title">${item.title}</div>
                    <div class="analytics-change ${item.trend}">
                        <span class="material-icons-round">${item.trend === 'up' ? 'trending_up' : 'trending_down'}</span>
                        ${item.change}
                    </div>
                </div>
            </div>
        `;
    });
    
    elements.analyticsContainer.innerHTML = html;
}

/**
 * Show report generation modal
 */
function showReportModal() {
    if (elements.reportModal) {
        elements.reportModal.style.display = 'flex';
    }
}

/**
 * Close report generation modal
 */
function closeReportModal() {
    if (elements.reportModal) {
        elements.reportModal.style.display = 'none';
    }
}

/**
 * Update progress in modal
 */
function updateProgress(percentage, text) {
    if (elements.progressFill) {
        elements.progressFill.style.width = `${percentage}%`;
    }
    if (elements.progressText) {
        elements.progressText.textContent = text;
    }
}

/**
 * Simulate progress updates
 */
async function simulateProgress(reportId) {
    const steps = [
        { progress: 20, text: 'Collecting data...' },
        { progress: 40, text: 'Processing electrical parameters...' },
        { progress: 60, text: 'Generating charts and graphs...' },
        { progress: 80, text: 'Formatting report...' },
        { progress: 100, text: 'Report completed!' }
    ];
    
    for (const step of steps) {
        updateProgress(step.progress, step.text);
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
}

/**
 * Show message
 */
function showMessage(message, type = 'info') {
    if (elements.messageContainer) {
        utils.showMessage(message, type, elements.messageContainer);
    }
}

// Global functions for onclick handlers
window.selectTemplate = function(type) {
    if (elements.reportType) {
        elements.reportType.value = type;
        state.reportType = type;
    }
    showMessage(`Selected ${type} template`, 'info');
};

window.addReportSchedule = function() {
    showMessage('Add schedule feature coming soon', 'info');
};

window.editSchedule = function(scheduleId) {
    showMessage('Edit schedule feature coming soon', 'info');
};

window.runScheduleNow = function(scheduleId) {
    showMessage('Run schedule feature coming soon', 'info');
};

window.deleteSchedule = function(scheduleId) {
    showMessage('Delete schedule feature coming soon', 'info');
};

window.downloadReport = function(reportId) {
    showMessage('Download report feature coming soon', 'info');
};

window.viewReport = function(reportId) {
    showMessage('View report feature coming soon', 'info');
};

window.shareReport = function(reportId) {
    showMessage('Share report feature coming soon', 'info');
};

window.deleteReport = function(reportId) {
    showMessage('Delete report feature coming soon', 'info');
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initReports);

// Export for module usage
export { initReports };
