import{D as f,u as i,C as T}from"./main-1864cb92.js";let e={};const r={currentPage:1,totalPages:1,data:[],chartInstance:null,chartType:"line",activeParameter:"voltage",isVisualizationVisible:!0};function V(){e={startDate:document.getElementById("startDate"),endDate:document.getElementById("endDate"),limit:document.getElementById("limit"),filterBtn:document.getElementById("filterBtn"),exportBtn:document.getElementById("exportBtn"),dataRows:document.getElementById("dataRows"),prevBtn:document.getElementById("prevBtn"),nextBtn:document.getElementById("nextBtn"),pageInfo:document.getElementById("pageInfo"),messageContainer:document.getElementById("messageContainer"),historyChart:document.getElementById("historyChart"),lineChartBtn:document.getElementById("lineChartBtn"),barChartBtn:document.getElementById("barChartBtn"),tableViewBtn:document.getElementById("tableViewBtn"),quickFilters:document.querySelectorAll(".quick-filter"),parameterBtns:document.querySelectorAll(".parameter-btn")},C("1h"),_("1h"),M()}function M(){e.filterBtn&&e.filterBtn.addEventListener("click",y),e.exportBtn&&e.exportBtn.addEventListener("click",L),e.prevBtn&&e.prevBtn.addEventListener("click",()=>{r.currentPage>1&&(r.currentPage--,y())}),e.nextBtn&&e.nextBtn.addEventListener("click",()=>{r.currentPage<r.totalPages&&(r.currentPage++,y())}),e.lineChartBtn&&e.lineChartBtn.addEventListener("click",()=>{r.chartType="line",b()}),e.barChartBtn&&e.barChartBtn.addEventListener("click",()=>{r.chartType="bar",b()}),e.tableViewBtn&&e.tableViewBtn.addEventListener("click",()=>{const a=document.querySelector(".data-visualization");a&&(r.isVisualizationVisible=!r.isVisualizationVisible,r.isVisualizationVisible?(a.style.display="block",e.tableViewBtn.innerHTML='<span class="material-icons-round">table_chart</span>'):(a.style.display="none",e.tableViewBtn.innerHTML='<span class="material-icons-round">insert_chart</span>'))}),e.quickFilters&&e.quickFilters.forEach(a=>{a.addEventListener("click",()=>{const t=a.dataset.range;_(t),C(t)})}),e.parameterBtns&&e.parameterBtns.forEach(a=>{a.addEventListener("click",()=>{const t=a.dataset.parameter;H(t),r.activeParameter=t,b()})})}function C(a){const t=f.now().setZone("Asia/Kolkata"),n=t;let s=t;switch(a){case"1h":s=t.minus({hours:1});break;case"6h":s=t.minus({hours:6});break;case"24h":s=t.minus({hours:24});break;case"7d":s=t.minus({days:7});break;case"30d":s=t.minus({days:30});break;case"custom":return;default:s=t.minus({hours:1})}e.startDate&&(e.startDate.value=s.toFormat("yyyy-MM-dd'T'HH:mm")),e.endDate&&(e.endDate.value=n.toFormat("yyyy-MM-dd'T'HH:mm")),y()}function _(a){e.quickFilters&&e.quickFilters.forEach(t=>{t.classList.remove("active"),t.dataset.range===a&&t.classList.add("active")})}function H(a){e.parameterBtns&&e.parameterBtns.forEach(t=>{t.classList.remove("active"),t.dataset.parameter===a&&t.classList.add("active")})}async function y(){if(!(!e.startDate||!e.endDate||!e.dataRows))try{k(),e.dataRows.innerHTML='<tr><td colspan="12" class="loading">Loading data...</td></tr>';const a=e.startDate.value,t=e.endDate.value;if(!a||!t)return l("Please select both start and end dates","error"),null;const n=f.fromISO(a),s=f.fromISO(t),o=n.toFormat("yyyy-MM-dd HH:mm:ss"),c=s.toFormat("yyyy-MM-dd HH:mm:ss"),u=e.limit?e.limit.value:25,p=(r.currentPage-1)*parseInt(u),m=new Date().getTime(),D=`../backend/get_historical_data.php?start=${encodeURIComponent(o)}&end=${encodeURIComponent(c)}&limit=${u}&offset=${p}&_=${m}`,g=await fetch(D);if(!g.ok)throw new Error(`HTTP error! Status: ${g.status}`);const E=await g.text();let d;try{d=JSON.parse(E)}catch(h){throw new Error(`Failed to parse JSON response: ${h.message}`)}if(d.error)return l(`Error: ${d.error}`,"error"),null;if(r.data=Array.isArray(d.data)?d.data:[],r.totalPages=Math.ceil(d.total/parseInt(u)),r.data.length===0&&d.debug&&d.debug.test_query_result&&d.debug.test_query_result.length>0){const B=d.debug.test_query_result[0].timestamp;l(`
        <h3>No data found for the selected time range</h3>
        <p>There is data in the database, but not for the specific time range you selected.</p>
        <p>We found data at: <strong>${B}</strong></p>
        <button id="useSampleDateBtn" class="button button-primary" style="margin-top: 10px;">
          <span class="material-icons-round">schedule</span>
          View Data Around This Time
        </button>
      `,"warning"),document.getElementById("useSampleDateBtn").addEventListener("click",()=>{const v=f.fromSQL(B),x=v.minus({hours:1}),I=v.plus({hours:1});e.startDate.value=x.toFormat("yyyy-MM-dd'T'HH:mm"),e.endDate.value=I.toFormat("yyyy-MM-dd'T'HH:mm"),y()})}else r.data.length===0?l("No data found for the selected period","warning"):l(`Found ${r.data.length} records`,"success");return F(),P(),b(),d}catch(a){return console.error("Error fetching historical data:",a),l(`Error: ${a.message}`,"error"),null}}function F(){if(!e.dataRows)return;if(r.data.length===0){e.dataRows.innerHTML='<tr><td colspan="12" style="text-align: center;">No data available</td></tr>';return}let a="";r.data.forEach(t=>{const n=i.formatTimestamp(t.timestamp);a+=`<tr>
      <td>${n}</td>
      <td>${i.formatNumber(t.voltage_1)}</td>
      <td>${i.formatNumber(t.voltage_2)}</td>
      <td>${i.formatNumber(t.voltage_3)}</td>
      <td>${i.formatNumber(t.current_1)}</td>
      <td>${i.formatNumber(t.current_2)}</td>
      <td>${i.formatNumber(t.current_3)}</td>
      <td>${i.formatNumber(t.pf_1)}</td>
      <td>${i.formatNumber(t.pf_2)}</td>
      <td>${i.formatNumber(t.pf_3)}</td>
      <td>${i.formatNumber(t.total_kw)}</td>
      <td>${i.formatNumber(t.frequency)}</td>
    </tr>`}),e.dataRows.innerHTML=a}function P(){!e.pageInfo||!e.prevBtn||!e.nextBtn||(e.pageInfo.textContent=`Page ${r.currentPage} of ${r.totalPages||1}`,e.prevBtn.disabled=r.currentPage<=1,e.nextBtn.disabled=r.currentPage>=r.totalPages)}function b(){if(!e.historyChart||(r.chartInstance&&r.chartInstance.destroy(),r.data.length===0))return;const a=r.data.map(o=>f.fromSQL(o.timestamp).toJSDate()),t=(o,c,u)=>({label:c,data:r.data.map(p=>p[o]),borderColor:u,backgroundColor:u+"33",borderWidth:2,tension:.4,pointRadius:0,pointHoverRadius:6,pointBackgroundColor:"#0f172a",pointBorderWidth:2,pointBorderColor:u,shadowBlur:10,shadowColor:u});let n=[];if(r.chartType==="line"||r.chartType==="bar")switch(r.activeParameter){case"voltage":n=[t("voltage_1","V1N (V)","#06b6d4"),t("voltage_2","V2N (V)","#3b82f6"),t("voltage_3","V3N (V)","#8b5cf6")];break;case"current":n=[t("current_1","Ia (A)","#ef4444"),t("current_2","Ib (A)","#f97316"),t("current_3","Ic (A)","#eab308")];break;case"power":n=[t("total_kw","Total kW","#10b981")];break;case"pf":n=[t("pf_1","PF1","#8b5cf6"),t("pf_2","PF2","#a855f7"),t("pf_3","PF3","#c084fc")];break;case"frequency":n=[t("frequency","Frequency (Hz)","#06b6d4")];break;case"all":n=[t("voltage_1","V1N (V)","#06b6d4"),t("voltage_2","V2N (V)","#3b82f6"),t("voltage_3","V3N (V)","#8b5cf6"),t("current_1","Ia (A)","#ef4444"),t("current_2","Ib (A)","#f97316"),t("current_3","Ic (A)","#eab308"),t("total_kw","Total kW","#10b981"),t("pf_1","PF1","#8b5cf6"),t("pf_2","PF2","#a855f7"),t("pf_3","PF3","#c084fc"),t("frequency","Frequency (Hz)","#06b6d4")];break;default:n=[t("voltage_1","V1N (V)","#06b6d4"),t("voltage_2","V2N (V)","#3b82f6"),t("voltage_3","V3N (V)","#8b5cf6")]}const s=e.historyChart.getContext("2d");r.chartInstance=new T(s,{type:r.chartType,data:{labels:a,datasets:n},options:{responsive:!0,maintainAspectRatio:!1,plugins:{datalabels:{display:!1},tooltip:{mode:"index",intersect:!1,backgroundColor:"rgba(15, 23, 42, 0.85)",titleColor:"#f8fafc",bodyColor:"#cbd5e1",borderColor:"rgba(148, 163, 184, 0.2)",borderWidth:1,padding:12,displayColors:!0,cornerRadius:8,callbacks:{label:o=>{let c=o.dataset.label||"";return c&&(c+=": "),o.parsed.y!==null&&(c+=o.parsed.y.toFixed(3)),c},labelTextColor:o=>o.dataset.borderColor}},legend:{position:"top",labels:{color:"#f8fafc",usePointStyle:!0,pointStyle:"rectRounded",padding:15,font:{family:"'JetBrains Mono', monospace",size:11}}}},scales:{x:{type:"time",time:{unit:"minute",displayFormats:{minute:"HH:mm"},tooltipFormat:"yyyy-MM-dd HH:mm:ss"},grid:{color:"rgba(148, 163, 184, 0.15)",lineWidth:.5},border:{display:!1},ticks:{color:"#cbd5e1",font:{family:"'JetBrains Mono', monospace",size:10},maxRotation:0}},y:{grid:{color:"rgba(148, 163, 184, 0.15)",lineWidth:.5},border:{display:!1},ticks:{color:"#cbd5e1",font:{family:"'JetBrains Mono', monospace",size:10},callback:function(o){return o.toFixed(2)}}}},animation:{duration:1e3,easing:"easeOutQuart"},elements:{line:{tension:.4},point:{radius:0,hoverRadius:6}},interaction:{mode:"index",intersect:!1}}})}async function L(){if(!(!e.startDate||!e.endDate||!e.exportBtn))try{l("Preparing CSV export of all data...","info");const a=e.exportBtn.innerHTML;e.exportBtn.disabled=!0,e.exportBtn.innerHTML='<span class="spinner"></span> Exporting...';const t=e.startDate.value,n=e.endDate.value;if(!t||!n){l("Please select both start and end dates","error"),e.exportBtn.disabled=!1,e.exportBtn.innerHTML=a;return}const s=f.fromISO(t),o=f.fromISO(n),c=s.toFormat("yyyy-MM-dd HH:mm:ss"),u=o.toFormat("yyyy-MM-dd HH:mm:ss"),p=`../backend/export_csv.php?parameter=all&start=${encodeURIComponent(c)}&end=${encodeURIComponent(u)}`;l("Downloading all data as CSV...","info");const m=document.createElement("a");m.style.display="none",m.href=p,m.download=`electrical_data_${f.now().toFormat("yyyy-MM-dd")}.csv`,document.body.appendChild(m),m.click(),setTimeout(()=>{document.body.removeChild(m),e.exportBtn.disabled=!1,e.exportBtn.innerHTML=a,l("CSV export initiated. If the file is large, it may take a moment to download.","success")},3e3)}catch(a){console.error("Error exporting CSV:",a),l(`Error exporting CSV: ${a.message}`,"error"),e.exportBtn.disabled=!1,e.exportBtn.innerHTML='<span class="material-icons-round">file_download</span> Export CSV'}}function l(a,t="info"){e.messageContainer&&(e.messageContainer.innerHTML=`
    <div class="message ${t}">
      ${a}
    </div>
  `,t==="success"&&setTimeout(()=>{e.messageContainer.querySelector(".message.success")&&k()},5e3))}function k(){e.messageContainer&&(e.messageContainer.innerHTML="")}document.addEventListener("DOMContentLoaded",V);
