<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Professional Power Monitoring Dashboard</title>
    <!-- Chart.js and plugins -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luxon@3.0.1/build/global/luxon.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-luxon@1.2.0/dist/chartjs-adapter-luxon.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-crosshair@1.2.0/dist/chartjs-plugin-crosshair.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>

    <!-- Custom scripts -->
    <script src="../assets/js/chart-config.js?v=<?php echo time(); ?>"></script>

    <!-- Professional styling -->
    <link rel="stylesheet" href="../assets/css/professional.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1>
                <span class="material-icons" style="vertical-align: middle; margin-right: 8px;">electric_meter</span>
                3-Phase Online Data Logger system
            </h1>

            <div class="controls">
                <a href="history.php" class="button button-green">
                    <span class="material-icons" style="font-size: 18px; margin-right: 4px;">history</span>
                    Historical Data
                </a>

                <button id="autoRangeBtn" class="button">
                    <span class="material-icons" style="font-size: 18px; margin-right: 4px;">auto_fix_high</span>
                    Auto Range
                </button>
                <button id="seeOldGraphBtn" class="button button-blue">
                    <span class="material-icons" style="font-size: 18px; margin-right: 4px;">history</span>
                    See Old Graph
                </button>
            </div>


        </header>

        <div class="grid-container">
            <!-- Voltage Graphs -->
            <div class="graph-container">
                <div class="chart-header">
                    <div class="chart-title">Phase Voltages</div>
                    <div class="chart-controls">
                        <button class="chart-btn" onclick="scrollChartBackward(voltageChart, 30)"><span class="material-icons">arrow_back</span></button>
                        <button class="chart-btn" onclick="scrollChartForward(voltageChart, 30)"><span class="material-icons">arrow_forward</span></button>
                        <button class="chart-btn" id="voltagePause" onclick="togglePause('voltage')" data-paused="false"><span class="material-icons">pause</span></button>
                        <button class="chart-btn" id="voltageAutoScroll" onclick="toggleAutoScroll('voltage')" data-auto-scroll="true"><span class="material-icons">autorenew</span></button>
                    </div>
                </div>
                <div class="chart-wrapper" style="position: relative; height: calc(100% - 30px);">
                    <canvas id="voltageChart"></canvas>
                </div>
                <div class="instant-values" id="voltageValues"></div>
            </div>

            <!-- Current Graphs -->
            <div class="graph-container">
                <div class="chart-header">
                    <div class="chart-title">Phase Currents</div>
                    <div class="chart-controls">
                        <button class="chart-btn" onclick="scrollChartBackward(currentChart, 30)"><span class="material-icons">arrow_back</span></button>
                        <button class="chart-btn" onclick="scrollChartForward(currentChart, 30)"><span class="material-icons">arrow_forward</span></button>
                        <button class="chart-btn" id="currentPause" onclick="togglePause('current')" data-paused="false"><span class="material-icons">pause</span></button>
                        <button class="chart-btn" id="currentAutoScroll" onclick="toggleAutoScroll('current')" data-auto-scroll="true"><span class="material-icons">autorenew</span></button>
                    </div>
                </div>
                <div class="chart-wrapper" style="position: relative; height: calc(100% - 30px);">
                    <canvas id="currentChart"></canvas>
                </div>
                <div class="instant-values" id="currentValues"></div>
            </div>

            <!-- Power Factor Graphs (temporarily hidden) -->
            <div class="graph-container" style="display: none;">
                <div class="chart-header">
                    <div class="chart-title">Power Factors</div>
                    <div class="chart-controls">
                        <button class="chart-btn" onclick="scrollChartBackward(pfChart, 30)"><span class="material-icons">arrow_back</span></button>
                        <button class="chart-btn" onclick="scrollChartForward(pfChart, 30)"><span class="material-icons">arrow_forward</span></button>
                        <button class="chart-btn" id="pfPause" onclick="togglePause('pf')" data-paused="false"><span class="material-icons">pause</span></button>
                        <button class="chart-btn" id="pfAutoScroll" onclick="toggleAutoScroll('pf')" data-auto-scroll="true"><span class="material-icons">autorenew</span></button>
                    </div>
                </div>
                <div class="chart-wrapper" style="position: relative; height: calc(100% - 30px);">
                    <canvas id="pfChart"></canvas>
                </div>
                <div class="instant-values" id="pfValues"></div>
            </div>

            <!-- KVA Graphs -->
            <div class="graph-container">
                <div class="chart-header">
                    <div class="chart-title">KVA Values</div>
                    <div class="chart-controls">
                        <button class="chart-btn" onclick="scrollChartBackward(kvaChart, 30)"><span class="material-icons">arrow_back</span></button>
                        <button class="chart-btn" onclick="scrollChartForward(kvaChart, 30)"><span class="material-icons">arrow_forward</span></button>
                        <button class="chart-btn" id="kvaPause" onclick="togglePause('kva')" data-paused="false"><span class="material-icons">pause</span></button>
                        <button class="chart-btn" id="kvaAutoScroll" onclick="toggleAutoScroll('kva')" data-auto-scroll="true"><span class="material-icons">autorenew</span></button>
                    </div>
                </div>
                <div class="chart-wrapper" style="position: relative; height: calc(100% - 30px);">
                    <canvas id="kvaChart"></canvas>
                </div>
                <div class="instant-values" id="kvaValues"></div>
            </div>

            <!-- Total Power Graphs (temporarily hidden) -->
            <div class="graph-container" style="display: none;">
                <div class="chart-header">
                    <div class="chart-title">Total Power</div>
                    <div class="chart-controls">
                        <button class="chart-btn" onclick="scrollChartBackward(totalPowerChart, 30)"><span class="material-icons">arrow_back</span></button>
                        <button class="chart-btn" onclick="scrollChartForward(totalPowerChart, 30)"><span class="material-icons">arrow_forward</span></button>
                        <button class="chart-btn" id="totalPowerPause" onclick="togglePause('totalPower')" data-paused="false"><span class="material-icons">pause</span></button>
                        <button class="chart-btn" id="totalPowerAutoScroll" onclick="toggleAutoScroll('totalPower')" data-auto-scroll="true"><span class="material-icons">autorenew</span></button>
                    </div>
                </div>
                <div class="chart-wrapper" style="position: relative; height: calc(100% - 30px);">
                    <canvas id="totalPowerChart"></canvas>
                </div>
                <div class="instant-values" id="totalPowerValues"></div>
            </div>

            <!-- Frequency Graph -->
            <div class="graph-container">
                <div class="chart-header">
                    <div class="chart-title">Frequency</div>
                    <div class="chart-controls">
                        <button class="chart-btn" onclick="scrollChartBackward(frequencyChart, 30)"><span class="material-icons">arrow_back</span></button>
                        <button class="chart-btn" onclick="scrollChartForward(frequencyChart, 30)"><span class="material-icons">arrow_forward</span></button>
                        <button class="chart-btn" id="frequencyPause" onclick="togglePause('frequency')" data-paused="false"><span class="material-icons">pause</span></button>
                        <button class="chart-btn" id="frequencyAutoScroll" onclick="toggleAutoScroll('frequency')" data-auto-scroll="true"><span class="material-icons">autorenew</span></button>
                    </div>
                </div>
                <div class="chart-wrapper" style="position: relative; height: calc(100% - 30px);">
                    <canvas id="frequencyChart"></canvas>
                </div>
                <div class="instant-values" id="frequencyValue"></div>
            </div>
        </div>
    </div>

    <script>
        // Log parameter ranges directly to verify they're loaded correctly
        console.log('DIRECT CHECK - Parameter ranges loaded:');
        setTimeout(() => {
            console.log('PF range:', parameterRanges.pf);
            console.log('Frequency range:', parameterRanges.frequency);
        }, 1000); // Delay to ensure chart-config.js is loaded

        // Function to load data for a specific time range
        function loadTimeRangeData(hours) {
            // Pause all charts
            pauseAllCharts();

            // Calculate start and end times
            const endTime = new Date();
            const startTime = new Date(endTime.getTime() - (hours * 60 * 60 * 1000)); // hours in milliseconds

            // Update status
            updateStatus(`Loading data for the last ${hours} hour(s)...`, 'info');

            // Store the time range for all charts
            [voltageChart, currentChart, pfChart, kvaChart, totalPowerChart, frequencyChart].forEach(chart => {
                if (chart) {
                    chart._pausedRange = {
                        min: startTime,
                        max: endTime
                    };

                    // Hide data points (dots) for historical view
                    chart.data.datasets.forEach(dataset => {
                        dataset.pointRadius = 0;
                        dataset.pointHoverRadius = 0;
                    });
                }
            });

            // Load historical data for all charts
            Promise.all([
                fetchHistoricalDataForChart(voltageChart, 'voltage', startTime, endTime),
                fetchHistoricalDataForChart(currentChart, 'current', startTime, endTime),
                fetchHistoricalDataForChart(pfChart, 'pf', startTime, endTime),
                fetchHistoricalDataForChart(kvaChart, 'kva', startTime, endTime),
                fetchHistoricalDataForChart(totalPowerChart, 'totalPower', startTime, endTime),
                fetchHistoricalDataForChart(frequencyChart, 'frequency', startTime, endTime)
            ]).then(() => {
                // After loading data, explicitly set the time range for all charts
                [voltageChart, currentChart, pfChart, kvaChart, totalPowerChart, frequencyChart].forEach(chart => {
                    if (chart) {
                        // Set the time range
                        chart.options.scales.x.min = startTime;
                        chart.options.scales.x.max = endTime;

                        // Apply fixed ranges for PF and frequency charts
                        if (chart === pfChart) {
                            chart.options.scales.y.min = -1;
                            chart.options.scales.y.max = 1;
                        } else if (chart === frequencyChart) {
                            chart.options.scales.y.min = 30;
                            chart.options.scales.y.max = 60;
                        }

                        // Update the chart
                        chart.update('none');
                    }
                });

                updateStatus(`Data loaded for the last ${hours} hour(s)`, 'success');
            }).catch(error => {
                console.error('Error loading time range data:', error);
                updateStatus(`Error loading data: ${error.message}`, 'error');
            });
        }

        // Function to fetch historical data for a specific chart and time range
        async function fetchHistoricalDataForChart(chart, chartType, startTime, endTime) {
            try {
                // Format dates for API
                const startTimeStr = startTime.toISOString();
                const endTimeStr = endTime.toISOString();

                console.log(`Fetching data for ${chartType} from ${startTimeStr} to ${endTimeStr}`);

                // Show loading indicator
                updateStatus(`Loading data for ${chartType} chart...`, 'info');

                // Fetch historical data - use a reasonable limit to prevent browser crashes
                // We'll downsample the data in processHistoricalDataForChart if needed
                const response = await fetch(`../../app/backend/api/api/get_historical_data.php?start=${startTimeStr}&end=${endTimeStr}&limit=10000`);
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                const result = await response.json();
                console.log(`Received ${result.data ? result.data.length : 0} data points for ${chartType} chart`);

                if (!result.data || result.data.length === 0) {
                    console.warn(`No historical data available for ${chartType} in time range ${startTimeStr} to ${endTimeStr}`);
                    updateStatus('No historical data available for this time range', 'warning');
                    return false;
                }

                // Store the time range in the chart
                chart._timeRange = {
                    start: startTime,
                    end: endTime
                };

                // Update chart with fixed time range
                chart.options.scales.x.min = startTime;
                chart.options.scales.x.max = endTime;

                // Apply fixed ranges for PF and frequency charts
                if (chartType === 'pf') {
                    chart.options.scales.y.min = -1;
                    chart.options.scales.y.max = 1;
                } else if (chartType === 'frequency') {
                    chart.options.scales.y.min = 30;
                    chart.options.scales.y.max = 60;
                }

                // Process and add data to chart - this will handle downsampling for large datasets
                processHistoricalDataForChart(chart, chartType, result.data);

                // For non-PF/frequency charts, calculate min/max values from the data
                if (chartType !== 'pf' && chartType !== 'frequency') {
                    let dataMin = Infinity;
                    let dataMax = -Infinity;

                    chart.data.datasets.forEach(dataset => {
                        dataset.data.forEach(point => {
                            dataMin = Math.min(dataMin, point.y);
                            dataMax = Math.max(dataMax, point.y);
                        });
                    });

                    // Only update if we have valid min/max values
                    if (isFinite(dataMin) && isFinite(dataMax) && dataMin !== dataMax) {
                        // Add padding for better visualization
                        const padding = (dataMax - dataMin) * 0.05;
                        chart.options.scales.y.min = parseFloat((dataMin - padding).toFixed(3));
                        chart.options.scales.y.max = parseFloat((dataMax + padding).toFixed(3));
                    }
                }

                // Update the chart with animation: false for better performance
                chart.update({
                    duration: 0,
                    lazy: true,
                    easing: 'linear'
                });

                return true;
            } catch (error) {
                console.error(`Error fetching historical data for ${chartType}:`, error);
                updateStatus(`Error fetching data: ${error.message}`, 'error');
                return false;
            }
        }

        // Helper function to update instant values
        function updateInstantValues(data) {
            // Ensure values are properly parsed, defaulting to 0 for null/undefined/NaN
            const ensureValue = (value) => {
                const parsed = parseFloat(value);
                return isNaN(parsed) ? 0 : parsed;
            };

            // Update voltage values with 3 decimal places
            document.getElementById('voltageValues').innerHTML = `
                <div>V1N: <span>${ensureValue(data.voltage_1).toFixed(3)} V</span></div>
                <div>V2N: <span>${ensureValue(data.voltage_2).toFixed(3)} V</span></div>
                <div>V3N: <span>${ensureValue(data.voltage_3).toFixed(3)} V</span></div>
            `;

            // Update current values with 3 decimal places
            document.getElementById('currentValues').innerHTML = `
                <div>Ia: <span>${ensureValue(data.current_1).toFixed(3)} A</span></div>
                <div>Ib: <span>${ensureValue(data.current_2).toFixed(3)} A</span></div>
                <div>Ic: <span>${ensureValue(data.current_3).toFixed(3)} A</span></div>
            `;

            // Update power factor values with 3 decimal places
            document.getElementById('pfValues').innerHTML = `
                <div>PF1: <span>${ensureValue(data.pf_1).toFixed(3)}</span></div>
                <div>PF2: <span>${ensureValue(data.pf_2).toFixed(3)}</span></div>
                <div>PF3: <span>${ensureValue(data.pf_3).toFixed(3)}</span></div>
            `;

            // Update KVA values with 3 decimal places
            document.getElementById('kvaValues').innerHTML = `
                <div>KVA1: <span>${ensureValue(data.kva_1).toFixed(3)} kVA</span></div>
                <div>KVA2: <span>${ensureValue(data.kva_2).toFixed(3)} kVA</span></div>
                <div>KVA3: <span>${ensureValue(data.kva_3).toFixed(3)} kVA</span></div>
            `;

            // Update total power values with 3 decimal places
            document.getElementById('totalPowerValues').innerHTML = `
                <div>Total KW: <span>${ensureValue(data.total_kw).toFixed(3)} kW</span></div>
                <div>Total KVA: <span>${ensureValue(data.total_kva).toFixed(3)} kVA</span></div>
                <div>Total KVAR: <span>${ensureValue(data.total_kvar).toFixed(3)} kVAR</span></div>
            `;

            // Update frequency value with 3 decimal places
            document.getElementById('frequencyValue').innerHTML = `
                <div>Frequency: <span>${ensureValue(data.frequency).toFixed(3)} Hz</span></div>
            `;
        }

        // Function to process historical data for a specific chart
        function processHistoricalDataForChart(chart, chartType, data) {
            try {
                // Check if data is valid
                if (!data || !Array.isArray(data) || data.length === 0) {
                    console.warn(`No valid data provided for ${chartType} chart`);
                    return;
                }

                console.log(`Processing ${data.length} data points for ${chartType} chart`);

                // Data should already be sorted by the caller, but ensure it's sorted just in case
                // This is important to prevent glitches in the chart
                const sortedData = [...data].sort((a, b) => {
                    return new Date(a.timestamp) - new Date(b.timestamp);
                });

                // Helper function to ensure values are properly parsed
                const ensureValue = (value) => {
                    const parsed = parseFloat(value);
                    return isNaN(parsed) ? 0 : parsed;
                };

                // Clear existing data first to avoid duplicates
                chart.data.datasets.forEach(dataset => {
                    dataset.data = [];
                });

                // For large datasets, downsample to reduce lag
                let dataToProcess = sortedData;
                const MAX_POINTS = 1000; // Maximum number of points to display

                if (sortedData.length > MAX_POINTS) {
                    // Calculate sampling interval
                    const samplingInterval = Math.ceil(sortedData.length / MAX_POINTS);
                    console.log(`Downsampling data: ${sortedData.length} points -> ~${MAX_POINTS} points (interval: ${samplingInterval})`);

                    // Downsample the data
                    dataToProcess = [];
                    for (let i = 0; i < sortedData.length; i += samplingInterval) {
                        dataToProcess.push(sortedData[i]);
                    }

                    // Always include the last point for accurate current values
                    if (dataToProcess[dataToProcess.length - 1] !== sortedData[sortedData.length - 1]) {
                        dataToProcess.push(sortedData[sortedData.length - 1]);
                    }
                }

                console.log(`Processing ${dataToProcess.length} data points after downsampling for ${chartType} chart`);

                // Prepare data arrays for batch processing
                const datasets = {};
                chart.data.datasets.forEach((dataset, index) => {
                    datasets[index] = [];
                });

                // Process data based on chart type
                dataToProcess.forEach(item => {
                    try {
                        const timestamp = new Date(item.timestamp);
                        if (isNaN(timestamp.getTime())) {
                            return; // Skip invalid timestamps
                        }

                        // Add data directly to temporary arrays
                        switch(chartType) {
                            case 'voltage':
                                datasets[0].push({ x: timestamp, y: ensureValue(item.voltage_1) });
                                datasets[1].push({ x: timestamp, y: ensureValue(item.voltage_2) });
                                datasets[2].push({ x: timestamp, y: ensureValue(item.voltage_3) });
                                break;
                            case 'current':
                                datasets[0].push({ x: timestamp, y: ensureValue(item.current_1) });
                                datasets[1].push({ x: timestamp, y: ensureValue(item.current_2) });
                                datasets[2].push({ x: timestamp, y: ensureValue(item.current_3) });
                                break;
                            case 'pf':
                                datasets[0].push({ x: timestamp, y: ensureValue(item.pf_1) });
                                datasets[1].push({ x: timestamp, y: ensureValue(item.pf_2) });
                                datasets[2].push({ x: timestamp, y: ensureValue(item.pf_3) });
                                break;
                            case 'kva':
                                datasets[0].push({ x: timestamp, y: ensureValue(item.kva_1) });
                                datasets[1].push({ x: timestamp, y: ensureValue(item.kva_2) });
                                datasets[2].push({ x: timestamp, y: ensureValue(item.kva_3) });
                                break;
                            case 'totalPower':
                                datasets[0].push({ x: timestamp, y: ensureValue(item.total_kw) });
                                datasets[1].push({ x: timestamp, y: ensureValue(item.total_kva) });
                                datasets[2].push({ x: timestamp, y: ensureValue(item.total_kvar) });
                                break;
                            case 'frequency':
                                datasets[0].push({ x: timestamp, y: ensureValue(item.frequency) });
                                break;
                        }
                    } catch (itemError) {
                        console.warn(`Error processing data point for ${chartType}:`, itemError);
                        // Skip problematic items
                    }
                });

                // Verify data integrity before updating chart
                Object.keys(datasets).forEach(index => {
                    const dataArray = datasets[index];

                    // Check for duplicate timestamps and remove them
                    const uniqueData = [];
                    const seenTimestamps = new Set();

                    dataArray.forEach(point => {
                        const timeStr = point.x.getTime().toString();
                        if (!seenTimestamps.has(timeStr)) {
                            seenTimestamps.add(timeStr);
                            uniqueData.push(point);
                        }
                    });

                    // Sort again to ensure chronological order
                    uniqueData.sort((a, b) => a.x - b.x);

                    // Update the dataset with clean data
                    datasets[index] = uniqueData;
                });

                // Batch update the chart datasets
                Object.keys(datasets).forEach(index => {
                    chart.data.datasets[index].data = datasets[index];
                });

                // Count how many data points were actually added
                let totalPointsAdded = 0;
                chart.data.datasets.forEach(dataset => {
                    totalPointsAdded += dataset.data.length;
                });

                console.log(`Added ${totalPointsAdded} total points across all datasets for ${chartType} chart`);
            } catch (error) {
                console.error(`Error processing historical data for ${chartType} chart:`, error);
                updateStatus(`Error processing data: ${error.message}`, 'error');
            }
        }

        // Function to pause all charts
        function pauseAllCharts() {
            const charts = [
                { chart: voltageChart, id: 'voltagePause', type: 'voltage' },
                { chart: currentChart, id: 'currentPause', type: 'current' },
                { chart: pfChart, id: 'pfPause', type: 'pf' },
                { chart: kvaChart, id: 'kvaPause', type: 'kva' },
                { chart: totalPowerChart, id: 'totalPowerPause', type: 'totalPower' },
                { chart: frequencyChart, id: 'frequencyPause', type: 'frequency' }
            ];

            charts.forEach(item => {
                if (!item.chart._paused) {
                    // Set paused state
                    item.chart._paused = true;

                    // Update button appearance
                    const button = document.getElementById(item.id);
                    if (button) {
                        button.setAttribute('data-paused', 'true');
                        const icon = button.querySelector('.material-icons');
                        if (icon) {
                            icon.textContent = 'play_arrow';
                        }
                    }
                }
            });
        }

        // Function to fetch latest data for live view
        function fetchLatestData() {
            // Update status
            updateStatus('Switching to live data...', 'info');

            // Set up 1-minute window for all charts
            const endTime = new Date();
            const startTime = new Date(endTime.getTime() - 60000); // 1 minute ago

            // Set time range for all charts
            [voltageChart, currentChart, pfChart, kvaChart, totalPowerChart, frequencyChart].forEach(chart => {
                // Reset Y-axis scaling for dynamic updates
                if (chart === pfChart) {
                    chart.options.scales.y.min = -1;
                    chart.options.scales.y.max = 1;
                } else if (chart === frequencyChart) {
                    chart.options.scales.y.min = 30;
                    chart.options.scales.y.max = 60;
                } else {
                    chart.options.scales.y.min = undefined;
                    chart.options.scales.y.max = undefined;
                }

                // Set time window to last minute
                chart.options.scales.x.min = startTime;
                chart.options.scales.x.max = endTime;

                // Restore data points (dots) for live view
                chart.data.datasets.forEach(dataset => {
                    dataset.pointRadius = 1;
                    dataset.pointHoverRadius = 4;
                });

                chart.update('none');
            });

            // Fetch latest data immediately
            fetchDataAndUpdateCharts();

            updateStatus('Switched to live data mode', 'success');
        }

        // Function to resume all charts (for live data)
        function resumeAllCharts() {
            const charts = [
                { chart: voltageChart, id: 'voltagePause', type: 'voltage' },
                { chart: currentChart, id: 'currentPause', type: 'current' },
                { chart: pfChart, id: 'pfPause', type: 'pf' },
                { chart: kvaChart, id: 'kvaPause', type: 'kva' },
                { chart: totalPowerChart, id: 'totalPowerPause', type: 'totalPower' },
                { chart: frequencyChart, id: 'frequencyPause', type: 'frequency' }
            ];

            charts.forEach(item => {
                if (item.chart._paused) {
                    // Set paused state
                    item.chart._paused = false;

                    // Update button appearance
                    const button = document.getElementById(item.id);
                    if (button) {
                        button.setAttribute('data-paused', 'false');
                        const icon = button.querySelector('.material-icons');
                        if (icon) {
                            icon.textContent = 'pause';
                        }
                    }

                    // Enable auto-scroll
                    item.chart._autoScroll = true;
                    const autoScrollButton = document.getElementById(item.type + 'AutoScroll');
                    if (autoScrollButton) {
                        autoScrollButton.setAttribute('data-auto-scroll', 'true');
                    }
                }
            });

            // Fetch latest data
            fetchLatestData();
        }

        // Wait for the DOM to be fully loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing professional charts...');

            // Create status indicator
            const statusIndicator = document.createElement('div');
            statusIndicator.id = 'statusIndicator';
            statusIndicator.textContent = 'Initializing...';
            document.body.appendChild(statusIndicator);

            // Global function to update status indicator - only shows errors and warnings
            window.updateStatus = function(message, type = 'info') {
                const indicator = document.getElementById('statusIndicator');
                if (!indicator) return;

                // Skip data fetching and update messages
                if (message.includes('Fetching') || message.includes('updated') ||
                    message.includes('Loading') || message.includes('Loaded') ||
                    type === 'success' || type === 'info') {
                    indicator.style.display = 'none';
                    return;
                }

                indicator.textContent = message;

                // Set color based on type
                switch (type) {
                    case 'error':
                        indicator.style.backgroundColor = '#c5221f';
                        break;
                    case 'warning':
                        indicator.style.backgroundColor = '#ea8600';
                        break;
                    default:
                        indicator.style.backgroundColor = '#1a73e8';
                }

                indicator.style.opacity = '1';
                indicator.style.display = 'block';

                // Auto-hide after 5 seconds
                setTimeout(() => {
                    indicator.style.opacity = '0';
                    setTimeout(() => {
                        indicator.style.display = 'none';
                    }, 500);
                }, 5000);
            };

            // Create professional charts with enhanced styling
            const chartOptions = {
                // Empty object for additional options
                // Tooltips are now enabled and configured in chart-config.js
            };

            window.voltageChart = createProfessionalChart('voltageChart', ['V12', 'V23', 'V31'], colorSchemes.professional.primary, 'Voltage (V)', chartOptions, 'voltage');
            window.currentChart = createProfessionalChart('currentChart', ['Ia', 'Ib', 'Ic'], colorSchemes.professional.primary, 'Current (A)', chartOptions, 'current');
            window.pfChart = createProfessionalChart('pfChart', ['PF1', 'PF2', 'PF3'], colorSchemes.professional.primary, 'Power Factor', chartOptions, 'pf');
            window.kvaChart = createProfessionalChart('kvaChart', ['KVA1', 'KVA2', 'KVA3'], colorSchemes.professional.primary, 'KVA', chartOptions, 'kva');
            window.totalPowerChart = createProfessionalChart('totalPowerChart', ['Total KW', 'Total KVA', 'Total KVAR'], colorSchemes.professional.primary, 'Power', chartOptions, 'totalPower');
            window.frequencyChart = createProfessionalChart('frequencyChart', ['Frequency'], [colorSchemes.professional.primary[0]], 'Frequency (Hz)', chartOptions, 'frequency');

            // Force the parameter ranges directly
            pfChart.options.scales.y.min = -0.3;
            pfChart.options.scales.y.max = 1;
            frequencyChart.options.scales.y.min = 30;
            frequencyChart.options.scales.y.max = 60;

            // Update the charts to apply the forced ranges
            pfChart.update('none');
            frequencyChart.update('none');

            console.log('Chart Y-axis ranges set:');
            console.log('PF Chart Y-axis min:', pfChart.options.scales.y.min, 'max:', pfChart.options.scales.y.max);
            console.log('Frequency Chart Y-axis min:', frequencyChart.options.scales.y.min, 'max:', frequencyChart.options.scales.y.max);

            // Override the updateProfessionalChart function to maintain the parameter ranges
            const originalUpdateProfessionalChart = window.updateProfessionalChart;
            window.updateProfessionalChart = function(chart, timestamp, values, autoScroll = true) {
                // Call the original function
                originalUpdateProfessionalChart(chart, timestamp, values, autoScroll);

                // Force the parameter ranges for specific charts
                if (chart === pfChart) {
                    chart.options.scales.y.min = -0.3;
                    chart.options.scales.y.max = 1;
                    chart.update('none');
                } else if (chart === frequencyChart) {
                    chart.options.scales.y.min = 30;
                    chart.options.scales.y.max = 60;
                    chart.update('none');
                }
            };

            // Override the scrollChartToTimeRange function to maintain the parameter ranges
            const originalScrollChartToTimeRange = window.scrollChartToTimeRange;
            window.scrollChartToTimeRange = function(chart, startTime, endTime, preserveYScale = true) {
                // Call the original function
                originalScrollChartToTimeRange(chart, startTime, endTime, preserveYScale);

                // Force the parameter ranges for specific charts
                if (chart === pfChart) {
                    chart.options.scales.y.min = -0.3;
                    chart.options.scales.y.max = 1;
                    chart.update('none');
                } else if (chart === frequencyChart) {
                    chart.options.scales.y.min = 30;
                    chart.options.scales.y.max = 60;
                    chart.update('none');
                }
            };

            console.log('FORCED Chart Y-axis ranges:');
            console.log('PF Chart Y-axis min:', pfChart.options.scales.y.min, 'max:', pfChart.options.scales.y.max);
            console.log('Frequency Chart Y-axis min:', frequencyChart.options.scales.y.min, 'max:', frequencyChart.options.scales.y.max);

            // Initialize auto-scroll and pause state for all charts
            voltageChart._autoScroll = true;
            currentChart._autoScroll = true;
            pfChart._autoScroll = true;
            kvaChart._autoScroll = true;
            totalPowerChart._autoScroll = true;
            frequencyChart._autoScroll = true;

            voltageChart._paused = false;
            currentChart._paused = false;
            pfChart._paused = false;
            kvaChart._paused = false;
            totalPowerChart._paused = false;
            frequencyChart._paused = false;

            // Function to toggle pause state for a specific chart
            window.togglePause = function(chartType) {
                let chart, button;

                switch(chartType) {
                    case 'voltage':
                        chart = voltageChart;
                        button = document.getElementById('voltagePause');
                        break;
                    case 'current':
                        chart = currentChart;
                        button = document.getElementById('currentPause');
                        break;
                    case 'pf':
                        chart = pfChart;
                        button = document.getElementById('pfPause');
                        break;
                    case 'kva':
                        chart = kvaChart;
                        button = document.getElementById('kvaPause');
                        break;
                    case 'totalPower':
                        chart = totalPowerChart;
                        button = document.getElementById('totalPowerPause');
                        break;
                    case 'frequency':
                        chart = frequencyChart;
                        button = document.getElementById('frequencyPause');
                        break;
                    default:
                        return;
                }

                // Toggle pause state
                chart._paused = !chart._paused;

                // Update button appearance immediately for better user feedback
                if (button) {
                    button.setAttribute('data-paused', chart._paused);
                    const icon = button.querySelector('.material-icons');
                    if (icon) {
                        icon.textContent = chart._paused ? 'play_arrow' : 'pause';
                    }
                }

                // Store current view range when pausing
                if (chart._paused) {
                    // Save the current visible range (X-axis)
                    chart._pausedRange = {
                        min: chart.options.scales.x.min ? new Date(chart.options.scales.x.min) : null,
                        max: chart.options.scales.x.max ? new Date(chart.options.scales.x.max) : null
                    };

                    // When pausing, automatically fetch all historical data for this parameter
                    updateStatus(`Pausing ${chartType} chart and fetching historical data...`, 'info');

                    // Fetch all historical data in the background
                    fetchAllHistoricalDataForChart(chart, chartType).then(() => {
                        // After fetching all data, calculate min/max values from the actual data
                        let dataMin = Infinity;
                        let dataMax = -Infinity;

                        // Only consider data points within the visible time range
                        const minTime = chart._pausedRange.min ? chart._pausedRange.min.getTime() : 0;
                        const maxTime = chart._pausedRange.max ? chart._pausedRange.max.getTime() : Infinity;

                        // Loop through all datasets and find min/max values
                        chart.data.datasets.forEach(dataset => {
                            dataset.data.forEach(point => {
                                const pointTime = new Date(point.x).getTime();
                                if (pointTime >= minTime && pointTime <= maxTime) {
                                    dataMin = Math.min(dataMin, point.y);
                                    dataMax = Math.max(dataMax, point.y);
                                }
                            });
                        });

                        // Add a small padding (5%) to the min/max values for better visualization
                        const padding = (dataMax - dataMin) * 0.05;

                        // Format and store the calculated min/max values with 3 decimal places
                        chart._dataMin = parseFloat((dataMin - padding).toFixed(3));
                        chart._dataMax = parseFloat((dataMax + padding).toFixed(3));

                        // Apply the calculated min/max values to the chart
                        // For PF and frequency charts, always use fixed ranges
                        if (chartType === 'pf') {
                            chart.options.scales.y.min = -0.3;
                            chart.options.scales.y.max = 1;
                        } else if (chartType === 'frequency') {
                            chart.options.scales.y.min = 30;
                            chart.options.scales.y.max = 60;
                        } else {
                            chart.options.scales.y.min = chart._dataMin;
                            chart.options.scales.y.max = chart._dataMax;
                        }

                        chart.update('none');
                        updateStatus(`Historical data loaded for ${chartType} chart`, 'success');
                    }).catch(error => {
                        console.error('Error fetching historical data:', error);
                        updateStatus(`Error fetching historical data: ${error.message}`, 'error');
                    });
                } else if (chart._pausedRange) {
                    // Restore the saved X-axis range when unpausing
                    if (chart._pausedRange.min && chart._pausedRange.max) {
                        chart.options.scales.x.min = chart._pausedRange.min;
                        chart.options.scales.x.max = chart._pausedRange.max;
                    }

                    // When unpausing, restore dynamic Y-axis scaling
                    if (chartType === 'pf') {
                        chart.options.scales.y.min = -0.3;
                        chart.options.scales.y.max = 1;
                    } else if (chartType === 'frequency') {
                        chart.options.scales.y.min = 30;
                        chart.options.scales.y.max = 60;
                    } else {
                        chart.options.scales.y.min = undefined;
                        chart.options.scales.y.max = undefined;
                    }

                    chart.update('none');
                    updateStatus(`Resumed ${chartType} chart`, 'success');
                }
            };

            // Function to toggle auto-scrolling for a specific chart
            window.toggleAutoScroll = function(chartType) {
                let chart, button;

                switch(chartType) {
                    case 'voltage':
                        chart = voltageChart;
                        button = document.getElementById('voltageAutoScroll');
                        break;
                    case 'current':
                        chart = currentChart;
                        button = document.getElementById('currentAutoScroll');
                        break;
                    case 'pf':
                        chart = pfChart;
                        button = document.getElementById('pfAutoScroll');
                        break;
                    case 'kva':
                        chart = kvaChart;
                        button = document.getElementById('kvaAutoScroll');
                        break;
                    case 'totalPower':
                        chart = totalPowerChart;
                        button = document.getElementById('totalPowerAutoScroll');
                        break;
                    case 'frequency':
                        chart = frequencyChart;
                        button = document.getElementById('frequencyAutoScroll');
                        break;
                    default:
                        return;
                }

                // Toggle auto-scroll state
                chart._autoScroll = !chart._autoScroll;

                // Update button appearance
                if (button) {
                    button.setAttribute('data-auto-scroll', chart._autoScroll);
                }

                // If auto-scroll is enabled and chart is not paused, immediately scroll to show the latest data
                if (chart._autoScroll && !chart._paused && chart.data.datasets[0].data.length > 0) {
                    const latestPoint = chart.data.datasets[0].data[chart.data.datasets[0].data.length - 1];
                    const latestTime = new Date(latestPoint.x);
                    const oneMinuteAgo = new Date(latestTime.getTime() - 60000); // 60 seconds (1 minute)

                    // Update time range with fixed Y-axis scale
                    scrollChartToTimeRange(chart, oneMinuteAgo, latestTime, true);

                    // If chart is paused, update the saved range but don't actually scroll
                    if (chart._paused && chart._pausedRange) {
                        chart._pausedRange.min = oneMinuteAgo;
                        chart._pausedRange.max = latestTime;
                    }
                }
            };

            // Function to show loading spinners - removed

            // Function to fetch all historical data for a specific chart
            async function fetchAllHistoricalDataForChart(chart, chartType) {
                try {
                    // Fetch all available historical data - use a very large limit to get ALL data points
                    const response = await fetch('../../app/backend/api/api/get_historical_data.php?limit=100000');
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }

                    const result = await response.json();
                    console.log(`Historical data for ${chartType} chart:`, result);

                    if (!result.data || result.data.length === 0) {
                        updateStatus('No historical data available', 'warning');
                        return false;
                    }

                    // Sort data by timestamp (oldest first)
                    const sortedData = result.data.sort((a, b) => {
                        return new Date(a.timestamp) - new Date(b.timestamp);
                    });

                    // Helper function to ensure values are properly parsed
                    const ensureValue = (value) => {
                        const parsed = parseFloat(value);
                        return isNaN(parsed) ? 0 : parsed;
                    };

                    // Keep existing data points to avoid duplicates
                    const existingTimestamps = new Set();
                    chart.data.datasets.forEach(dataset => {
                        dataset.data.forEach(point => {
                            existingTimestamps.add(new Date(point.x).getTime());
                        });
                    });

                    // Add historical data points based on chart type
                    let newPointsAdded = 0;

                    sortedData.forEach(item => {
                        const timestamp = new Date(item.timestamp);
                        const timestampMs = timestamp.getTime();

                        // Skip if this timestamp already exists in the chart
                        if (existingTimestamps.has(timestampMs)) {
                            return;
                        }

                        // Add data based on chart type
                        switch(chartType) {
                            case 'voltage':
                                updateProfessionalChart(chart, timestamp, [
                                    ensureValue(item.voltage_1),
                                    ensureValue(item.voltage_2),
                                    ensureValue(item.voltage_3)
                                ], false);
                                break;
                            case 'current':
                                updateProfessionalChart(chart, timestamp, [
                                    ensureValue(item.current_1),
                                    ensureValue(item.current_2),
                                    ensureValue(item.current_3)
                                ], false);
                                break;
                            case 'pf':
                                updateProfessionalChart(chart, timestamp, [
                                    ensureValue(item.pf_1),
                                    ensureValue(item.pf_2),
                                    ensureValue(item.pf_3)
                                ], false);
                                break;
                            case 'kva':
                                updateProfessionalChart(chart, timestamp, [
                                    ensureValue(item.kva_1),
                                    ensureValue(item.kva_2),
                                    ensureValue(item.kva_3)
                                ], false);
                                break;
                            case 'totalPower':
                                updateProfessionalChart(chart, timestamp, [
                                    ensureValue(item.total_kw),
                                    ensureValue(item.total_kva),
                                    ensureValue(item.total_kvar)
                                ], false);
                                break;
                            case 'frequency':
                                updateProfessionalChart(chart, timestamp, [
                                    ensureValue(item.frequency)
                                ], false);
                                break;
                        }

                        newPointsAdded++;
                    });

                    console.log(`Added ${newPointsAdded} new data points to ${chartType} chart`);
                    return true;
                } catch (error) {
                    console.error(`Error fetching historical data for ${chartType} chart:`, error);
                    throw error;
                }
            }

            // Function to fetch data and update charts
            async function fetchDataAndUpdateCharts() {
                try {
                    console.log('Fetching data...');
                    // Don't show status update to avoid distracting the user
                    // updateStatus('Fetching latest data...', 'info');

                    // Use a cache-busting parameter to prevent caching
                    const timestamp = new Date().getTime();
                    // Request 20 most recent records for more detailed behavior and smoother updates
                    const response = await fetch(`../../app/backend/api/api/get_latest_data.php?_=${timestamp}&records=20`);

                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }

                    let data;
                    try {
                        const text = await response.text();
                        data = JSON.parse(text);
                    } catch (parseError) {
                        throw new Error(`Failed to parse JSON: ${parseError.message}`);
                    }

                    if (!data) {
                        throw new Error('No data received from server');
                    }

                    // Store current view ranges for each chart before updating
                    const chartRanges = {};
                    [voltageChart, currentChart, pfChart, kvaChart, totalPowerChart, frequencyChart].forEach(chart => {
                        if (chart && chart.options && chart.options.scales && chart.options.scales.x) {
                            chartRanges[chart.id] = {
                                xMin: chart.options.scales.x.min,
                                xMax: chart.options.scales.x.max,
                                yMin: chart.options.scales.y.min,
                                yMax: chart.options.scales.y.max,
                                paused: chart._paused,
                                autoScroll: chart._autoScroll
                            };
                        }
                    });

                    // Helper function to ensure values are properly parsed
                    const ensureValue = (value) => {
                        const parsed = parseFloat(value);
                        return isNaN(parsed) ? 0 : parsed;
                    };

                    // Check if we received an array of records or just a single record
                    const dataArray = Array.isArray(data) ? data : [data];

                    // Sort data by timestamp (oldest first)
                    const sortedData = dataArray.sort((a, b) => {
                        return new Date(a.timestamp) - new Date(b.timestamp);
                    });

                    // Process each record in order (oldest first)
                    // This ensures proper chronological order in the charts
                    for (let i = 0; i < sortedData.length; i++) {
                        const record = sortedData[i];

                        // Create timestamp
                        let dataTimestamp;
                        try {
                            dataTimestamp = new Date(record.timestamp);
                            if (isNaN(dataTimestamp.getTime())) {
                                // If timestamp is invalid, use current time with offset
                                console.warn('Invalid timestamp received:', record.timestamp);
                                dataTimestamp = new Date(Date.now() - ((sortedData.length - i) * 1000)); // Offset by position
                            }
                        } catch (e) {
                            console.warn('Error parsing timestamp:', e);
                            dataTimestamp = new Date(Date.now() - ((sortedData.length - i) * 1000)); // Offset by position
                        }

                        // Update charts with new data, respecting auto-scroll and pause settings
                        if (!voltageChart._paused) {
                            updateProfessionalChart(voltageChart, dataTimestamp, [
                                ensureValue(record.voltage_1),
                                ensureValue(record.voltage_2),
                                ensureValue(record.voltage_3)
                            ], voltageChart._autoScroll);
                        }

                        if (!currentChart._paused) {
                            updateProfessionalChart(currentChart, dataTimestamp, [
                                ensureValue(record.current_1),
                                ensureValue(record.current_2),
                                ensureValue(record.current_3)
                            ], currentChart._autoScroll);
                        }

                        if (!pfChart._paused) {
                            updateProfessionalChart(pfChart, dataTimestamp, [
                                ensureValue(record.pf_1),
                                ensureValue(record.pf_2),
                                ensureValue(record.pf_3)
                            ], pfChart._autoScroll);
                        }

                        if (!kvaChart._paused) {
                            updateProfessionalChart(kvaChart, dataTimestamp, [
                                ensureValue(record.kva_1),
                                ensureValue(record.kva_2),
                                ensureValue(record.kva_3)
                            ], kvaChart._autoScroll);
                        }

                        if (!totalPowerChart._paused) {
                            updateProfessionalChart(totalPowerChart, dataTimestamp, [
                                ensureValue(record.total_kw),
                                ensureValue(record.total_kva),
                                ensureValue(record.total_kvar)
                            ], totalPowerChart._autoScroll);
                        }

                        if (!frequencyChart._paused) {
                            updateProfessionalChart(frequencyChart, dataTimestamp, [
                                ensureValue(record.frequency)
                            ], frequencyChart._autoScroll);
                        }
                    }

                    // Restore view ranges for charts that are not auto-scrolling
                    [voltageChart, currentChart, pfChart, kvaChart, totalPowerChart, frequencyChart].forEach(chart => {
                        if (chart && chartRanges[chart.id]) {
                            const range = chartRanges[chart.id];

                            // If chart is paused or auto-scroll is disabled, restore the previous view
                            if (range.paused || !range.autoScroll) {
                                chart.options.scales.x.min = range.xMin;
                                chart.options.scales.x.max = range.xMax;

                                // For PF and frequency charts, always use fixed ranges
                                if (chart === pfChart) {
                                    chart.options.scales.y.min = -0.3;
                                    chart.options.scales.y.max = 1;
                                } else if (chart === frequencyChart) {
                                    chart.options.scales.y.min = 30;
                                    chart.options.scales.y.max = 60;
                                } else if (!range.autoScroll) {
                                    // For other charts with auto-scroll disabled, restore Y range
                                    chart.options.scales.y.min = range.yMin;
                                    chart.options.scales.y.max = range.yMax;
                                }

                                chart.update('none');
                            }
                        }
                    });

                    // Update instant values with the most recent record
                    if (sortedData.length > 0) {
                        const latestRecord = sortedData[sortedData.length - 1]; // Last record is the most recent

                        // Update all instant values at once
                        updateInstantValues(latestRecord);
                    }

                    // Don't show status update to avoid distracting the user
                    // updateStatus('Data updated successfully', 'success');
                } catch (error) {
                    console.error('Error fetching data:', error);
                    updateStatus('Error fetching data: ' + error.message, 'error');
                }
            }

            // Function to load historical data from the database
            async function loadHistoricalData() {
                return new Promise(async (resolve, reject) => {
                    try {
                        updateStatus('Loading historical data...', 'info');

                        // Clear all chart data first to prevent glitches
                        [voltageChart, currentChart, pfChart, kvaChart, totalPowerChart, frequencyChart].forEach(chart => {
                            if (chart) {
                                chart.data.datasets.forEach(dataset => {
                                    dataset.data = [];
                                });
                                chart.update('none');
                            }
                        });

                        // Calculate start and end times (last 5 minutes for faster initial load)
                        const endTime = new Date();
                        const startTime = new Date(endTime.getTime() - (5 * 60 * 1000)); // 5 minutes in milliseconds

                        // Format dates for API
                        const startTimeStr = startTime.toISOString();
                        const endTimeStr = endTime.toISOString();

                        console.log(`Fetching initial historical data from ${startTimeStr} to ${endTimeStr}`);

                        // Fetch recent historical data with a higher limit for more detailed initial view
                        const response = await fetch(`../../app/backend/api/api/get_historical_data.php?start=${startTimeStr}&end=${endTimeStr}&limit=1000`);
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }

                        const result = await response.json();
                        console.log(`Received ${result.data ? result.data.length : 0} historical data points`);

                        if (!result.data || result.data.length === 0) {
                            updateStatus('No historical data available', 'warning');
                            resolve(false);
                            return;
                        }

                        // Sort data by timestamp (oldest first) before processing
                        const sortedData = result.data.sort((a, b) => {
                            return new Date(a.timestamp) - new Date(b.timestamp);
                        });

                        // Get the earliest and latest timestamps
                        const earliestTimestamp = new Date(sortedData[0].timestamp);
                        const latestTimestamp = new Date(sortedData[sortedData.length - 1].timestamp);

                        // Calculate the most recent minute for display
                        const oneMinuteAgo = new Date(latestTimestamp.getTime() - 60000); // 60 seconds (1 minute)

                        // Process data for each chart
                        await Promise.all([
                            processHistoricalDataForChart(voltageChart, 'voltage', sortedData),
                            processHistoricalDataForChart(currentChart, 'current', sortedData),
                            processHistoricalDataForChart(pfChart, 'pf', sortedData),
                            processHistoricalDataForChart(kvaChart, 'kva', sortedData),
                            processHistoricalDataForChart(totalPowerChart, 'totalPower', sortedData),
                            processHistoricalDataForChart(frequencyChart, 'frequency', sortedData)
                        ]);

                        // Update instant values with the most recent data
                        if (sortedData.length > 0) {
                            const latestData = sortedData[sortedData.length - 1];
                            updateInstantValues(latestData);
                        }

                        // Set the time range for all charts to show the most recent minute
                        [voltageChart, currentChart, pfChart, kvaChart, totalPowerChart, frequencyChart].forEach(chart => {
                            if (!chart) return;

                            // Show small data points for better visibility
                            chart.data.datasets.forEach(dataset => {
                                dataset.pointRadius = 1;
                                dataset.pointHoverRadius = 4;
                            });

                            // Calculate min/max values from visible data points (last minute)
                            let dataMin = Infinity;
                            let dataMax = -Infinity;
                            let hasData = false;

                            chart.data.datasets.forEach(dataset => {
                                dataset.data.forEach(point => {
                                    const pointTime = new Date(point.x).getTime();
                                    if (pointTime >= oneMinuteAgo.getTime() && pointTime <= latestTimestamp.getTime()) {
                                        dataMin = Math.min(dataMin, point.y);
                                        dataMax = Math.max(dataMax, point.y);
                                        hasData = true;
                                    }
                                });
                            });

                            // If no data in the last minute, use all data
                            if (!hasData || !isFinite(dataMin) || !isFinite(dataMax) || dataMin === dataMax) {
                                dataMin = Infinity;
                                dataMax = -Infinity;

                                chart.data.datasets.forEach(dataset => {
                                    dataset.data.forEach(point => {
                                        dataMin = Math.min(dataMin, point.y);
                                        dataMax = Math.max(dataMax, point.y);
                                    });
                                });

                                // If still no valid range, set defaults
                                if (!isFinite(dataMin) || !isFinite(dataMax) || dataMin === dataMax) {
                                    if (chart === voltageChart) {
                                        dataMin = 0;
                                        dataMax = 500;
                                    } else if (chart === currentChart) {
                                        dataMin = 0;
                                        dataMax = 100;
                                    } else if (chart === kvaChart) {
                                        dataMin = 0;
                                        dataMax = 50;
                                    } else if (chart === totalPowerChart) {
                                        dataMin = 0;
                                        dataMax = 100;
                                    } else {
                                        dataMin = 0;
                                        dataMax = 1;
                                    }
                                }
                            }

                            // Add padding for better visualization
                            const padding = (dataMax - dataMin) * 0.05;
                            chart._dataMin = parseFloat((dataMin - padding).toFixed(3));
                            chart._dataMax = parseFloat((dataMax + padding).toFixed(3));

                            // Apply fixed ranges for PF and frequency charts
                            if (chart === pfChart) {
                                chart.options.scales.y.min = -0.3;
                                chart.options.scales.y.max = 1;
                            } else if (chart === frequencyChart) {
                                chart.options.scales.y.min = 30;
                                chart.options.scales.y.max = 60;
                            } else {
                                // For other charts, use calculated min/max values
                                chart.options.scales.y.min = chart._dataMin;
                                chart.options.scales.y.max = chart._dataMax;
                            }

                            // Set the time range to show the most recent minute
                            chart.options.scales.x.min = oneMinuteAgo;
                            chart.options.scales.x.max = latestTimestamp;

                            // Save the range for paused charts
                            chart._pausedRange = {
                                min: oneMinuteAgo,
                                max: latestTimestamp
                            };

                            // Update the chart with no animation
                            chart.update('none');
                        });

                        updateStatus('Historical data loaded successfully', 'success');
                        resolve(true);
                    } catch (error) {
                        console.error('Error loading historical data:', error);
                        updateStatus('Error loading historical data: ' + error.message, 'error');
                        reject(error);
                    }
                });
            }

            // Note: updateInstantValues function moved outside of DOM ready event handler

            // Hour navigation functionality and loadHourlyData function removed

            // Navigate to historical data viewer
            document.querySelector('a[href="history.php"]').addEventListener('click', function(e) {
                e.preventDefault();
                window.location.href = 'history.php';
            });

            // Auto-range button - applies appropriate ranges based on paused state
            document.getElementById('autoRangeBtn').addEventListener('click', function() {
                // Apply appropriate ranges to all charts based on paused state
                [voltageChart, currentChart, pfChart, kvaChart, totalPowerChart, frequencyChart].forEach(chart => {
                    if (!chart) return;

                    // If chart is paused, recalculate min/max values from the visible data
                    if (chart._paused && chart._pausedRange) {
                        // Get the current visible time range
                        const minTime = chart._pausedRange.min ? chart._pausedRange.min.getTime() : 0;
                        const maxTime = chart._pausedRange.max ? chart._pausedRange.max.getTime() : Infinity;

                        // Recalculate min/max values from the actual data
                        let dataMin = Infinity;
                        let dataMax = -Infinity;

                        // Loop through all datasets and find min/max values
                        chart.data.datasets.forEach(dataset => {
                            dataset.data.forEach(point => {
                                const pointTime = new Date(point.x).getTime();
                                if (pointTime >= minTime && pointTime <= maxTime) {
                                    dataMin = Math.min(dataMin, point.y);
                                    dataMax = Math.max(dataMax, point.y);
                                }
                            });
                        });

                        // Add a small padding (5%) to the min/max values for better visualization
                        const padding = (dataMax - dataMin) * 0.05;

                        // Format and store the calculated min/max values with 3 decimal places
                        chart._dataMin = parseFloat((dataMin - padding).toFixed(3));
                        chart._dataMax = parseFloat((dataMax + padding).toFixed(3));

                        // Apply fixed ranges for PF and frequency charts
                        if (chart === pfChart) {
                            chart.options.scales.y.min = -0.3;
                            chart.options.scales.y.max = 1;
                        } else if (chart === frequencyChart) {
                            chart.options.scales.y.min = 30;
                            chart.options.scales.y.max = 60;
                        } else {
                            chart.options.scales.y.min = chart._dataMin;
                            chart.options.scales.y.max = chart._dataMax;
                        }

                        updateStatus('Data min/max ranges applied to paused charts', 'success');
                    } else {
                        // Apply fixed ranges for PF and frequency charts
                        if (chart === pfChart) {
                            chart.options.scales.y.min = -0.3;
                            chart.options.scales.y.max = 1;
                        } else if (chart === frequencyChart) {
                            chart.options.scales.y.min = 30;
                            chart.options.scales.y.max = 60;
                        } else {
                            // Otherwise use dynamic scaling
                            chart.options.scales.y.min = undefined;
                            chart.options.scales.y.max = undefined;
                        }
                        updateStatus('Dynamic auto-scaling applied to active charts', 'success');
                    }
                    chart.update();
                });
            });

            // See Old Graph button - redirects to the historical graph page
            document.getElementById('seeOldGraphBtn').addEventListener('click', function() {
                // Redirect to the old graphs page
                window.location.href = 'old_graphs.php';
            });



            // Show loading status
            updateStatus('Initializing charts...', 'info');

            // Initialize charts with empty data first
            [voltageChart, currentChart, pfChart, kvaChart, totalPowerChart, frequencyChart].forEach(chart => {
                if (chart) {
                    // Clear any existing data
                    chart.data.datasets.forEach(dataset => {
                        dataset.data = [];
                    });

                    // Apply fixed ranges for PF and frequency charts
                    if (chart === pfChart) {
                        chart.options.scales.y.min = -0.3;
                        chart.options.scales.y.max = 1;
                    } else if (chart === frequencyChart) {
                        chart.options.scales.y.min = 30;
                        chart.options.scales.y.max = 60;
                    }

                    // Update the chart
                    chart.update('none');
                }
            });

            // Load historical data immediately without delay
            // Load historical data instead of using dummy data
            loadHistoricalData().then(() => {
                // Fetch data immediately after historical data is loaded
                fetchDataAndUpdateCharts();

                // Set up more frequent data fetching (every 1 second for more responsive updates)
                setInterval(fetchDataAndUpdateCharts, 1000);
            });

            // Function to enforce parameter ranges
            function enforceParameterRanges() {
                // Force the parameter ranges for specific charts
                if (pfChart.options.scales.y.min !== -0.3 || pfChart.options.scales.y.max !== 1) {
                    pfChart.options.scales.y.min = -0.3;
                    pfChart.options.scales.y.max = 1;
                    pfChart.update('none');
                }

                if (frequencyChart.options.scales.y.min !== 30 || frequencyChart.options.scales.y.max !== 60) {
                    frequencyChart.options.scales.y.min = 30;
                    frequencyChart.options.scales.y.max = 60;
                    frequencyChart.update('none');
                }
            }

            // Set up periodic parameter range enforcement (every 10 seconds to reduce lag)
            setInterval(enforceParameterRanges, 10000);

            // Call it immediately to ensure ranges are set from the start
            enforceParameterRanges();

            // Time range buttons have been removed
        });
    </script>
</body>
</html>
