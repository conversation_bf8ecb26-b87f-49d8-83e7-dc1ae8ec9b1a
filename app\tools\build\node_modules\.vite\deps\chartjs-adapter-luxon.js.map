{"version": 3, "sources": ["../../chartjs-adapter-luxon/dist/chartjs-adapter-luxon.esm.js"], "sourcesContent": ["/*!\n * chartjs-adapter-luxon v1.3.1\n * https://www.chartjs.org\n * (c) 2023 chartjs-adapter-luxon Contributors\n * Released under the MIT license\n */\nimport { _adapters } from 'chart.js';\nimport { DateTime } from 'luxon';\n\nconst FORMATS = {\n  datetime: DateTime.DATETIME_MED_WITH_SECONDS,\n  millisecond: 'h:mm:ss.SSS a',\n  second: DateTime.TIME_WITH_SECONDS,\n  minute: DateTime.TIME_SIMPLE,\n  hour: {hour: 'numeric'},\n  day: {day: 'numeric', month: 'short'},\n  week: 'DD',\n  month: {month: 'short', year: 'numeric'},\n  quarter: \"'Q'q - yyyy\",\n  year: {year: 'numeric'}\n};\n\n_adapters._date.override({\n  _id: 'luxon', // DEBUG\n\n  /**\n   * @private\n   */\n  _create: function(time) {\n    return DateTime.fromMillis(time, this.options);\n  },\n\n  init(chartOptions) {\n    if (!this.options.locale) {\n      this.options.locale = chartOptions.locale;\n    }\n  },\n\n  formats: function() {\n    return FORMATS;\n  },\n\n  parse: function(value, format) {\n    const options = this.options;\n\n    const type = typeof value;\n    if (value === null || type === 'undefined') {\n      return null;\n    }\n\n    if (type === 'number') {\n      value = this._create(value);\n    } else if (type === 'string') {\n      if (typeof format === 'string') {\n        value = DateTime.fromFormat(value, format, options);\n      } else {\n        value = DateTime.fromISO(value, options);\n      }\n    } else if (value instanceof Date) {\n      value = DateTime.fromJSDate(value, options);\n    } else if (type === 'object' && !(value instanceof DateTime)) {\n      value = DateTime.fromObject(value, options);\n    }\n\n    return value.isValid ? value.valueOf() : null;\n  },\n\n  format: function(time, format) {\n    const datetime = this._create(time);\n    return typeof format === 'string'\n      ? datetime.toFormat(format)\n      : datetime.toLocaleString(format);\n  },\n\n  add: function(time, amount, unit) {\n    const args = {};\n    args[unit] = amount;\n    return this._create(time).plus(args).valueOf();\n  },\n\n  diff: function(max, min, unit) {\n    return this._create(max).diff(this._create(min)).as(unit).valueOf();\n  },\n\n  startOf: function(time, unit, weekday) {\n    if (unit === 'isoWeek') {\n      weekday = Math.trunc(Math.min(Math.max(0, weekday), 6));\n      const dateTime = this._create(time);\n      return dateTime.minus({days: (dateTime.weekday - weekday + 7) % 7}).startOf('day').valueOf();\n    }\n    return unit ? this._create(time).startOf(unit).valueOf() : time;\n  },\n\n  endOf: function(time, unit) {\n    return this._create(time).endOf(unit).valueOf();\n  }\n});\n"], "mappings": ";;;;;;;;;AASA,IAAM,UAAU;AAAA,EACd,UAAU,SAAS;AAAA,EACnB,aAAa;AAAA,EACb,QAAQ,SAAS;AAAA,EACjB,QAAQ,SAAS;AAAA,EACjB,MAAM,EAAC,MAAM,UAAS;AAAA,EACtB,KAAK,EAAC,KAAK,WAAW,OAAO,QAAO;AAAA,EACpC,MAAM;AAAA,EACN,OAAO,EAAC,OAAO,SAAS,MAAM,UAAS;AAAA,EACvC,SAAS;AAAA,EACT,MAAM,EAAC,MAAM,UAAS;AACxB;AAEA,SAAU,MAAM,SAAS;AAAA,EACvB,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,EAKL,SAAS,SAAS,MAAM;AACtB,WAAO,SAAS,WAAW,MAAM,KAAK,OAAO;AAAA,EAC/C;AAAA,EAEA,KAAK,cAAc;AACjB,QAAI,CAAC,KAAK,QAAQ,QAAQ;AACxB,WAAK,QAAQ,SAAS,aAAa;AAAA,IACrC;AAAA,EACF;AAAA,EAEA,SAAS,WAAW;AAClB,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,SAAS,OAAO,QAAQ;AAC7B,UAAM,UAAU,KAAK;AAErB,UAAM,OAAO,OAAO;AACpB,QAAI,UAAU,QAAQ,SAAS,aAAa;AAC1C,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,UAAU;AACrB,cAAQ,KAAK,QAAQ,KAAK;AAAA,IAC5B,WAAW,SAAS,UAAU;AAC5B,UAAI,OAAO,WAAW,UAAU;AAC9B,gBAAQ,SAAS,WAAW,OAAO,QAAQ,OAAO;AAAA,MACpD,OAAO;AACL,gBAAQ,SAAS,QAAQ,OAAO,OAAO;AAAA,MACzC;AAAA,IACF,WAAW,iBAAiB,MAAM;AAChC,cAAQ,SAAS,WAAW,OAAO,OAAO;AAAA,IAC5C,WAAW,SAAS,YAAY,EAAE,iBAAiB,WAAW;AAC5D,cAAQ,SAAS,WAAW,OAAO,OAAO;AAAA,IAC5C;AAEA,WAAO,MAAM,UAAU,MAAM,QAAQ,IAAI;AAAA,EAC3C;AAAA,EAEA,QAAQ,SAAS,MAAM,QAAQ;AAC7B,UAAM,WAAW,KAAK,QAAQ,IAAI;AAClC,WAAO,OAAO,WAAW,WACrB,SAAS,SAAS,MAAM,IACxB,SAAS,eAAe,MAAM;AAAA,EACpC;AAAA,EAEA,KAAK,SAAS,MAAM,QAAQ,MAAM;AAChC,UAAM,OAAO,CAAC;AACd,SAAK,IAAI,IAAI;AACb,WAAO,KAAK,QAAQ,IAAI,EAAE,KAAK,IAAI,EAAE,QAAQ;AAAA,EAC/C;AAAA,EAEA,MAAM,SAAS,KAAK,KAAK,MAAM;AAC7B,WAAO,KAAK,QAAQ,GAAG,EAAE,KAAK,KAAK,QAAQ,GAAG,CAAC,EAAE,GAAG,IAAI,EAAE,QAAQ;AAAA,EACpE;AAAA,EAEA,SAAS,SAAS,MAAM,MAAM,SAAS;AACrC,QAAI,SAAS,WAAW;AACtB,gBAAU,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,GAAG,CAAC,CAAC;AACtD,YAAM,WAAW,KAAK,QAAQ,IAAI;AAClC,aAAO,SAAS,MAAM,EAAC,OAAO,SAAS,UAAU,UAAU,KAAK,EAAC,CAAC,EAAE,QAAQ,KAAK,EAAE,QAAQ;AAAA,IAC7F;AACA,WAAO,OAAO,KAAK,QAAQ,IAAI,EAAE,QAAQ,IAAI,EAAE,QAAQ,IAAI;AAAA,EAC7D;AAAA,EAEA,OAAO,SAAS,MAAM,MAAM;AAC1B,WAAO,KAAK,QAAQ,IAAI,EAAE,MAAM,IAAI,EAAE,QAAQ;AAAA,EAChD;AACF,CAAC;", "names": []}