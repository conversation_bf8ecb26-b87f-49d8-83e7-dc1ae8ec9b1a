<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Historical Graphs - Power Monitoring Dashboard</title>
    
    <!-- Chart.js and plugins -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luxon@3.0.1/build/global/luxon.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-luxon@1.2.0/dist/chartjs-adapter-luxon.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-crosshair@1.2.0/dist/chartjs-plugin-crosshair.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>

    <!-- Custom scripts -->
    <script src="../assets/js/chart-config.js?v=<?php echo time(); ?>"></script>

    <!-- Professional styling -->
    <link rel="stylesheet" href="../assets/css/professional.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <style>
        .grid-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: repeat(3, 1fr);
            gap: 20px;
            height: calc(100vh - 180px);
            margin-top: 20px;
        }
        
        .filters {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
        }
        
        .filter-group label {
            font-size: 12px;
            margin-bottom: 5px;
            color: #5f6368;
        }
        
        .filter-group input, .filter-group select {
            padding: 8px 12px;
            border: 1px solid #dadce0;
            border-radius: 4px;
            font-size: 14px;
        }
        
        #messageContainer {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        
        .message-info {
            background-color: #e8f0fe;
            border-left: 4px solid #1a73e8;
        }
        
        .message-success {
            background-color: #e6f4ea;
            border-left: 4px solid #34a853;
        }
        
        .message-warning {
            background-color: #fef7e0;
            border-left: 4px solid #fbbc04;
        }
        
        .message-error {
            background-color: #fce8e6;
            border-left: 4px solid #ea4335;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>
                <span class="material-icons" style="vertical-align: middle; margin-right: 8px;">history</span>
                Historical Graphs
                <span style="font-size: 14px; color: #6c757d; font-weight: normal;">(Indian Standard Time - IST)</span>
            </h1>
            <div class="controls">
                <a href="pro_dashboard.php" class="button">
                    <span class="material-icons">dashboard</span>
                    Back to Dashboard
                </a>
                <a href="history.php" class="button" style="margin-left: 10px;">
                    <span class="material-icons">table_chart</span>
                    View Data Table
                </a>
            </div>
        </header>

        <div id="messageContainer"></div>

        <div class="filters">
            <div class="filter-group">
                <label for="startDate">Start Date</label>
                <input type="datetime-local" id="startDate" name="startDate">
            </div>

            <div class="filter-group">
                <label for="endDate">End Date</label>
                <input type="datetime-local" id="endDate" name="endDate">
            </div>

            <div class="filter-group">
                <label>&nbsp;</label>
                <button id="filterBtn" class="button">
                    <span class="material-icons">filter_alt</span>
                    Apply Filters
                </button>
            </div>

            <div class="filter-group">
                <label>&nbsp;</label>
                <button id="exportBtn" class="button button-green">
                    <span class="material-icons">file_download</span>
                    Export CSV
                </button>
            </div>
        </div>

        <div class="grid-container">
            <!-- Voltage Graphs -->
            <div class="graph-container">
                <div class="chart-header">
                    <div class="chart-title">Phase Voltages</div>
                </div>
                <div class="chart-wrapper" style="position: relative; height: calc(100% - 30px);">
                    <canvas id="voltageChart"></canvas>
                </div>
                <div class="instant-values" id="voltageValues"></div>
            </div>

            <!-- Current Graphs -->
            <div class="graph-container">
                <div class="chart-header">
                    <div class="chart-title">Phase Currents</div>
                </div>
                <div class="chart-wrapper" style="position: relative; height: calc(100% - 30px);">
                    <canvas id="currentChart"></canvas>
                </div>
                <div class="instant-values" id="currentValues"></div>
            </div>

            <!-- Power Factor Graphs -->
            <div class="graph-container">
                <div class="chart-header">
                    <div class="chart-title">Power Factors</div>
                </div>
                <div class="chart-wrapper" style="position: relative; height: calc(100% - 30px);">
                    <canvas id="pfChart"></canvas>
                </div>
                <div class="instant-values" id="pfValues"></div>
            </div>

            <!-- KVA Graphs -->
            <div class="graph-container">
                <div class="chart-header">
                    <div class="chart-title">KVA Values</div>
                </div>
                <div class="chart-wrapper" style="position: relative; height: calc(100% - 30px);">
                    <canvas id="kvaChart"></canvas>
                </div>
                <div class="instant-values" id="kvaValues"></div>
            </div>

            <!-- Total Power Graphs -->
            <div class="graph-container">
                <div class="chart-header">
                    <div class="chart-title">Total Power</div>
                </div>
                <div class="chart-wrapper" style="position: relative; height: calc(100% - 30px);">
                    <canvas id="totalPowerChart"></canvas>
                </div>
                <div class="instant-values" id="totalPowerValues"></div>
            </div>

            <!-- Frequency Graph -->
            <div class="graph-container">
                <div class="chart-header">
                    <div class="chart-title">Frequency</div>
                </div>
                <div class="chart-wrapper" style="position: relative; height: calc(100% - 30px);">
                    <canvas id="frequencyChart"></canvas>
                </div>
                <div class="instant-values" id="frequencyValue"></div>
            </div>
        </div>
    </div>

    <script>
        // DOM Elements
        const elements = {
            startDate: document.getElementById('startDate'),
            endDate: document.getElementById('endDate'),
            filterBtn: document.getElementById('filterBtn'),
            exportBtn: document.getElementById('exportBtn'),
            messageContainer: document.getElementById('messageContainer')
        };

        // State
        const state = {
            data: [],
            charts: {}
        };

        // Utility functions
        const utils = {
            // Show message with type (info, success, warning, error)
            showMessage: (message, type = 'info') => {
                const container = elements.messageContainer;
                container.innerHTML = message;
                container.className = '';
                container.classList.add(`message-${type}`);
                container.style.display = 'block';

                // Auto-hide success and info messages after 5 seconds
                if (type === 'success' || type === 'info') {
                    setTimeout(() => {
                        container.style.display = 'none';
                    }, 5000);
                }
            },

            // Format date for API
            formatDate: (date) => {
                return date.toISOString();
            },

            // Set current time as default
            setCurrentTime: () => {
                const now = new Date();
                const oneHourAgo = new Date(now.getTime() - (60 * 60 * 1000)); // 1 hour ago
                
                // Format for datetime-local input
                const formatForInput = (date) => {
                    return date.toISOString().slice(0, 16);
                };
                
                elements.startDate.value = formatForInput(oneHourAgo);
                elements.endDate.value = formatForInput(now);
            },

            // Ensure values are properly parsed
            ensureValue: (value) => {
                const parsed = parseFloat(value);
                return isNaN(parsed) ? 0 : parsed;
            }
        };

        // API functions
        const api = {
            // Fetch historical data based on filters
            fetchHistoricalData: async () => {
                try {
                    utils.showMessage('Fetching historical data...', 'info');
                    
                    // Get filter values
                    const startDate = new Date(elements.startDate.value);
                    const endDate = new Date(elements.endDate.value);
                    
                    // Validate dates
                    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
                        throw new Error('Please enter valid start and end dates');
                    }
                    
                    if (startDate >= endDate) {
                        throw new Error('Start date must be before end date');
                    }
                    
                    // Format dates for API
                    const startFormatted = utils.formatDate(startDate);
                    const endFormatted = utils.formatDate(endDate);
                    
                    // Add cache buster
                    const cacheBuster = new Date().getTime();
                    
                    // Build URL - use a large limit to get all data points
                    const url = `../../app/backend/api/api/get_historical_data.php?start=${encodeURIComponent(startFormatted)}&end=${encodeURIComponent(endFormatted)}&limit=100000&_=${cacheBuster}`;
                    
                    console.log('Fetching data with URL:', url);
                    
                    // Fetch data
                    const response = await fetch(url);
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    
                    // Parse response
                    const responseText = await response.text();
                    
                    let data;
                    try {
                        data = JSON.parse(responseText);
                    } catch (parseError) {
                        throw new Error(`Failed to parse JSON response: ${parseError.message}`);
                    }
                    
                    // Check for API errors
                    if (data.error) {
                        throw new Error(data.error);
                    }
                    
                    // Store data
                    state.data = data.data || [];
                    
                    // Check if we have data
                    if (state.data.length === 0) {
                        utils.showMessage('No data found for the selected period', 'warning');
                        return null;
                    }
                    
                    utils.showMessage(`Found ${state.data.length} records`, 'success');
                    
                    // Process data for charts
                    processDataForCharts();
                    
                    return data;
                } catch (error) {
                    console.error('Error fetching historical data:', error);
                    utils.showMessage(`Error: ${error.message}`, 'error');
                    return null;
                }
            },
            
            // Export data as CSV
            exportCSV: async () => {
                try {
                    utils.showMessage('Preparing CSV export...', 'info');
                    
                    // Get filter values
                    const startDate = new Date(elements.startDate.value);
                    const endDate = new Date(elements.endDate.value);
                    
                    // Validate dates
                    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
                        throw new Error('Please enter valid start and end dates');
                    }
                    
                    // Format dates for API
                    const startFormatted = utils.formatDate(startDate);
                    const endFormatted = utils.formatDate(endDate);
                    
                    // Build URL - use a very large limit to get ALL data
                    const url = `../../app/backend/api/api/get_historical_data.php?start=${encodeURIComponent(startFormatted)}&end=${encodeURIComponent(endFormatted)}&limit=1000000&format=csv`;
                    
                    // Redirect to CSV download
                    window.location.href = url;
                    
                    utils.showMessage('CSV export initiated', 'success');
                } catch (error) {
                    console.error('Error exporting CSV:', error);
                    utils.showMessage(`Error: ${error.message}`, 'error');
                }
            }
        };

        // Chart functions
        function createHistoricalChart(canvasId, labels, colorScheme, yAxisLabel, options = {}) {
            const ctx = document.getElementById(canvasId).getContext('2d');
            
            // Default options
            const defaultOptions = {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'minute',
                            displayFormats: {
                                second: 'HH:mm:ss',
                                minute: 'HH:mm',
                                hour: 'HH:mm',
                                day: 'MMM d'
                            },
                            tooltipFormat: 'MMM d, yyyy HH:mm:ss'
                        },
                        adapters: {
                            date: {
                                locale: 'en-IN',
                                zone: 'Asia/Kolkata' // Set timezone to India
                            }
                        },
                        title: {
                            display: false,
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.03)',
                            borderDash: [3, 3],
                            drawBorder: false,
                            tickLength: 10
                        },
                        ticks: {
                            font: {
                                size: 11
                            },
                            padding: 8,
                            maxRotation: 0,
                            autoSkip: true,
                            maxTicksLimit: 6
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: yAxisLabel,
                            font: {
                                size: 12,
                                weight: 'normal'
                            },
                            padding: {
                                top: 0,
                                bottom: 10
                            }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.03)',
                            borderDash: [3, 3],
                            drawBorder: false
                        },
                        ticks: {
                            font: {
                                size: 11
                            },
                            padding: 8,
                            callback: function(value) {
                                return value.toFixed(3);
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        align: 'start',
                        labels: {
                            boxWidth: 12,
                            padding: 15,
                            font: {
                                size: 11
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#202124',
                        bodyColor: '#5f6368',
                        borderColor: '#dadce0',
                        borderWidth: 0.5,
                        padding: 10,
                        cornerRadius: 4,
                        boxPadding: 4,
                        usePointStyle: true,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += context.parsed.y.toFixed(3);
                                return label;
                            },
                            title: function(context) {
                                // Format the timestamp in IST
                                const date = new Date(context[0].parsed.x);
                                return date.toLocaleTimeString('en-IN', {
                                    timeZone: 'Asia/Kolkata',
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    second: '2-digit',
                                    day: '2-digit',
                                    month: 'short'
                                });
                            }
                        }
                    }
                }
            };
            
            // Merge options
            const mergedOptions = { ...defaultOptions, ...options };
            
            // Create datasets array
            const datasets = labels.map((label, index) => {
                return {
                    label: label,
                    data: [],
                    borderColor: colorScheme[index % colorScheme.length],
                    backgroundColor: 'transparent',
                    borderWidth: 1.5,
                    tension: 0.2,
                    pointRadius: 0,
                    pointHoverRadius: 3
                };
            });
            
            // Create chart
            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    datasets: datasets
                },
                options: mergedOptions
            });
            
            return chart;
        }

        // Process data for charts
        function processDataForCharts() {
            if (!state.data || state.data.length === 0) {
                utils.showMessage('No data to display', 'warning');
                return;
            }
            
            // Sort data by timestamp (oldest first)
            const sortedData = state.data.sort((a, b) => {
                return new Date(a.timestamp) - new Date(b.timestamp);
            });
            
            // Clear existing data from charts
            Object.values(state.charts).forEach(chart => {
                chart.data.datasets.forEach(dataset => {
                    dataset.data = [];
                });
                chart.update();
            });
            
            // Process data for each chart
            sortedData.forEach(item => {
                const timestamp = new Date(item.timestamp);
                
                // Add data to voltage chart
                if (state.charts.voltage) {
                    state.charts.voltage.data.datasets[0].data.push({
                        x: timestamp,
                        y: utils.ensureValue(item.voltage_1)
                    });
                    state.charts.voltage.data.datasets[1].data.push({
                        x: timestamp,
                        y: utils.ensureValue(item.voltage_2)
                    });
                    state.charts.voltage.data.datasets[2].data.push({
                        x: timestamp,
                        y: utils.ensureValue(item.voltage_3)
                    });
                }
                
                // Add data to current chart
                if (state.charts.current) {
                    state.charts.current.data.datasets[0].data.push({
                        x: timestamp,
                        y: utils.ensureValue(item.current_1)
                    });
                    state.charts.current.data.datasets[1].data.push({
                        x: timestamp,
                        y: utils.ensureValue(item.current_2)
                    });
                    state.charts.current.data.datasets[2].data.push({
                        x: timestamp,
                        y: utils.ensureValue(item.current_3)
                    });
                }
                
                // Add data to power factor chart
                if (state.charts.pf) {
                    state.charts.pf.data.datasets[0].data.push({
                        x: timestamp,
                        y: utils.ensureValue(item.pf_1)
                    });
                    state.charts.pf.data.datasets[1].data.push({
                        x: timestamp,
                        y: utils.ensureValue(item.pf_2)
                    });
                    state.charts.pf.data.datasets[2].data.push({
                        x: timestamp,
                        y: utils.ensureValue(item.pf_3)
                    });
                }
                
                // Add data to KVA chart
                if (state.charts.kva) {
                    state.charts.kva.data.datasets[0].data.push({
                        x: timestamp,
                        y: utils.ensureValue(item.kva_1)
                    });
                    state.charts.kva.data.datasets[1].data.push({
                        x: timestamp,
                        y: utils.ensureValue(item.kva_2)
                    });
                    state.charts.kva.data.datasets[2].data.push({
                        x: timestamp,
                        y: utils.ensureValue(item.kva_3)
                    });
                }
                
                // Add data to total power chart
                if (state.charts.totalPower) {
                    state.charts.totalPower.data.datasets[0].data.push({
                        x: timestamp,
                        y: utils.ensureValue(item.total_kw)
                    });
                    state.charts.totalPower.data.datasets[1].data.push({
                        x: timestamp,
                        y: utils.ensureValue(item.total_kva)
                    });
                    state.charts.totalPower.data.datasets[2].data.push({
                        x: timestamp,
                        y: utils.ensureValue(item.total_kvar)
                    });
                }
                
                // Add data to frequency chart
                if (state.charts.frequency) {
                    state.charts.frequency.data.datasets[0].data.push({
                        x: timestamp,
                        y: utils.ensureValue(item.frequency)
                    });
                }
            });
            
            // Get the earliest and latest timestamps
            const earliestTimestamp = new Date(sortedData[0].timestamp);
            const latestTimestamp = new Date(sortedData[sortedData.length - 1].timestamp);
            
            // Update all charts with the time range and calculated min/max values
            Object.entries(state.charts).forEach(([chartType, chart]) => {
                // Set the time range
                chart.options.scales.x.min = earliestTimestamp;
                chart.options.scales.x.max = latestTimestamp;
                
                // Calculate min/max values from data points
                let dataMin = Infinity;
                let dataMax = -Infinity;
                
                chart.data.datasets.forEach(dataset => {
                    dataset.data.forEach(point => {
                        dataMin = Math.min(dataMin, point.y);
                        dataMax = Math.max(dataMax, point.y);
                    });
                });
                
                // Add padding for better visualization
                const padding = (dataMax - dataMin) * 0.05;
                const paddedMin = parseFloat((dataMin - padding).toFixed(3));
                const paddedMax = parseFloat((dataMax + padding).toFixed(3));
                
                // Apply fixed ranges for PF and frequency charts
                if (chartType === 'pf') {
                    chart.options.scales.y.min = -1;
                    chart.options.scales.y.max = 1;
                } else if (chartType === 'frequency') {
                    chart.options.scales.y.min = 30;
                    chart.options.scales.y.max = 60;
                } else {
                    // For other charts, use calculated min/max values
                    chart.options.scales.y.min = paddedMin;
                    chart.options.scales.y.max = paddedMax;
                }
                
                // Update the chart
                chart.update();
            });
            
            // Update instant values with the most recent data
            updateInstantValues(sortedData[sortedData.length - 1]);
        }

        // Update instant values display
        function updateInstantValues(data) {
            if (!data) return;
            
            // Ensure values are properly parsed
            const ensureValue = utils.ensureValue;
            
            // Update voltage values with 3 decimal places
            document.getElementById('voltageValues').innerHTML = `
                <div>V1N: <span>${ensureValue(data.voltage_1).toFixed(3)} V</span></div>
                <div>V2N: <span>${ensureValue(data.voltage_2).toFixed(3)} V</span></div>
                <div>V3N: <span>${ensureValue(data.voltage_3).toFixed(3)} V</span></div>
            `;
            
            // Update current values with 3 decimal places
            document.getElementById('currentValues').innerHTML = `
                <div>Ia: <span>${ensureValue(data.current_1).toFixed(3)} A</span></div>
                <div>Ib: <span>${ensureValue(data.current_2).toFixed(3)} A</span></div>
                <div>Ic: <span>${ensureValue(data.current_3).toFixed(3)} A</span></div>
            `;
            
            // Update power factor values with 3 decimal places
            document.getElementById('pfValues').innerHTML = `
                <div>PF1: <span>${ensureValue(data.pf_1).toFixed(3)}</span></div>
                <div>PF2: <span>${ensureValue(data.pf_2).toFixed(3)}</span></div>
                <div>PF3: <span>${ensureValue(data.pf_3).toFixed(3)}</span></div>
            `;
            
            // Update KVA values with 3 decimal places
            document.getElementById('kvaValues').innerHTML = `
                <div>KVA1: <span>${ensureValue(data.kva_1).toFixed(3)} kVA</span></div>
                <div>KVA2: <span>${ensureValue(data.kva_2).toFixed(3)} kVA</span></div>
                <div>KVA3: <span>${ensureValue(data.kva_3).toFixed(3)} kVA</span></div>
            `;
            
            // Update total power values with 3 decimal places
            document.getElementById('totalPowerValues').innerHTML = `
                <div>Total KW: <span>${ensureValue(data.total_kw).toFixed(3)} kW</span></div>
                <div>Total KVA: <span>${ensureValue(data.total_kva).toFixed(3)} kVA</span></div>
                <div>Total KVAR: <span>${ensureValue(data.total_kvar).toFixed(3)} kVAR</span></div>
            `;
            
            // Update frequency value with 3 decimal places
            document.getElementById('frequencyValue').innerHTML = `
                <div>Frequency: <span>${ensureValue(data.frequency).toFixed(3)} Hz</span></div>
            `;
        }

        // Initialize the page
        function initPage() {
            // Set default date range to current time
            utils.setCurrentTime();
            
            // Create charts
            state.charts.voltage = createHistoricalChart('voltageChart', ['V1N', 'V2N', 'V3N'], colorSchemes.professional.primary, 'Voltage (V)');
            state.charts.current = createHistoricalChart('currentChart', ['Ia', 'Ib', 'Ic'], colorSchemes.professional.primary, 'Current (A)');
            state.charts.pf = createHistoricalChart('pfChart', ['PF1', 'PF2', 'PF3'], colorSchemes.professional.primary, 'Power Factor');
            state.charts.kva = createHistoricalChart('kvaChart', ['KVA1', 'KVA2', 'KVA3'], colorSchemes.professional.primary, 'KVA');
            state.charts.totalPower = createHistoricalChart('totalPowerChart', ['Total KW', 'Total KVA', 'Total KVAR'], colorSchemes.professional.primary, 'Power');
            state.charts.frequency = createHistoricalChart('frequencyChart', ['Frequency'], [colorSchemes.professional.primary[0]], 'Frequency (Hz)');
            
            // Apply fixed ranges for PF and frequency charts
            state.charts.pf.options.scales.y.min = -1;
            state.charts.pf.options.scales.y.max = 1;
            state.charts.frequency.options.scales.y.min = 30;
            state.charts.frequency.options.scales.y.max = 60;
            
            // Update the charts to apply the forced ranges
            state.charts.pf.update();
            state.charts.frequency.update();
            
            // Add event listeners
            elements.filterBtn.addEventListener('click', api.fetchHistoricalData);
            elements.exportBtn.addEventListener('click', api.exportCSV);
            
            // Fetch initial data
            api.fetchHistoricalData();
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', initPage);
    </script>
</body>
</html>
