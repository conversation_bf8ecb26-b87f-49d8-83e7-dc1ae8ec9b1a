@import 'variables';

.stability-analysis-container {
  background: rgba(30, 41, 59, 0.7);
  backdrop-filter: blur(10px);
  border-radius: $border-radius-xl;
  box-shadow: $futuristic-shadow;
  padding: 25px;
  margin-bottom: 25px;
  border: 1px solid $futuristic-border;
  transition: $transition-normal;
  animation: fade-in-up 0.8s ease forwards;
  animation-delay: 0.4s;
  opacity: 0;
}

.stability-analysis-container:hover {
  box-shadow: $futuristic-glow;
}

.analysis-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
  padding: 20px;
  background: rgba(15, 23, 42, 0.7);
  backdrop-filter: blur(10px);
  border-radius: $border-radius-lg;
  border: 1px solid $futuristic-border;
}

.control-group {
  display: flex;
  flex-direction: column;
}

.control-group label {
  margin-bottom: 8px;
  font-weight: 500;
  font-size: $font-size-sm;
  color: $futuristic-text-secondary;
  letter-spacing: 0.5px;
}

.analysis-type-selector {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.analysis-type-btn {
  padding: 10px 15px;
  border-radius: 20px;
  background: rgba(15, 23, 42, 0.7);
  color: $futuristic-text;
  font-size: $font-size-sm;
  cursor: pointer;
  transition: $transition-normal;
  border: 1px solid $futuristic-border;
  backdrop-filter: blur(10px);
}

.analysis-type-btn:hover {
  background: rgba(6, 182, 212, 0.15);
  border-color: $futuristic-accent-1;
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.analysis-type-btn.active {
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.2), rgba(59, 130, 246, 0.2));
  border-color: $futuristic-accent-1;
  color: $futuristic-accent-1;
  box-shadow: 0 0 10px rgba(6, 182, 212, 0.3);
}

.stability-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.stability-metric-card {
  background: rgba(15, 23, 42, 0.7);
  backdrop-filter: blur(10px);
  border-radius: $border-radius-lg;
  padding: 20px;
  border: 1px solid $futuristic-border;
  transition: $transition-normal;
}

.stability-metric-card:hover {
  box-shadow: $futuristic-glow;
  transform: translateY(-2px);
}

.metric-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.metric-icon {
  background: rgba(6, 182, 212, 0.15);
  color: $futuristic-accent-1;
  width: 40px;
  height: 40px;
  border-radius: $border-radius-lg;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.metric-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: $futuristic-text;
  margin: 0;
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: $futuristic-accent-1;
  margin-bottom: 10px;
  font-family: 'JetBrains Mono', monospace;
}

.metric-description {
  font-size: $font-size-sm;
  color: $futuristic-text-secondary;
  margin-bottom: 15px;
}

.metric-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.metric-detail {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.metric-detail-label {
  font-size: $font-size-xs;
  color: $futuristic-text-secondary;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-detail-value {
  font-size: $font-size-sm;
  color: $futuristic-text;
  font-family: 'JetBrains Mono', monospace;
}

.stability-score-indicator {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto 20px;
}

.score-circle {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(
    from 0deg,
    $futuristic-accent-1 0deg,
    $futuristic-accent-1 var(--score-angle),
    rgba(148, 163, 184, 0.2) var(--score-angle),
    rgba(148, 163, 184, 0.2) 360deg
  );
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.score-circle::before {
  content: '';
  position: absolute;
  width: 80%;
  height: 80%;
  background: rgba(15, 23, 42, 0.9);
  border-radius: 50%;
}

.score-text {
  position: relative;
  z-index: 1;
  text-align: center;
}

.score-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: $futuristic-text;
  font-family: 'JetBrains Mono', monospace;
}

.score-label {
  font-size: $font-size-xs;
  color: $futuristic-text-secondary;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.analysis-charts-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.analysis-chart-card {
  background: rgba(15, 23, 42, 0.7);
  backdrop-filter: blur(10px);
  border-radius: $border-radius-lg;
  padding: 20px;
  border: 1px solid $futuristic-border;
  transition: $transition-normal;
}

.analysis-chart-card:hover {
  box-shadow: $futuristic-glow;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.chart-title {
  font-size: $font-size-md;
  font-weight: 500;
  color: $futuristic-text;
  letter-spacing: 0.5px;
}

.chart-container {
  height: 300px;
  position: relative;
}

.recommendations-section {
  background: rgba(15, 23, 42, 0.7);
  backdrop-filter: blur(10px);
  border-radius: $border-radius-lg;
  padding: 20px;
  border: 1px solid $futuristic-border;
  margin-bottom: 25px;
}

.recommendations-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: $futuristic-text;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.recommendations-title .material-icons-round {
  margin-right: 10px;
  color: $futuristic-accent-1;
}

.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.recommendation-category {
  background: rgba(30, 41, 59, 0.5);
  border-radius: $border-radius-md;
  padding: 15px;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.recommendation-category h4 {
  color: $futuristic-accent-2;
  margin-bottom: 10px;
  font-size: $font-size-md;
}

.recommendation-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.recommendation-list li {
  padding: 5px 0;
  color: $futuristic-text-secondary;
  font-size: $font-size-sm;
  position: relative;
  padding-left: 20px;
}

.recommendation-list li::before {
  content: '→';
  position: absolute;
  left: 0;
  color: $futuristic-accent-1;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: $futuristic-text-secondary;
}

.loading-spinner::after {
  content: '';
  width: 40px;
  height: 40px;
  border: 3px solid rgba(6, 182, 212, 0.3);
  border-radius: 50%;
  border-top-color: $futuristic-accent-1;
  animation: spin 1s ease-in-out infinite;
  margin-left: 15px;
}

@media (max-width: $breakpoint-md) {
  .analysis-controls {
    grid-template-columns: 1fr;
  }
  
  .stability-metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .analysis-charts-container {
    grid-template-columns: 1fr;
  }
  
  .recommendations-grid {
    grid-template-columns: 1fr;
  }
}
