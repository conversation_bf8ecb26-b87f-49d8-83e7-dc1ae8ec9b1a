<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Historical Data</title>
    <style>
        * {
            box-sizing: border-box;
            font-family: Arial, sans-serif;
        }

        body {
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        h1 {
            color: #333;
            margin: 0;
        }

        .back-link {
            color: #3498db;
            text-decoration: none;
        }

        .back-link:hover {
            text-decoration: underline;
        }

        .filters {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            margin-bottom: 5px;
            font-weight: bold;
            font-size: 14px;
            color: #555;
        }

        input, select, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        button {
            background-color: #3498db;
            color: white;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #2980b9;
        }

        .data-container {
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: auto;
            margin-bottom: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            margin-top: 20px;
        }

        .page-info {
            font-size: 14px;
            color: #555;
        }

        .message {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
        }

        .message.error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .message.success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .message.warning {
            background-color: #fff3cd;
            border-color: #ffeeba;
            color: #856404;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .view-toggle {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .view-toggle button {
            padding: 8px 15px;
        }

        .view-toggle button.active {
            background-color: #2980b9;
        }

        .raw-data {
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            overflow: auto;
            max-height: 500px;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 12px;
        }

        .debug-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #ddd;
        }

        .debug-info h3 {
            margin-top: 0;
            margin-bottom: 10px;
        }

        .debug-info pre {
            background-color: #fff;
            padding: 10px;
            border-radius: 3px;
            overflow: auto;
            max-height: 300px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Historical Data <span style="font-size: 14px; color: #6c757d;">(Indian Standard Time - IST)</span></h1>
            <a href="pro_dashboard.php" class="back-link">← Back to Dashboard</a>
        </header>

        <div id="messageContainer"></div>

        <div class="filters">
            <div class="filter-group">
                <label for="startDate">Start Date</label>
                <input type="datetime-local" id="startDate" name="startDate">
            </div>

            <div class="filter-group">
                <label for="endDate">End Date</label>
                <input type="datetime-local" id="endDate" name="endDate">
            </div>

            <div class="filter-group">
                <label for="limit">Records</label>
                <select id="limit" name="limit">
                    <option value="10">10</option>
                    <option value="25" selected>25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
            </div>

            <div class="filter-group">
                <label>&nbsp;</label>
                <button id="filterBtn">Apply Filters</button>
            </div>

            <div class="filter-group">
                <label>&nbsp;</label>
                <button id="exportBtn">Export CSV</button>
            </div>

            <div class="filter-group">
                <label>&nbsp;</label>
                <button id="useSampleDateBtn">Use Sample Date</button>
            </div>
        </div>

        <div class="view-toggle">
            <button id="tableViewBtn" class="active">Table View</button>
            <button id="rawViewBtn">Raw Data View</button>
        </div>

        <div id="tableView" class="data-container">
            <table>
                <thead>
                    <tr>
                        <th>Timestamp</th>
                        <th>V12 (V)</th>
                        <th>V23 (V)</th>
                        <th>V31 (V)</th>
                        <th>Ia (A)</th>
                        <th>Ib (A)</th>
                        <th>Ic (A)</th>
                        <th>PF1</th>
                        <th>PF2</th>
                        <th>PF3</th>
                        <th>Total kW</th>
                        <th>Frequency (Hz)</th>
                    </tr>
                </thead>
                <tbody id="dataRows">
                    <tr>
                        <td colspan="12" class="loading">Loading data...</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div id="rawView" class="data-container" style="display: none;">
            <div class="raw-data" id="rawData">Loading data...</div>
        </div>

        <div class="pagination">
            <button id="prevBtn" disabled>Previous</button>
            <span id="pageInfo" class="page-info">Page 1 of 1</span>
            <button id="nextBtn" disabled>Next</button>
        </div>

        <div id="debugContainer" class="debug-info" style="display: none;">
            <h3>Debug Information</h3>
            <div id="debugContent"></div>
        </div>
    </div>

    <script>
        // DOM Elements
        const elements = {
            startDate: document.getElementById('startDate'),
            endDate: document.getElementById('endDate'),
            limit: document.getElementById('limit'),
            filterBtn: document.getElementById('filterBtn'),
            exportBtn: document.getElementById('exportBtn'),
            useSampleDateBtn: document.getElementById('useSampleDateBtn'),
            tableViewBtn: document.getElementById('tableViewBtn'),
            rawViewBtn: document.getElementById('rawViewBtn'),
            tableView: document.getElementById('tableView'),
            rawView: document.getElementById('rawView'),
            dataRows: document.getElementById('dataRows'),
            rawData: document.getElementById('rawData'),
            prevBtn: document.getElementById('prevBtn'),
            nextBtn: document.getElementById('nextBtn'),
            pageInfo: document.getElementById('pageInfo'),
            messageContainer: document.getElementById('messageContainer'),
            debugContainer: document.getElementById('debugContainer'),
            debugContent: document.getElementById('debugContent')
        };

        // State
        const state = {
            currentPage: 1,
            totalPages: 1,
            data: [],
            isLoading: false,
            viewMode: 'table', // 'table' or 'raw'
            debugMode: false
        };

        // Utility Functions
        const utils = {
            // Show a message to the user
            showMessage: (message, type = 'info') => {
                elements.messageContainer.innerHTML = `
                    <div class="message ${type}">
                        ${message}
                    </div>
                `;
            },

            // Clear any displayed messages
            clearMessage: () => {
                elements.messageContainer.innerHTML = '';
            },

            // Format date for datetime-local input (in IST - Indian Standard Time)
            formatDateForInput: (date) => {
                // Convert to IST by adding 5 hours and 30 minutes to UTC
                // This is needed because the input expects local time
                const istDate = new Date(date.getTime() + (5.5 * 60 * 60 * 1000));
                return istDate.toISOString().slice(0, 16);
            },

            // Format date for server (YYYY-MM-DD HH:MM:SS)
            formatDateForServer: (dateObj) => {
                if (typeof dateObj === 'string') {
                    dateObj = new Date(dateObj);
                }

                // Adjust for IST timezone (UTC+5:30)
                // When we get a date from the input, it's in local time
                // We need to convert it to UTC and then to IST for the server

                // Format as YYYY-MM-DD HH:MM:SS
                const year = dateObj.getFullYear();
                const month = String(dateObj.getMonth() + 1).padStart(2, '0');
                const day = String(dateObj.getDate()).padStart(2, '0');
                const hours = String(dateObj.getHours()).padStart(2, '0');
                const minutes = String(dateObj.getMinutes()).padStart(2, '0');
                const seconds = String(dateObj.getSeconds()).padStart(2, '0');

                const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                console.log('Formatted date for server:', formattedDate);
                return formattedDate;
            },

            // Format number with 2 decimal places
            formatNumber: (value) => {
                if (value === undefined || value === null) return '0.00';
                try {
                    return parseFloat(value).toFixed(2);
                } catch (e) {
                    return '0.00';
                }
            },

            // Format timestamp for display in Indian Standard Time (IST)
            formatTimestamp: (timestamp) => {
                if (!timestamp) return 'N/A';
                try {
                    // Parse the timestamp
                    const date = new Date(timestamp);
                    if (isNaN(date.getTime())) return timestamp;

                    // Format with explicit IST timezone
                    const options = {
                        timeZone: 'Asia/Kolkata', // Indian Standard Time
                        year: 'numeric',
                        month: 'numeric',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: true
                    };

                    const formattedDate = date.toLocaleString('en-IN', options);
                    return formattedDate + ' (IST)';
                } catch (e) {
                    console.error('Error formatting timestamp:', e);
                    return timestamp;
                }
            },

            // Toggle view mode
            setViewMode: (mode) => {
                state.viewMode = mode;

                if (mode === 'table') {
                    elements.tableView.style.display = 'block';
                    elements.rawView.style.display = 'none';
                    elements.tableViewBtn.classList.add('active');
                    elements.rawViewBtn.classList.remove('active');
                } else {
                    elements.tableView.style.display = 'none';
                    elements.rawView.style.display = 'block';
                    elements.tableViewBtn.classList.remove('active');
                    elements.rawViewBtn.classList.add('active');
                }
            },

            // Show debug information
            showDebugInfo: (debugData) => {
                if (!debugData) {
                    elements.debugContainer.style.display = 'none';
                    return;
                }

                elements.debugContainer.style.display = 'block';
                elements.debugContent.innerHTML = `<pre>${JSON.stringify(debugData, null, 2)}</pre>`;
            },

            // Set sample date (2025-04-23 09:59:05) - This is the exact timestamp where data exists
            setSampleDate: () => {
                // The exact timestamp from the database where we know data exists
                const exactTimestamp = '2025-04-23 09:59:05';
                console.log('Using exact timestamp from database:', exactTimestamp);

                // Parse the sample date
                const sampleDate = new Date(exactTimestamp);

                // Set start date to 1 minute before the sample (narrow window to ensure we get the exact record)
                const startDate = new Date(sampleDate);
                startDate.setMinutes(startDate.getMinutes() - 1);

                // Set end date to 1 minute after the sample (narrow window to ensure we get the exact record)
                const endDate = new Date(sampleDate);
                endDate.setMinutes(endDate.getMinutes() + 1);

                console.log('Sample date range:',
                    utils.formatDateForServer(startDate), 'to',
                    utils.formatDateForServer(endDate));

                // Format dates for the input fields
                elements.startDate.value = utils.formatDateForInput(startDate);
                elements.endDate.value = utils.formatDateForInput(endDate);

                utils.showMessage(`Using exact timestamp from database: ${exactTimestamp} (IST)`, 'success');

                // Fetch data with the new date range
                api.fetchHistoricalData();
            }
        };

        // API Functions
        const api = {
            // Fetch historical data from the backend
            fetchHistoricalData: async () => {
                try {
                    state.isLoading = true;
                    utils.clearMessage();

                    // Show loading state
                    elements.dataRows.innerHTML = '<tr><td colspan="12" class="loading">Loading data...</td></tr>';
                    elements.rawData.textContent = 'Loading data...';

                    // Get the input values
                    const startDate = elements.startDate.value;
                    const endDate = elements.endDate.value;

                    // Validate inputs
                    if (!startDate || !endDate) {
                        utils.showMessage('Please select both start and end dates', 'error');
                        return null;
                    }

                    // Parse dates from input fields
                    const startDateObj = new Date(startDate);
                    const endDateObj = new Date(endDate);

                    console.log('Raw date objects:', startDateObj, endDateObj);

                    // Format dates for server in YYYY-MM-DD HH:MM:SS format
                    const startFormatted = utils.formatDateForServer(startDateObj);
                    const endFormatted = utils.formatDateForServer(endDateObj);

                    console.log('Formatted date range for server:', startFormatted, 'to', endFormatted);

                    // Prepare request parameters
                    const limit = elements.limit.value;
                    const offset = (state.currentPage - 1) * parseInt(limit);
                    const cacheBuster = new Date().getTime();

                    // Build URL
                    const url = `../../app/backend/api/api/get_historical_data.php?start=${encodeURIComponent(startFormatted)}&end=${encodeURIComponent(endFormatted)}&limit=${limit}&offset=${offset}&_=${cacheBuster}`;

                    console.log('Fetching data with URL:', url);

                    // Fetch data
                    const response = await fetch(url);
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }

                    // Parse response
                    const responseText = await response.text();
                    console.log('Raw response:', responseText);

                    let data;
                    try {
                        data = JSON.parse(responseText);
                    } catch (parseError) {
                        throw new Error(`Failed to parse JSON response: ${parseError.message}`);
                    }

                    console.log('Parsed data:', data);

                    // Handle error in response
                    if (data.error) {
                        utils.showMessage(`Error: ${data.error}`, 'error');
                        utils.showDebugInfo(state.debugMode ? data.debug : null);
                        return null;
                    }

                    // Process data
                    state.data = Array.isArray(data.data) ? data.data : [];
                    state.totalPages = Math.ceil(data.total / parseInt(limit));

                    // Check if we have no data but sample data exists
                    if (state.data.length === 0 && data.debug && data.debug.test_query_result && data.debug.test_query_result.length > 0) {
                        const sampleData = data.debug.test_query_result[0];
                        const sampleTimestamp = sampleData.timestamp;

                        utils.showMessage(`
                            <h3>No data found for the selected time range</h3>
                            <p>There is data in the database, but not for the specific time range you selected.</p>
                            <p>Try searching for data around: <strong>${sampleTimestamp}</strong></p>
                            <button id="useSampleDateNowBtn" style="margin-top: 10px; padding: 5px 10px; background-color: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;">Use This Date</button>
                        `, 'warning');

                        // Add event listener to the button
                        document.getElementById('useSampleDateNowBtn').addEventListener('click', utils.setSampleDate);
                    } else if (state.data.length === 0) {
                        utils.showMessage('No data found for the selected period', 'warning');
                    } else {
                        utils.showMessage(`Found ${state.data.length} records`, 'success');
                    }

                    // Update UI
                    ui.renderTable();
                    ui.updateRawView();
                    ui.updatePagination();
                    utils.showDebugInfo(state.debugMode ? data.debug : null);

                    return data;
                } catch (error) {
                    console.error('Error fetching historical data:', error);
                    utils.showMessage(`Error: ${error.message}`, 'error');
                    return null;
                } finally {
                    state.isLoading = false;
                }
            },

            // Export data as CSV
            exportCSV: () => {
                if (state.data.length === 0) {
                    utils.showMessage('No data to export', 'error');
                    return;
                }

                // Create CSV header
                let csv = 'Timestamp,V12 (V),V23 (V),V31 (V),Ia (A),Ib (A),Ic (A),PF1,PF2,PF3,Total kW,Frequency (Hz)\n';

                // Add data rows
                state.data.forEach(row => {
                    const timestamp = utils.formatTimestamp(row.timestamp);
                    csv += `"${timestamp}",${row.voltage_1},${row.voltage_2},${row.voltage_3},${row.current_1},${row.current_2},${row.current_3},${row.pf_1},${row.pf_2},${row.pf_3},${row.total_kw},${row.frequency}\n`;
                });

                // Create download link
                const blob = new Blob([csv], { type: 'text/csv' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.setAttribute('hidden', '');
                a.setAttribute('href', url);
                a.setAttribute('download', `electrical_data_${new Date().toISOString().slice(0, 10)}.csv`);
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);

                utils.showMessage('CSV exported successfully', 'success');
            }
        };

        // UI Functions
        const ui = {
            // Render the data table
            renderTable: () => {
                if (state.data.length === 0) {
                    elements.dataRows.innerHTML = '<tr><td colspan="12" style="text-align: center;">No data available</td></tr>';
                    return;
                }

                let html = '';
                state.data.forEach(row => {
                    const timestamp = utils.formatTimestamp(row.timestamp);
                    html += `<tr>
                        <td>${timestamp}</td>
                        <td>${utils.formatNumber(row.voltage_1)}</td>
                        <td>${utils.formatNumber(row.voltage_2)}</td>
                        <td>${utils.formatNumber(row.voltage_3)}</td>
                        <td>${utils.formatNumber(row.current_1)}</td>
                        <td>${utils.formatNumber(row.current_2)}</td>
                        <td>${utils.formatNumber(row.current_3)}</td>
                        <td>${utils.formatNumber(row.pf_1)}</td>
                        <td>${utils.formatNumber(row.pf_2)}</td>
                        <td>${utils.formatNumber(row.pf_3)}</td>
                        <td>${utils.formatNumber(row.total_kw)}</td>
                        <td>${utils.formatNumber(row.frequency)}</td>
                    </tr>`;
                });

                elements.dataRows.innerHTML = html;
            },

            // Update the raw data view
            updateRawView: () => {
                elements.rawData.textContent = JSON.stringify(state.data, null, 2);
            },

            // Update pagination controls
            updatePagination: () => {
                elements.pageInfo.textContent = `Page ${state.currentPage} of ${state.totalPages || 1}`;
                elements.prevBtn.disabled = state.currentPage <= 1;
                elements.nextBtn.disabled = state.currentPage >= state.totalPages;
            },

            // Initialize the page
            init: () => {
                // Set default date range using the sample data date (2025-04-23 09:59:05)
                utils.setSampleDate();

                // Add event listeners
                elements.filterBtn.addEventListener('click', api.fetchHistoricalData);
                elements.exportBtn.addEventListener('click', api.exportCSV);
                elements.useSampleDateBtn.addEventListener('click', utils.setSampleDate);

                elements.prevBtn.addEventListener('click', () => {
                    if (state.currentPage > 1) {
                        state.currentPage--;
                        api.fetchHistoricalData();
                    }
                });

                elements.nextBtn.addEventListener('click', () => {
                    if (state.currentPage < state.totalPages) {
                        state.currentPage++;
                        api.fetchHistoricalData();
                    }
                });

                elements.tableViewBtn.addEventListener('click', () => {
                    utils.setViewMode('table');
                });

                elements.rawViewBtn.addEventListener('click', () => {
                    utils.setViewMode('raw');
                });

                // Toggle debug mode with keyboard shortcut (Ctrl+Shift+D)
                document.addEventListener('keydown', (e) => {
                    if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                        state.debugMode = !state.debugMode;
                        utils.showMessage(state.debugMode ? 'Debug mode enabled' : 'Debug mode disabled', state.debugMode ? 'warning' : 'success');
                        console.log('Debug mode:', state.debugMode);
                    }
                });
            }
        };

        // Initialize the page
        document.addEventListener('DOMContentLoaded', ui.init);
    </script>
</body>
</html>
