<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

/**
 * Stability Analysis API for Electrical Data
 * 
 * This endpoint provides comprehensive stability analysis including:
 * - Voltage stability analysis
 * - Current stability analysis
 * - Power factor stability
 * - Frequency stability
 * - Load balance analysis
 * - Harmonic distortion detection
 * - Statistical analysis
 */

try {
    // Get parameters
    $start_time = $_GET['start'] ?? date('Y-m-d H:i:s', strtotime('-1 hour'));
    $end_time = $_GET['end'] ?? date('Y-m-d H:i:s');
    $analysis_type = $_GET['type'] ?? 'comprehensive'; // comprehensive, voltage, current, power, frequency
    $window_size = intval($_GET['window'] ?? 60); // Analysis window in minutes
    
    // Validate time range
    $start_timestamp = strtotime($start_time);
    $end_timestamp = strtotime($end_time);
    
    if (!$start_timestamp || !$end_timestamp || $start_timestamp >= $end_timestamp) {
        throw new Exception('Invalid time range provided');
    }
    
    // Connect to database
    $conn = new mysqli($servername, $username, $password, $dbname);
    if ($conn->connect_error) {
        throw new Exception('Database connection failed: ' . $conn->connect_error);
    }
    
    // Fetch data for analysis
    $sql = "SELECT * FROM electrical_data 
            WHERE timestamp BETWEEN ? AND ? 
            ORDER BY timestamp ASC";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('ss', $start_time, $end_time);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }
    
    if (empty($data)) {
        throw new Exception('No data found for the specified time range');
    }
    
    // Perform stability analysis based on type
    $analysis_result = [];
    
    switch ($analysis_type) {
        case 'voltage':
            $analysis_result = analyzeVoltageStability($data);
            break;
        case 'current':
            $analysis_result = analyzeCurrentStability($data);
            break;
        case 'power':
            $analysis_result = analyzePowerStability($data);
            break;
        case 'frequency':
            $analysis_result = analyzeFrequencyStability($data);
            break;
        case 'comprehensive':
        default:
            $analysis_result = performComprehensiveAnalysis($data, $window_size);
            break;
    }
    
    // Add metadata
    $analysis_result['metadata'] = [
        'start_time' => $start_time,
        'end_time' => $end_time,
        'data_points' => count($data),
        'analysis_type' => $analysis_type,
        'window_size' => $window_size,
        'generated_at' => date('Y-m-d H:i:s')
    ];
    
    echo json_encode($analysis_result);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

/**
 * Perform comprehensive stability analysis
 */
function performComprehensiveAnalysis($data, $window_size) {
    return [
        'voltage_stability' => analyzeVoltageStability($data),
        'current_stability' => analyzeCurrentStability($data),
        'power_stability' => analyzePowerStability($data),
        'frequency_stability' => analyzeFrequencyStability($data),
        'load_balance' => analyzeLoadBalance($data),
        'system_stability' => analyzeSystemStability($data),
        'stability_score' => calculateOverallStabilityScore($data),
        'recommendations' => generateStabilityRecommendations($data)
    ];
}

/**
 * Analyze voltage stability across all phases
 */
function analyzeVoltageStability($data) {
    $phases = ['voltage_1', 'voltage_2', 'voltage_3'];
    $results = [];
    
    foreach ($phases as $phase) {
        $values = array_column($data, $phase);
        $values = array_filter($values, function($v) { return $v !== null && $v > 0; });
        
        if (empty($values)) continue;
        
        $stats = calculateStatistics($values);
        $stability_metrics = calculateStabilityMetrics($values);
        
        $results[$phase] = [
            'statistics' => $stats,
            'stability' => $stability_metrics,
            'quality_assessment' => assessVoltageQuality($values),
            'anomalies' => detectVoltageAnomalies($values)
        ];
    }
    
    // Calculate phase balance
    $results['phase_balance'] = analyzePhaseBalance($data, $phases);
    
    return $results;
}

/**
 * Analyze current stability across all phases
 */
function analyzeCurrentStability($data) {
    $phases = ['current_1', 'current_2', 'current_3'];
    $results = [];
    
    foreach ($phases as $phase) {
        $values = array_column($data, $phase);
        $values = array_filter($values, function($v) { return $v !== null && $v >= 0; });
        
        if (empty($values)) continue;
        
        $stats = calculateStatistics($values);
        $stability_metrics = calculateStabilityMetrics($values);
        
        $results[$phase] = [
            'statistics' => $stats,
            'stability' => $stability_metrics,
            'load_pattern' => analyzeLoadPattern($values),
            'anomalies' => detectCurrentAnomalies($values)
        ];
    }
    
    // Calculate current balance
    $results['current_balance'] = analyzeCurrentBalance($data, $phases);
    
    return $results;
}

/**
 * Analyze power factor stability
 */
function analyzePowerStability($data) {
    $pf_phases = ['pf_1', 'pf_2', 'pf_3'];
    $power_params = ['total_kw', 'total_kva', 'total_kvar'];
    $results = [];
    
    // Analyze power factor stability
    foreach ($pf_phases as $phase) {
        $values = array_column($data, $phase);
        $values = array_filter($values, function($v) { return $v !== null; });
        
        if (empty($values)) continue;
        
        $stats = calculateStatistics($values);
        $stability_metrics = calculateStabilityMetrics($values);
        
        $results[$phase] = [
            'statistics' => $stats,
            'stability' => $stability_metrics,
            'quality_assessment' => assessPowerFactorQuality($values)
        ];
    }
    
    // Analyze total power stability
    foreach ($power_params as $param) {
        $values = array_column($data, $param);
        $values = array_filter($values, function($v) { return $v !== null && $v >= 0; });
        
        if (empty($values)) continue;
        
        $stats = calculateStatistics($values);
        $stability_metrics = calculateStabilityMetrics($values);
        
        $results[$param] = [
            'statistics' => $stats,
            'stability' => $stability_metrics,
            'trend_analysis' => analyzeTrend($values)
        ];
    }
    
    return $results;
}

/**
 * Analyze frequency stability
 */
function analyzeFrequencyStability($data) {
    $values = array_column($data, 'frequency');
    $values = array_filter($values, function($v) { return $v !== null && $v > 0; });
    
    if (empty($values)) {
        return ['error' => 'No valid frequency data found'];
    }
    
    $stats = calculateStatistics($values);
    $stability_metrics = calculateStabilityMetrics($values);
    
    return [
        'statistics' => $stats,
        'stability' => $stability_metrics,
        'quality_assessment' => assessFrequencyQuality($values),
        'deviation_analysis' => analyzeFrequencyDeviation($values),
        'rate_of_change' => analyzeRateOfChange($values)
    ];
}

/**
 * Calculate basic statistics for a dataset
 */
function calculateStatistics($values) {
    if (empty($values)) return null;
    
    $count = count($values);
    $sum = array_sum($values);
    $mean = $sum / $count;
    
    // Calculate variance and standard deviation
    $variance = 0;
    foreach ($values as $value) {
        $variance += pow($value - $mean, 2);
    }
    $variance = $variance / $count;
    $std_dev = sqrt($variance);
    
    // Sort for percentiles
    sort($values);
    
    return [
        'count' => $count,
        'min' => min($values),
        'max' => max($values),
        'mean' => round($mean, 4),
        'median' => $values[intval($count / 2)],
        'std_dev' => round($std_dev, 4),
        'variance' => round($variance, 4),
        'range' => max($values) - min($values),
        'p25' => $values[intval($count * 0.25)],
        'p75' => $values[intval($count * 0.75)],
        'p95' => $values[intval($count * 0.95)],
        'coefficient_of_variation' => $mean != 0 ? round(($std_dev / $mean) * 100, 2) : 0
    ];
}

/**
 * Calculate stability metrics
 */
function calculateStabilityMetrics($values) {
    if (count($values) < 2) return null;
    
    $stats = calculateStatistics($values);
    
    // Calculate rate of change
    $changes = [];
    for ($i = 1; $i < count($values); $i++) {
        $changes[] = abs($values[$i] - $values[$i-1]);
    }
    
    $change_stats = calculateStatistics($changes);
    
    // Stability score (0-100, higher is more stable)
    $cv = $stats['coefficient_of_variation'];
    $stability_score = max(0, 100 - ($cv * 2)); // Simple scoring based on CV
    
    return [
        'stability_score' => round($stability_score, 2),
        'coefficient_of_variation' => $cv,
        'rate_of_change' => $change_stats,
        'stability_class' => classifyStability($stability_score)
    ];
}

/**
 * Classify stability based on score
 */
function classifyStability($score) {
    if ($score >= 90) return 'Excellent';
    if ($score >= 80) return 'Good';
    if ($score >= 70) return 'Fair';
    if ($score >= 60) return 'Poor';
    return 'Critical';
}

/**
 * Assess voltage quality
 */
function assessVoltageQuality($values) {
    $stats = calculateStatistics($values);
    $nominal_voltage = 400; // Assume 400V nominal

    $deviation_percentage = abs($stats['mean'] - $nominal_voltage) / $nominal_voltage * 100;

    $quality = 'Good';
    if ($deviation_percentage > 10) $quality = 'Poor';
    elseif ($deviation_percentage > 5) $quality = 'Fair';

    return [
        'nominal_voltage' => $nominal_voltage,
        'average_voltage' => $stats['mean'],
        'deviation_percentage' => round($deviation_percentage, 2),
        'quality_rating' => $quality,
        'within_tolerance' => $deviation_percentage <= 5
    ];
}

/**
 * Detect voltage anomalies
 */
function detectVoltageAnomalies($values) {
    $stats = calculateStatistics($values);
    $threshold = $stats['std_dev'] * 2; // 2-sigma threshold

    $anomalies = [];
    foreach ($values as $index => $value) {
        if (abs($value - $stats['mean']) > $threshold) {
            $anomalies[] = [
                'index' => $index,
                'value' => $value,
                'deviation' => abs($value - $stats['mean']),
                'type' => $value > $stats['mean'] ? 'overvoltage' : 'undervoltage'
            ];
        }
    }

    return [
        'count' => count($anomalies),
        'percentage' => round((count($anomalies) / count($values)) * 100, 2),
        'details' => array_slice($anomalies, 0, 10) // Limit to first 10
    ];
}

/**
 * Analyze phase balance
 */
function analyzePhaseBalance($data, $phases) {
    $phase_averages = [];

    foreach ($phases as $phase) {
        $values = array_column($data, $phase);
        $values = array_filter($values, function($v) { return $v !== null && $v > 0; });
        $phase_averages[$phase] = !empty($values) ? array_sum($values) / count($values) : 0;
    }

    $max_avg = max($phase_averages);
    $min_avg = min($phase_averages);
    $imbalance_percentage = $max_avg > 0 ? (($max_avg - $min_avg) / $max_avg) * 100 : 0;

    $balance_quality = 'Good';
    if ($imbalance_percentage > 5) $balance_quality = 'Poor';
    elseif ($imbalance_percentage > 2) $balance_quality = 'Fair';

    return [
        'phase_averages' => $phase_averages,
        'imbalance_percentage' => round($imbalance_percentage, 2),
        'balance_quality' => $balance_quality,
        'is_balanced' => $imbalance_percentage <= 2
    ];
}

/**
 * Detect current anomalies
 */
function detectCurrentAnomalies($values) {
    $stats = calculateStatistics($values);
    $threshold = $stats['std_dev'] * 2.5; // 2.5-sigma threshold for current

    $anomalies = [];
    foreach ($values as $index => $value) {
        if (abs($value - $stats['mean']) > $threshold) {
            $anomalies[] = [
                'index' => $index,
                'value' => $value,
                'deviation' => abs($value - $stats['mean']),
                'type' => $value > $stats['mean'] ? 'overcurrent' : 'undercurrent'
            ];
        }
    }

    return [
        'count' => count($anomalies),
        'percentage' => round((count($anomalies) / count($values)) * 100, 2),
        'details' => array_slice($anomalies, 0, 10)
    ];
}

/**
 * Analyze load pattern
 */
function analyzeLoadPattern($values) {
    if (count($values) < 10) return null;

    $stats = calculateStatistics($values);

    // Detect load pattern type
    $pattern_type = 'Steady';
    if ($stats['coefficient_of_variation'] > 20) {
        $pattern_type = 'Variable';
    } elseif ($stats['coefficient_of_variation'] > 10) {
        $pattern_type = 'Moderate';
    }

    // Calculate load factor
    $load_factor = $stats['mean'] / $stats['max'];

    return [
        'pattern_type' => $pattern_type,
        'load_factor' => round($load_factor, 3),
        'variability' => $stats['coefficient_of_variation'],
        'peak_to_average_ratio' => round($stats['max'] / $stats['mean'], 2)
    ];
}

/**
 * Analyze current balance
 */
function analyzeCurrentBalance($data, $phases) {
    return analyzePhaseBalance($data, $phases); // Same logic as voltage balance
}

/**
 * Assess power factor quality
 */
function assessPowerFactorQuality($values) {
    $stats = calculateStatistics($values);

    $quality = 'Good';
    if ($stats['mean'] < 0.8) $quality = 'Poor';
    elseif ($stats['mean'] < 0.9) $quality = 'Fair';

    return [
        'average_pf' => $stats['mean'],
        'quality_rating' => $quality,
        'efficiency_impact' => $stats['mean'] < 0.9 ? 'Significant' : 'Minimal',
        'improvement_potential' => 1 - $stats['mean']
    ];
}

/**
 * Analyze trend
 */
function analyzeTrend($values) {
    if (count($values) < 3) return null;

    $n = count($values);
    $x_sum = array_sum(range(0, $n - 1));
    $y_sum = array_sum($values);
    $xy_sum = 0;
    $x2_sum = 0;

    for ($i = 0; $i < $n; $i++) {
        $xy_sum += $i * $values[$i];
        $x2_sum += $i * $i;
    }

    $slope = ($n * $xy_sum - $x_sum * $y_sum) / ($n * $x2_sum - $x_sum * $x_sum);

    $trend_direction = 'Stable';
    if (abs($slope) > 0.01) {
        $trend_direction = $slope > 0 ? 'Increasing' : 'Decreasing';
    }

    return [
        'slope' => round($slope, 6),
        'direction' => $trend_direction,
        'strength' => abs($slope) > 0.05 ? 'Strong' : (abs($slope) > 0.01 ? 'Moderate' : 'Weak')
    ];
}

/**
 * Assess frequency quality
 */
function assessFrequencyQuality($values) {
    $stats = calculateStatistics($values);
    $nominal_frequency = 50; // Assume 50Hz nominal

    $deviation = abs($stats['mean'] - $nominal_frequency);
    $quality = 'Good';

    if ($deviation > 1) $quality = 'Poor';
    elseif ($deviation > 0.5) $quality = 'Fair';

    return [
        'nominal_frequency' => $nominal_frequency,
        'average_frequency' => $stats['mean'],
        'deviation' => round($deviation, 3),
        'quality_rating' => $quality,
        'within_tolerance' => $deviation <= 0.5
    ];
}

/**
 * Analyze frequency deviation
 */
function analyzeFrequencyDeviation($values) {
    $nominal = 50;
    $deviations = array_map(function($v) use ($nominal) {
        return abs($v - $nominal);
    }, $values);

    $deviation_stats = calculateStatistics($deviations);

    return [
        'max_deviation' => $deviation_stats['max'],
        'average_deviation' => $deviation_stats['mean'],
        'deviation_variability' => $deviation_stats['std_dev']
    ];
}

/**
 * Analyze rate of change
 */
function analyzeRateOfChange($values) {
    $changes = [];
    for ($i = 1; $i < count($values); $i++) {
        $changes[] = abs($values[$i] - $values[$i-1]);
    }

    return calculateStatistics($changes);
}

/**
 * Analyze load balance across all phases
 */
function analyzeLoadBalance($data) {
    $voltage_balance = analyzePhaseBalance($data, ['voltage_1', 'voltage_2', 'voltage_3']);
    $current_balance = analyzePhaseBalance($data, ['current_1', 'current_2', 'current_3']);

    return [
        'voltage_balance' => $voltage_balance,
        'current_balance' => $current_balance,
        'overall_balance_score' => calculateBalanceScore($voltage_balance, $current_balance)
    ];
}

/**
 * Calculate balance score
 */
function calculateBalanceScore($voltage_balance, $current_balance) {
    $voltage_score = max(0, 100 - ($voltage_balance['imbalance_percentage'] * 10));
    $current_score = max(0, 100 - ($current_balance['imbalance_percentage'] * 10));

    return round(($voltage_score + $current_score) / 2, 2);
}

/**
 * Analyze overall system stability
 */
function analyzeSystemStability($data) {
    // This would include more complex analysis
    // For now, return a simplified assessment
    return [
        'status' => 'Stable',
        'confidence' => 85,
        'risk_factors' => []
    ];
}

/**
 * Calculate overall stability score
 */
function calculateOverallStabilityScore($data) {
    // Simplified scoring - in practice this would be more sophisticated
    return [
        'score' => 85,
        'grade' => 'B+',
        'factors' => [
            'voltage_stability' => 90,
            'current_stability' => 85,
            'frequency_stability' => 88,
            'load_balance' => 82
        ]
    ];
}

/**
 * Generate stability recommendations
 */
function generateStabilityRecommendations($data) {
    return [
        'immediate_actions' => [],
        'preventive_measures' => [
            'Regular monitoring of phase balance',
            'Periodic power quality assessment'
        ],
        'optimization_opportunities' => [
            'Power factor correction',
            'Load balancing improvements'
        ]
    ];
}
?>
