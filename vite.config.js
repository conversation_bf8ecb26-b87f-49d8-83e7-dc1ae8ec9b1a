import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  // Base public path when served in development or production
  base: '/online data logger/assets/',
  
  // Configure the build output directory
  build: {
    outDir: 'frontend/assets',
    emptyOutDir: true,
    manifest: true,
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'src/js/main.js'),
        history: resolve(__dirname, 'src/js/history.js'),
        stability: resolve(__dirname, 'src/js/stability.js'),
        alerts: resolve(__dirname, 'src/js/alerts.js'),
        reports: resolve(__dirname, 'src/js/reports.js'),
        dashboard: resolve(__dirname, 'src/js/dashboard.js'),
        monkData: resolve(__dirname, 'src/js/monk-data.js'),
        testData: resolve(__dirname, 'src/js/test-data.js'),
      },
      output: {
        entryFileNames: 'js/[name]-[hash].js',
        chunkFileNames: 'js/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    }
  },
  
  // Configure server options
  server: {
    // Adjust this to match your PHP server
    proxy: {
      '/api': 'http://localhost/online data logger/backend'
    },
    cors: true,
    port: 3000,
    strictPort: true,
    hmr: {
      host: 'localhost',
      port: 3000
    }
  },
  
  // Configure CSS processing
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "./src/scss/variables.scss";`
      }
    }
  },
  
  // Resolve aliases for easier imports
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@scss': resolve(__dirname, 'src/scss'),
      '@js': resolve(__dirname, 'src/js'),
      '@assets': resolve(__dirname, 'src/assets')
    }
  }
});
