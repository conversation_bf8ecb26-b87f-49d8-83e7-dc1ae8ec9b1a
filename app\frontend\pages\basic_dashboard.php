<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basic Power Monitoring Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8fafc;
            color: #0f172a;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e2e8f0;
        }
        h1 {
            color: #2563eb;
            margin: 0;
        }
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .card-title {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 10px;
        }
        .card-value {
            font-size: 24px;
            font-weight: 600;
            color: #0f172a;
        }
        .card-unit {
            font-size: 14px;
            color: #64748b;
            margin-left: 5px;
        }
        .tables {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
            gap: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th {
            text-align: left;
            padding: 12px;
            background-color: #f1f5f9;
            color: #64748b;
            font-weight: 500;
            font-size: 14px;
        }
        td {
            padding: 12px;
            border-bottom: 1px solid #e2e8f0;
        }
        .button {
            background-color: #2563eb;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
        }
        .button:hover {
            background-color: #1d4ed8;
        }
        .timestamp {
            color: #64748b;
            font-size: 14px;
        }
        #loading {
            text-align: center;
            padding: 50px;
            font-size: 18px;
            color: #64748b;
        }
        #error {
            background-color: #fee2e2;
            color: #b91c1c;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Power Monitoring Dashboard</h1>
            <div>
                <span id="last-updated" class="timestamp">Last updated: Loading...</span>
            </div>
        </header>

        <div id="error"></div>
        <div id="loading">Loading data...</div>

        <div id="dashboard-content" style="display: none;">
            <div class="dashboard">
                <div class="card">
                    <div class="card-title">Voltage (Avg)</div>
                    <div class="card-value" id="voltage-avg">0<span class="card-unit">V</span></div>
                </div>
                <div class="card">
                    <div class="card-title">Current (Total)</div>
                    <div class="card-value" id="current-total">0<span class="card-unit">A</span></div>
                </div>
                <div class="card">
                    <div class="card-title">Power Factor (Avg)</div>
                    <div class="card-value" id="pf-avg">0</div>
                </div>
                <div class="card">
                    <div class="card-title">Frequency</div>
                    <div class="card-value" id="frequency">0<span class="card-unit">Hz</span></div>
                </div>
            </div>

            <div class="tables">
                <div class="card">
                    <div class="card-title">Voltage Readings</div>
                    <table>
                        <thead>
                            <tr>
                                <th>Phase</th>
                                <th>Value (V)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Phase 1</td>
                                <td id="voltage-1">0</td>
                            </tr>
                            <tr>
                                <td>Phase 2</td>
                                <td id="voltage-2">0</td>
                            </tr>
                            <tr>
                                <td>Phase 3</td>
                                <td id="voltage-3">0</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="card">
                    <div class="card-title">Current Readings</div>
                    <table>
                        <thead>
                            <tr>
                                <th>Phase</th>
                                <th>Value (A)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Phase 1</td>
                                <td id="current-1">0</td>
                            </tr>
                            <tr>
                                <td>Phase 2</td>
                                <td id="current-2">0</td>
                            </tr>
                            <tr>
                                <td>Phase 3</td>
                                <td id="current-3">0</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div style="margin-top: 20px; display: flex; justify-content: space-between; align-items: center;">
                <button id="refresh-btn" class="button">Refresh Data</button>
                <span id="timestamp" class="timestamp">Timestamp: -</span>
            </div>
        </div>
    </div>

    <script>
        // Format number to 3 decimal places
        function formatNumber(value) {
            return parseFloat(value).toFixed(3);
        }

        // Calculate average of values
        function calculateAverage(values) {
            if (!values || values.length === 0) return 0;
            const sum = values.reduce((acc, val) => acc + parseFloat(val || 0), 0);
            return sum / values.length;
        }

        // Calculate sum of values
        function calculateSum(values) {
            if (!values || values.length === 0) return 0;
            return values.reduce((acc, val) => acc + parseFloat(val || 0), 0);
        }

        // Update the dashboard with data
        function updateDashboard(data) {
            // Update summary cards
            document.getElementById('voltage-avg').innerHTML = formatNumber(calculateAverage([data.voltage_1, data.voltage_2, data.voltage_3])) + '<span class="card-unit">V</span>';
            document.getElementById('current-total').innerHTML = formatNumber(calculateSum([data.current_1, data.current_2, data.current_3])) + '<span class="card-unit">A</span>';
            document.getElementById('pf-avg').textContent = formatNumber(calculateAverage([data.pf_1, data.pf_2, data.pf_3]));
            document.getElementById('frequency').innerHTML = formatNumber(data.frequency) + '<span class="card-unit">Hz</span>';

            // Update voltage table
            document.getElementById('voltage-1').textContent = formatNumber(data.voltage_1);
            document.getElementById('voltage-2').textContent = formatNumber(data.voltage_2);
            document.getElementById('voltage-3').textContent = formatNumber(data.voltage_3);

            // Update current table
            document.getElementById('current-1').textContent = formatNumber(data.current_1);
            document.getElementById('current-2').textContent = formatNumber(data.current_2);
            document.getElementById('current-3').textContent = formatNumber(data.current_3);

            // Update timestamp
            document.getElementById('timestamp').textContent = 'Timestamp: ' + data.timestamp;
            
            // Update last updated time
            document.getElementById('last-updated').textContent = 'Last updated: ' + new Date().toLocaleTimeString();
            
            // Show dashboard content
            document.getElementById('loading').style.display = 'none';
            document.getElementById('dashboard-content').style.display = 'block';
        }

        // Show error message
        function showError(message) {
            const errorElement = document.getElementById('error');
            errorElement.textContent = message;
            errorElement.style.display = 'block';
            document.getElementById('loading').style.display = 'none';
        }

        // Fetch data from the backend
        async function fetchData() {
            try {
                document.getElementById('loading').style.display = 'block';
                document.getElementById('error').style.display = 'none';
                
                // Add cache-busting parameter
                const timestamp = new Date().getTime();
                const response = await fetch(`../../app/backend/api/api/get_latest_data.php?_=${timestamp}&records=1`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                
                const data = await response.json();
                
                if (Array.isArray(data) && data.length > 0) {
                    updateDashboard(data[0]);
                } else if (typeof data === 'object') {
                    updateDashboard(data);
                } else {
                    showError('Invalid data format received from server');
                }
            } catch (error) {
                console.error('Error fetching data:', error);
                showError('Failed to fetch data: ' + error.message);
            }
        }

        // Fetch data on page load
        document.addEventListener('DOMContentLoaded', () => {
            fetchData();
            
            // Set up refresh button
            document.getElementById('refresh-btn').addEventListener('click', fetchData);
            
            // Set up auto-refresh every 10 seconds
            setInterval(fetchData, 10000);
        });
    </script>
</body>
</html>
