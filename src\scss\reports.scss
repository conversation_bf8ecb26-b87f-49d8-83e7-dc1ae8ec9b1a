@import "./src/scss/variables.scss";@import 'variables';

// Reports Page Styles
.reports-container {
  background: rgba(30, 41, 59, 0.7);
  backdrop-filter: blur(10px);
  border-radius: $border-radius-lg;
  box-shadow: $futuristic-shadow;
  padding: 25px;
  margin-bottom: 25px;
  border: 1px solid $futuristic-border;
  transition: $transition-normal;

  &:hover {
    box-shadow: $futuristic-glow;
  }
}

// Report Generator
.report-generator {
  margin-bottom: 30px;
}

.report-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  padding: 20px;
  background: rgba(15, 23, 42, 0.7);
  border-radius: $border-radius-md;
  border: 1px solid $futuristic-border;
}

.control-group {
  display: flex;
  flex-direction: column;

  label {
    color: $futuristic-text-secondary;
    font-size: $font-size-sm;
    font-weight: 500;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
  }

  select,
  input {
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid $futuristic-border;
    border-radius: $border-radius-sm;
    color: $futuristic-text;
    padding: 10px 12px;
    font-size: $font-size-sm;
    transition: $transition-fast;

    &:focus {
      outline: none;
      border-color: $futuristic-accent-1;
      box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.2);
    }

    &:hover {
      border-color: rgba(148, 163, 184, 0.4);
    }
  }
}

// Section Headers
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid $futuristic-border;

  h3 {
    display: flex;
    align-items: center;
    color: $futuristic-text;
    font-size: $font-size-lg;
    font-weight: 600;
    margin: 0;

    .material-icons-round {
      margin-right: 10px;
      color: $futuristic-accent-1;
    }
  }
}

.section-actions {
  display: flex;
  gap: 10px;
}

// Report Templates
.report-templates {
  margin-bottom: 30px;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.template-card {
  background: rgba(15, 23, 42, 0.7);
  border-radius: $border-radius-md;
  padding: 20px;
  border: 1px solid $futuristic-border;
  cursor: pointer;
  transition: $transition-normal;
  display: flex;
  align-items: center;

  &:hover {
    background: rgba(15, 23, 42, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border-color: $futuristic-accent-1;
  }
}

.template-icon {
  width: 50px;
  height: 50px;
  border-radius: $border-radius-md;
  background: rgba(6, 182, 212, 0.15);
  color: $futuristic-accent-1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;

  .material-icons-round {
    font-size: 24px;
  }
}

.template-content {
  flex: 1;

  h4 {
    color: $futuristic-text;
    font-size: $font-size-md;
    font-weight: 600;
    margin: 0 0 5px 0;
  }

  p {
    color: $futuristic-text-secondary;
    font-size: $font-size-sm;
    margin: 0;
    line-height: 1.4;
  }
}

.template-actions {
  .action-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: $border-radius-sm;
    background: rgba(6, 182, 212, 0.15);
    color: $futuristic-accent-1;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: $transition-fast;

    &:hover {
      background: rgba(6, 182, 212, 0.25);
      transform: scale(1.1);
    }

    .material-icons-round {
      font-size: 20px;
    }
  }
}

// Scheduled Reports
.scheduled-reports {
  margin-bottom: 30px;
}

.schedules-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.schedule-item {
  background: rgba(15, 23, 42, 0.7);
  border-radius: $border-radius-md;
  padding: 20px;
  border: 1px solid $futuristic-border;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: $transition-normal;

  &:hover {
    background: rgba(15, 23, 42, 0.9);
    transform: translateX(5px);
  }
}

.schedule-info {
  flex: 1;

  h4 {
    color: $futuristic-text;
    font-size: $font-size-md;
    font-weight: 600;
    margin: 0 0 8px 0;
  }
}

.schedule-details {
  display: flex;
  gap: 15px;
  margin-bottom: 5px;
  font-size: $font-size-sm;

  .schedule-frequency {
    color: $futuristic-accent-1;
  }

  .schedule-format {
    color: $futuristic-text-secondary;
  }

  .schedule-status {
    padding: 2px 6px;
    border-radius: $border-radius-sm;
    font-size: $font-size-xs;
    font-weight: 500;

    &.active {
      background: rgba(16, 185, 129, 0.15);
      color: #10b981;
    }

    &.paused {
      background: rgba(245, 158, 11, 0.15);
      color: #f59e0b;
    }
  }
}

.schedule-next {
  font-size: $font-size-xs;
  color: $futuristic-text-secondary;
  font-family: 'JetBrains Mono', monospace;
}

.schedule-actions {
  display: flex;
  gap: 8px;

  .action-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: $border-radius-sm;
    background: rgba(30, 41, 59, 0.7);
    color: $futuristic-text-secondary;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: $transition-fast;

    &:hover {
      background: rgba(6, 182, 212, 0.15);
      color: $futuristic-accent-1;
    }

    &.danger:hover {
      background: rgba(239, 68, 68, 0.15);
      color: #ef4444;
    }

    .material-icons-round {
      font-size: 18px;
    }
  }
}

// Recent Reports
.recent-reports {
  margin-bottom: 30px;
}

.reports-table-container {
  background: rgba(15, 23, 42, 0.7);
  border-radius: $border-radius-md;
  border: 1px solid $futuristic-border;
  overflow: hidden;
  margin-bottom: 20px;
}

.reports-table {
  width: 100%;
  border-collapse: collapse;

  th,
  td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  }

  th {
    background: rgba(30, 41, 59, 0.7);
    color: $futuristic-text;
    font-weight: 600;
    font-size: $font-size-sm;
    letter-spacing: 0.5px;
  }

  td {
    color: $futuristic-text-secondary;
    font-size: $font-size-sm;
  }

  tr {
    transition: $transition-fast;

    &:hover {
      background: rgba(30, 41, 59, 0.5);
    }
  }
}

.status-badge {
  padding: 4px 8px;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &.completed {
    background: rgba(16, 185, 129, 0.15);
    color: #10b981;
  }

  &.processing {
    background: rgba(245, 158, 11, 0.15);
    color: #f59e0b;
  }

  &.failed {
    background: rgba(239, 68, 68, 0.15);
    color: #ef4444;
  }
}

.action-buttons {
  display: flex;
  gap: 5px;

  .action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: $border-radius-sm;
    background: rgba(30, 41, 59, 0.7);
    color: $futuristic-text-secondary;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: $transition-fast;

    &:hover {
      background: rgba(6, 182, 212, 0.15);
      color: $futuristic-accent-1;
    }

    &.danger:hover {
      background: rgba(239, 68, 68, 0.15);
      color: #ef4444;
    }

    .material-icons-round {
      font-size: 16px;
    }
  }
}

// Report Analytics
.report-analytics {
  margin-bottom: 30px;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.analytics-card {
  background: rgba(15, 23, 42, 0.7);
  border-radius: $border-radius-md;
  padding: 20px;
  border: 1px solid $futuristic-border;
  display: flex;
  align-items: center;
  transition: $transition-normal;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }
}

.analytics-icon {
  width: 50px;
  height: 50px;
  border-radius: $border-radius-md;
  background: rgba(6, 182, 212, 0.15);
  color: $futuristic-accent-1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;

  .material-icons-round {
    font-size: 24px;
  }
}

.analytics-content {
  flex: 1;
}

.analytics-value {
  font-size: $font-size-xl;
  font-weight: 700;
  color: $futuristic-text;
  margin-bottom: 4px;
}

.analytics-title {
  font-size: $font-size-sm;
  color: $futuristic-text-secondary;
  margin-bottom: 4px;
}

.analytics-change {
  display: flex;
  align-items: center;
  font-size: $font-size-xs;
  font-weight: 500;

  &.up {
    color: #10b981;
  }

  &.down {
    color: #ef4444;
  }

  .material-icons-round {
    font-size: 16px;
    margin-right: 4px;
  }
}

// Report Generation Modal
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;
}

.modal-content {
  background: rgba(30, 41, 59, 0.95);
  border-radius: $border-radius-lg;
  border: 1px solid $futuristic-border;
  box-shadow: $futuristic-shadow;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid $futuristic-border;

  h4 {
    color: $futuristic-text;
    margin: 0;
    font-size: $font-size-lg;
    font-weight: 600;
  }
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: $border-radius-sm;
  background: rgba(239, 68, 68, 0.15);
  color: #ef4444;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: $transition-fast;

  &:hover {
    background: rgba(239, 68, 68, 0.25);
  }
}

.modal-body {
  padding: 20px;
}

.progress-container {
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(148, 163, 184, 0.2);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 15px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, $futuristic-accent-1, $futuristic-accent-2);
  border-radius: 4px;
  transition: width 0.3s ease;
  width: 0%;
}

.progress-text {
  color: $futuristic-text-secondary;
  font-size: $font-size-sm;
}

// Responsive Design
@media (max-width: $breakpoint-md) {
  .report-controls {
    grid-template-columns: 1fr;
  }

  .templates-grid {
    grid-template-columns: 1fr;
  }

  .analytics-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .schedule-item {
    flex-direction: column;
    align-items: flex-start;

    .schedule-actions {
      margin-top: 15px;
      align-self: flex-end;
    }
  }

  .reports-table {
    font-size: $font-size-xs;

    th,
    td {
      padding: 10px 8px;
    }
  }
}

@media (max-width: $breakpoint-sm) {
  .analytics-grid {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;

    .section-actions {
      margin-top: 10px;
    }
  }

  .template-card {
    flex-direction: column;
    text-align: center;

    .template-icon {
      margin-right: 0;
      margin-bottom: 15px;
    }

    .template-actions {
      margin-top: 15px;
    }
  }
}
