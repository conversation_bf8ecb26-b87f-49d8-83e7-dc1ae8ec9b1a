// Import styles
import '../scss/main.scss';
import '../scss/stability.scss';

// Import utilities from main
import { utils } from './main';
import { DateTime } from 'luxon';
import Chart from 'chart.js/auto';

// DOM Elements
let elements = {};

// State
const state = {
    analysisType: 'comprehensive',
    timeRange: '1h',
    analysisData: null,
    charts: {},
    isLoading: false
};

/**
 * Initialize the stability analysis page
 */
function initStabilityAnalysis() {
    // Get DOM elements
    elements = {
        analysisTypeButtons: document.querySelectorAll('.analysis-type-btn'),
        timeRangeSelect: document.getElementById('timeRange'),
        runAnalysisBtn: document.getElementById('runAnalysisBtn'),
        metricsContainer: document.getElementById('metricsContainer'),
        chartsContainer: document.getElementById('chartsContainer'),
        recommendationsContainer: document.getElementById('recommendationsContainer'),
        loadingIndicator: document.getElementById('loadingIndicator'),
        messageContainer: document.getElementById('messageContainer')
    };

    // Bind events
    bindEvents();
    
    // Run initial analysis
    runStabilityAnalysis();
}

/**
 * Bind event listeners
 */
function bindEvents() {
    // Analysis type buttons
    if (elements.analysisTypeButtons) {
        elements.analysisTypeButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                setActiveAnalysisType(btn.dataset.type);
            });
        });
    }
    
    // Time range selector
    if (elements.timeRangeSelect) {
        elements.timeRangeSelect.addEventListener('change', (e) => {
            state.timeRange = e.target.value;
        });
    }
    
    // Run analysis button
    if (elements.runAnalysisBtn) {
        elements.runAnalysisBtn.addEventListener('click', runStabilityAnalysis);
    }
}

/**
 * Set active analysis type
 */
function setActiveAnalysisType(type) {
    state.analysisType = type;
    
    // Update button states
    elements.analysisTypeButtons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.type === type) {
            btn.classList.add('active');
        }
    });
}

/**
 * Run stability analysis
 */
async function runStabilityAnalysis() {
    if (state.isLoading) return;
    
    try {
        state.isLoading = true;
        showLoading(true);
        clearMessage();
        
        // Calculate time range
        const timeRange = calculateTimeRange(state.timeRange);
        
        // Fetch analysis data
        const response = await fetch(
            `../backend/stability_analysis.php?type=${state.analysisType}&start=${encodeURIComponent(timeRange.start)}&end=${encodeURIComponent(timeRange.end)}`
        );
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.error) {
            throw new Error(data.error);
        }
        
        state.analysisData = data;
        
        // Render analysis results
        renderAnalysisResults(data);
        
        showMessage('Stability analysis completed successfully', 'success');
        
    } catch (error) {
        console.error('Error running stability analysis:', error);
        showMessage(`Error: ${error.message}`, 'error');
    } finally {
        state.isLoading = false;
        showLoading(false);
    }
}

/**
 * Calculate time range based on selection
 */
function calculateTimeRange(range) {
    const now = DateTime.now().setZone('Asia/Kolkata');
    let start = now;
    
    switch(range) {
        case '1h':
            start = now.minus({ hours: 1 });
            break;
        case '6h':
            start = now.minus({ hours: 6 });
            break;
        case '24h':
            start = now.minus({ hours: 24 });
            break;
        case '7d':
            start = now.minus({ days: 7 });
            break;
        case '30d':
            start = now.minus({ days: 30 });
            break;
        default:
            start = now.minus({ hours: 1 });
    }
    
    return {
        start: start.toFormat('yyyy-MM-dd HH:mm:ss'),
        end: now.toFormat('yyyy-MM-dd HH:mm:ss')
    };
}

/**
 * Render analysis results
 */
function renderAnalysisResults(data) {
    if (state.analysisType === 'comprehensive') {
        renderComprehensiveAnalysis(data);
    } else {
        renderSpecificAnalysis(data, state.analysisType);
    }
}

/**
 * Render comprehensive analysis
 */
function renderComprehensiveAnalysis(data) {
    // Render stability metrics
    renderStabilityMetrics(data);
    
    // Render charts
    renderAnalysisCharts(data);
    
    // Render recommendations
    renderRecommendations(data.recommendations);
}

/**
 * Render stability metrics
 */
function renderStabilityMetrics(data) {
    if (!elements.metricsContainer) return;
    
    const overallScore = data.stability_score || { score: 0, grade: 'N/A' };
    
    let html = `
        <div class="stability-metric-card">
            <div class="metric-header">
                <div class="metric-icon">
                    <span class="material-icons-round">analytics</span>
                </div>
                <h3 class="metric-title">Overall Stability Score</h3>
            </div>
            <div class="stability-score-indicator" style="--score-angle: ${(overallScore.score / 100) * 360}deg;">
                <div class="score-circle">
                    <div class="score-text">
                        <div class="score-number">${overallScore.score}</div>
                        <div class="score-label">Score</div>
                    </div>
                </div>
            </div>
            <div class="metric-description">Grade: ${overallScore.grade}</div>
        </div>
    `;
    
    // Add individual stability metrics
    const metrics = [
        { key: 'voltage_stability', title: 'Voltage Stability', icon: 'bolt' },
        { key: 'current_stability', title: 'Current Stability', icon: 'electric_bolt' },
        { key: 'frequency_stability', title: 'Frequency Stability', icon: 'speed' },
        { key: 'power_stability', title: 'Power Stability', icon: 'power' }
    ];
    
    metrics.forEach(metric => {
        const metricData = data[metric.key];
        if (metricData) {
            html += renderMetricCard(metric, metricData);
        }
    });
    
    elements.metricsContainer.innerHTML = html;
}

/**
 * Render individual metric card
 */
function renderMetricCard(metric, data) {
    // Extract key statistics from the metric data
    let score = 'N/A';
    let details = '';
    
    if (data.voltage_1 && data.voltage_1.stability) {
        score = data.voltage_1.stability.stability_score || 'N/A';
    } else if (data.statistics) {
        score = Math.round(100 - (data.statistics.coefficient_of_variation || 0));
    }
    
    return `
        <div class="stability-metric-card">
            <div class="metric-header">
                <div class="metric-icon">
                    <span class="material-icons-round">${metric.icon}</span>
                </div>
                <h3 class="metric-title">${metric.title}</h3>
            </div>
            <div class="metric-value">${score}${typeof score === 'number' ? '%' : ''}</div>
            <div class="metric-description">
                ${getMetricDescription(metric.key, data)}
            </div>
            <div class="metric-details">
                ${renderMetricDetails(data)}
            </div>
        </div>
    `;
}

/**
 * Get metric description
 */
function getMetricDescription(key, data) {
    switch(key) {
        case 'voltage_stability':
            return 'Voltage stability across all three phases';
        case 'current_stability':
            return 'Current stability and load balance';
        case 'frequency_stability':
            return 'System frequency stability and quality';
        case 'power_stability':
            return 'Power factor and total power stability';
        default:
            return 'Stability analysis metric';
    }
}

/**
 * Render metric details
 */
function renderMetricDetails(data) {
    let html = '';
    
    if (data.statistics) {
        html += `
            <div class="metric-detail">
                <span class="metric-detail-label">Mean</span>
                <span class="metric-detail-value">${utils.formatNumber(data.statistics.mean)}</span>
            </div>
            <div class="metric-detail">
                <span class="metric-detail-label">Std Dev</span>
                <span class="metric-detail-value">${utils.formatNumber(data.statistics.std_dev)}</span>
            </div>
        `;
    }
    
    if (data.phase_balance) {
        html += `
            <div class="metric-detail">
                <span class="metric-detail-label">Balance</span>
                <span class="metric-detail-value">${data.phase_balance.balance_quality}</span>
            </div>
        `;
    }
    
    return html;
}

/**
 * Render analysis charts
 */
function renderAnalysisCharts(data) {
    if (!elements.chartsContainer) return;
    
    let html = `
        <div class="analysis-chart-card">
            <div class="chart-header">
                <h4 class="chart-title">Stability Trends</h4>
            </div>
            <div class="chart-container">
                <canvas id="stabilityTrendChart"></canvas>
            </div>
        </div>
        
        <div class="analysis-chart-card">
            <div class="chart-header">
                <h4 class="chart-title">Phase Balance</h4>
            </div>
            <div class="chart-container">
                <canvas id="phaseBalanceChart"></canvas>
            </div>
        </div>
    `;
    
    elements.chartsContainer.innerHTML = html;
    
    // Create charts
    setTimeout(() => {
        createStabilityTrendChart(data);
        createPhaseBalanceChart(data);
    }, 100);
}

/**
 * Create stability trend chart
 */
function createStabilityTrendChart(data) {
    const canvas = document.getElementById('stabilityTrendChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Destroy existing chart
    if (state.charts.stabilityTrend) {
        state.charts.stabilityTrend.destroy();
    }
    
    const chartData = {
        labels: ['Voltage', 'Current', 'Power', 'Frequency'],
        datasets: [{
            label: 'Stability Score',
            data: [
                data.stability_score?.factors?.voltage_stability || 0,
                data.stability_score?.factors?.current_stability || 0,
                data.stability_score?.factors?.power_stability || 0,
                data.stability_score?.factors?.frequency_stability || 0
            ],
            backgroundColor: 'rgba(6, 182, 212, 0.2)',
            borderColor: '#06b6d4',
            borderWidth: 2,
            tension: 0.4
        }]
    };
    
    state.charts.stabilityTrend = new Chart(ctx, {
        type: 'radar',
        data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: '#f8fafc'
                    }
                }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100,
                    grid: {
                        color: 'rgba(148, 163, 184, 0.15)'
                    },
                    pointLabels: {
                        color: '#cbd5e1'
                    },
                    ticks: {
                        color: '#64748b'
                    }
                }
            }
        }
    });
}

/**
 * Create phase balance chart
 */
function createPhaseBalanceChart(data) {
    const canvas = document.getElementById('phaseBalanceChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Destroy existing chart
    if (state.charts.phaseBalance) {
        state.charts.phaseBalance.destroy();
    }
    
    const voltageBalance = data.load_balance?.voltage_balance?.phase_averages || {};
    
    const chartData = {
        labels: ['Phase 1', 'Phase 2', 'Phase 3'],
        datasets: [{
            label: 'Voltage (V)',
            data: [
                voltageBalance.voltage_1 || 0,
                voltageBalance.voltage_2 || 0,
                voltageBalance.voltage_3 || 0
            ],
            backgroundColor: ['#06b6d4', '#3b82f6', '#8b5cf6'],
            borderColor: ['#06b6d4', '#3b82f6', '#8b5cf6'],
            borderWidth: 2
        }]
    };
    
    state.charts.phaseBalance = new Chart(ctx, {
        type: 'bar',
        data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: '#f8fafc'
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        color: 'rgba(148, 163, 184, 0.15)'
                    },
                    ticks: {
                        color: '#cbd5e1'
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(148, 163, 184, 0.15)'
                    },
                    ticks: {
                        color: '#cbd5e1'
                    }
                }
            }
        }
    });
}

/**
 * Render recommendations
 */
function renderRecommendations(recommendations) {
    if (!elements.recommendationsContainer || !recommendations) return;
    
    let html = `
        <h3 class="recommendations-title">
            <span class="material-icons-round">lightbulb</span>
            Recommendations
        </h3>
        <div class="recommendations-grid">
    `;
    
    if (recommendations.immediate_actions && recommendations.immediate_actions.length > 0) {
        html += `
            <div class="recommendation-category">
                <h4>Immediate Actions</h4>
                <ul class="recommendation-list">
                    ${recommendations.immediate_actions.map(action => `<li>${action}</li>`).join('')}
                </ul>
            </div>
        `;
    }
    
    if (recommendations.preventive_measures && recommendations.preventive_measures.length > 0) {
        html += `
            <div class="recommendation-category">
                <h4>Preventive Measures</h4>
                <ul class="recommendation-list">
                    ${recommendations.preventive_measures.map(measure => `<li>${measure}</li>`).join('')}
                </ul>
            </div>
        `;
    }
    
    if (recommendations.optimization_opportunities && recommendations.optimization_opportunities.length > 0) {
        html += `
            <div class="recommendation-category">
                <h4>Optimization Opportunities</h4>
                <ul class="recommendation-list">
                    ${recommendations.optimization_opportunities.map(opportunity => `<li>${opportunity}</li>`).join('')}
                </ul>
            </div>
        `;
    }
    
    html += '</div>';
    
    elements.recommendationsContainer.innerHTML = html;
}

/**
 * Render specific analysis (voltage, current, etc.)
 */
function renderSpecificAnalysis(data, type) {
    // Implementation for specific analysis types
    renderStabilityMetrics({ [type]: data });
}

/**
 * Show loading indicator
 */
function showLoading(show) {
    if (elements.loadingIndicator) {
        elements.loadingIndicator.style.display = show ? 'flex' : 'none';
    }
}

/**
 * Show message
 */
function showMessage(message, type = 'info') {
    if (elements.messageContainer) {
        utils.showMessage(message, type, elements.messageContainer);
    }
}

/**
 * Clear message
 */
function clearMessage() {
    if (elements.messageContainer) {
        elements.messageContainer.innerHTML = '';
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initStabilityAnalysis);
