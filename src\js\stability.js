// Import styles
import '../scss/main.scss';
import '../scss/stability.scss';

// Import utilities from main
import { utils } from './main';
import { DateTime } from 'luxon';
import Chart from 'chart.js/auto';

// DOM Elements
let elements = {};

// State
const state = {
    analysisType: 'comprehensive',
    timeRange: '1h',
    analysisData: null,
    charts: {},
    isLoading: false
};

/**
 * Initialize the stability analysis page
 */
function initStabilityAnalysis() {
    // Get DOM elements
    elements = {
        analysisTypeButtons: document.querySelectorAll('.analysis-type-btn'),
        timeRangeSelect: document.getElementById('timeRange'),
        startDate: document.getElementById('startDate'),
        endDate: document.getElementById('endDate'),
        runAnalysisBtn: document.getElementById('runAnalysisBtn'),
        metricsContainer: document.getElementById('metricsContainer'),
        chartsContainer: document.getElementById('chartsContainer'),
        recommendationsContainer: document.getElementById('recommendationsContainer'),
        loadingIndicator: document.getElementById('loadingIndicator'),
        messageContainer: document.getElementById('messageContainer')
    };

    // Set default date range to current time
    setTimeRange('1h');

    // Bind events
    bindEvents();
    
    // Run initial analysis
    runStabilityAnalysis();
}

/**
 * Bind event listeners
 */
function bindEvents() {
    // Analysis type buttons
    if (elements.analysisTypeButtons) {
        elements.analysisTypeButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                setActiveAnalysisType(btn.dataset.type);
            });
        });
    }
    
    // Time range selector
    if (elements.timeRangeSelect) {
        elements.timeRangeSelect.addEventListener('change', (e) => {
            state.timeRange = e.target.value;
            if (e.target.value !== 'custom') {
                setTimeRange(e.target.value);
            }
        });
    }
    
    // Run analysis button
    if (elements.runAnalysisBtn) {
        elements.runAnalysisBtn.addEventListener('click', runStabilityAnalysis);
    }
}

/**
 * Set active analysis type
 */
function setActiveAnalysisType(type) {
    state.analysisType = type;
    
    // Update button states
    elements.analysisTypeButtons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.type === type) {
            btn.classList.add('active');
        }
    });
}

/**
 * Run stability analysis
 */
async function runStabilityAnalysis() {
    if (state.isLoading) return;
    
    try {
        state.isLoading = true;
        showLoading(true);
        clearMessage();
        
        // Calculate time range
        const timeRange = calculateTimeRange();
        
        // Fetch analysis data
        const response = await fetch(
            `../backend/stability_analysis.php?type=${state.analysisType}&start=${encodeURIComponent(timeRange.start)}&end=${encodeURIComponent(timeRange.end)}`
        );
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.error) {
            throw new Error(data.error);
        }
        
        state.analysisData = data;
        
        // Render analysis results
        renderAnalysisResults(data);
        
        showMessage('Stability analysis completed successfully', 'success');
        
    } catch (error) {
        console.error('Error running stability analysis:', error);
        showMessage(`Error: ${error.message}`, 'error');
    } finally {
        state.isLoading = false;
        showLoading(false);
    }
}

/**
 * Set time range based on quick filter
 * @param {string} range - Time range identifier
 */
function setTimeRange(range) {
    // Get current time
    const now = DateTime.now().setZone('Asia/Kolkata');

    // Set end date to current time
    const endDate = now;

    // Set start date based on range
    let startDate = now;

    switch(range) {
        case '1h':
            startDate = now.minus({ hours: 1 });
            break;
        case '6h':
            startDate = now.minus({ hours: 6 });
            break;
        case '24h':
            startDate = now.minus({ hours: 24 });
            break;
        case '7d':
            startDate = now.minus({ days: 7 });
            break;
        case '30d':
            startDate = now.minus({ days: 30 });
            break;
        case 'custom':
            // Don't change dates, just return
            return;
        default:
            startDate = now.minus({ hours: 1 });
    }

    // Format dates for the input fields
    if (elements.startDate) {
        elements.startDate.value = startDate.toFormat("yyyy-MM-dd'T'HH:mm");
    }

    if (elements.endDate) {
        elements.endDate.value = endDate.toFormat("yyyy-MM-dd'T'HH:mm");
    }
}

/**
 * Calculate time range from form inputs
 */
function calculateTimeRange() {
    if (!elements.startDate || !elements.endDate) {
        // Fallback to quick range
        const now = DateTime.now().setZone('Asia/Kolkata');
        let start = now.minus({ hours: 1 });

        return {
            start: start.toFormat('yyyy-MM-dd HH:mm:ss'),
            end: now.toFormat('yyyy-MM-dd HH:mm:ss')
        };
    }

    const startDate = elements.startDate.value;
    const endDate = elements.endDate.value;

    if (!startDate || !endDate) {
        throw new Error('Please select both start and end dates');
    }

    // Parse dates from input fields
    const startDateObj = DateTime.fromISO(startDate);
    const endDateObj = DateTime.fromISO(endDate);

    if (!startDateObj.isValid || !endDateObj.isValid) {
        throw new Error('Invalid date format');
    }

    if (startDateObj >= endDateObj) {
        throw new Error('Start date must be before end date');
    }

    return {
        start: startDateObj.toFormat('yyyy-MM-dd HH:mm:ss'),
        end: endDateObj.toFormat('yyyy-MM-dd HH:mm:ss')
    };
}

/**
 * Render analysis results
 */
function renderAnalysisResults(data) {
    if (state.analysisType === 'comprehensive') {
        renderComprehensiveAnalysis(data);
    } else {
        renderSpecificAnalysis(data, state.analysisType);
    }
}

/**
 * Render comprehensive analysis
 */
function renderComprehensiveAnalysis(data) {
    // Render stability metrics
    renderStabilityMetrics(data);
    
    // Render charts
    renderAnalysisCharts(data);
    
    // Render recommendations
    renderRecommendations(data.recommendations);
}

/**
 * Render stability metrics
 */
function renderStabilityMetrics(data) {
    if (!elements.metricsContainer) return;
    
    const overallScore = data.stability_score || { score: 0, grade: 'N/A' };
    
    let html = `
        <div class="stability-metric-card">
            <div class="metric-header">
                <div class="metric-icon">
                    <span class="material-icons-round">analytics</span>
                </div>
                <h3 class="metric-title">Overall Stability Score</h3>
            </div>
            <div class="stability-score-indicator" style="--score-angle: ${(overallScore.score / 100) * 360}deg;">
                <div class="score-circle">
                    <div class="score-text">
                        <div class="score-number">${overallScore.score}</div>
                        <div class="score-label">Score</div>
                    </div>
                </div>
            </div>
            <div class="metric-description">Grade: ${overallScore.grade}</div>
        </div>
    `;
    
    // Add individual stability metrics
    const metrics = [
        { key: 'voltage_stability', title: 'Voltage Stability', icon: 'bolt' },
        { key: 'current_stability', title: 'Current Stability', icon: 'electric_bolt' },
        { key: 'frequency_stability', title: 'Frequency Stability', icon: 'speed' },
        { key: 'power_stability', title: 'Power Stability', icon: 'power' }
    ];
    
    metrics.forEach(metric => {
        const metricData = data[metric.key];
        if (metricData) {
            html += renderMetricCard(metric, metricData);
        }
    });
    
    elements.metricsContainer.innerHTML = html;
}

/**
 * Render individual metric card
 */
function renderMetricCard(metric, data) {
    // Extract key statistics from the metric data
    let score = 'N/A';
    let details = '';
    
    if (data.voltage_1 && data.voltage_1.stability) {
        score = data.voltage_1.stability.stability_score || 'N/A';
    } else if (data.statistics) {
        score = Math.round(100 - (data.statistics.coefficient_of_variation || 0));
    }
    
    return `
        <div class="stability-metric-card">
            <div class="metric-header">
                <div class="metric-icon">
                    <span class="material-icons-round">${metric.icon}</span>
                </div>
                <h3 class="metric-title">${metric.title}</h3>
            </div>
            <div class="metric-value">${score}${typeof score === 'number' ? '%' : ''}</div>
            <div class="metric-description">
                ${getMetricDescription(metric.key, data)}
            </div>
            <div class="metric-details">
                ${renderMetricDetails(data)}
            </div>
        </div>
    `;
}

/**
 * Get metric description
 */
function getMetricDescription(key, data) {
    switch(key) {
        case 'voltage_stability':
            return 'Voltage stability across all three phases';
        case 'current_stability':
            return 'Current stability and load balance';
        case 'frequency_stability':
            return 'System frequency stability and quality';
        case 'power_stability':
            return 'Power factor and total power stability';
        default:
            return 'Stability analysis metric';
    }
}

/**
 * Render metric details
 */
function renderMetricDetails(data) {
    let html = '';
    
    if (data.statistics) {
        html += `
            <div class="metric-detail">
                <span class="metric-detail-label">Mean</span>
                <span class="metric-detail-value">${utils.formatNumber(data.statistics.mean)}</span>
            </div>
            <div class="metric-detail">
                <span class="metric-detail-label">Std Dev</span>
                <span class="metric-detail-value">${utils.formatNumber(data.statistics.std_dev)}</span>
            </div>
        `;
    }
    
    if (data.phase_balance) {
        html += `
            <div class="metric-detail">
                <span class="metric-detail-label">Balance</span>
                <span class="metric-detail-value">${data.phase_balance.balance_quality}</span>
            </div>
        `;
    }
    
    return html;
}

/**
 * Render analysis charts
 */
function renderAnalysisCharts(data) {
    if (!elements.chartsContainer) return;
    
    let html = `
        <div class="analysis-chart-card">
            <div class="chart-header">
                <h4 class="chart-title">Stability Trends</h4>
            </div>
            <div class="chart-container">
                <canvas id="stabilityTrendChart"></canvas>
            </div>
        </div>
        
        <div class="analysis-chart-card">
            <div class="chart-header">
                <h4 class="chart-title">Phase Balance</h4>
            </div>
            <div class="chart-container">
                <canvas id="phaseBalanceChart"></canvas>
            </div>
        </div>
    `;
    
    elements.chartsContainer.innerHTML = html;
    
    // Create charts
    setTimeout(() => {
        createStabilityTrendChart(data);
        createPhaseBalanceChart(data);
    }, 100);
}

/**
 * Create stability trend chart
 */
function createStabilityTrendChart(data) {
    const canvas = document.getElementById('stabilityTrendChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Destroy existing chart
    if (state.charts.stabilityTrend) {
        state.charts.stabilityTrend.destroy();
    }
    
    const chartData = {
        labels: ['Voltage', 'Current', 'Power', 'Frequency'],
        datasets: [{
            label: 'Stability Score',
            data: [
                data.stability_score?.factors?.voltage_stability || 0,
                data.stability_score?.factors?.current_stability || 0,
                data.stability_score?.factors?.power_stability || 0,
                data.stability_score?.factors?.frequency_stability || 0
            ],
            backgroundColor: 'rgba(6, 182, 212, 0.2)',
            borderColor: '#06b6d4',
            borderWidth: 2,
            tension: 0.4
        }]
    };
    
    state.charts.stabilityTrend = new Chart(ctx, {
        type: 'radar',
        data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: '#f8fafc'
                    }
                }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100,
                    grid: {
                        color: 'rgba(148, 163, 184, 0.15)'
                    },
                    pointLabels: {
                        color: '#cbd5e1'
                    },
                    ticks: {
                        color: '#64748b'
                    }
                }
            }
        }
    });
}

/**
 * Create phase balance chart
 */
function createPhaseBalanceChart(data) {
    const canvas = document.getElementById('phaseBalanceChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Destroy existing chart
    if (state.charts.phaseBalance) {
        state.charts.phaseBalance.destroy();
    }
    
    const voltageBalance = data.load_balance?.voltage_balance?.phase_averages || {};
    
    const chartData = {
        labels: ['Phase 1', 'Phase 2', 'Phase 3'],
        datasets: [{
            label: 'Voltage (V)',
            data: [
                voltageBalance.voltage_1 || 0,
                voltageBalance.voltage_2 || 0,
                voltageBalance.voltage_3 || 0
            ],
            backgroundColor: ['#06b6d4', '#3b82f6', '#8b5cf6'],
            borderColor: ['#06b6d4', '#3b82f6', '#8b5cf6'],
            borderWidth: 2
        }]
    };
    
    state.charts.phaseBalance = new Chart(ctx, {
        type: 'bar',
        data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: '#f8fafc'
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        color: 'rgba(148, 163, 184, 0.15)'
                    },
                    ticks: {
                        color: '#cbd5e1'
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(148, 163, 184, 0.15)'
                    },
                    ticks: {
                        color: '#cbd5e1'
                    }
                }
            }
        }
    });
}

/**
 * Render recommendations
 */
function renderRecommendations(recommendations) {
    if (!elements.recommendationsContainer || !recommendations) return;
    
    let html = `
        <h3 class="recommendations-title">
            <span class="material-icons-round">lightbulb</span>
            Recommendations
        </h3>
        <div class="recommendations-grid">
    `;
    
    if (recommendations.immediate_actions && recommendations.immediate_actions.length > 0) {
        html += `
            <div class="recommendation-category">
                <h4>Immediate Actions</h4>
                <ul class="recommendation-list">
                    ${recommendations.immediate_actions.map(action => `<li>${action}</li>`).join('')}
                </ul>
            </div>
        `;
    }
    
    if (recommendations.preventive_measures && recommendations.preventive_measures.length > 0) {
        html += `
            <div class="recommendation-category">
                <h4>Preventive Measures</h4>
                <ul class="recommendation-list">
                    ${recommendations.preventive_measures.map(measure => `<li>${measure}</li>`).join('')}
                </ul>
            </div>
        `;
    }
    
    if (recommendations.optimization_opportunities && recommendations.optimization_opportunities.length > 0) {
        html += `
            <div class="recommendation-category">
                <h4>Optimization Opportunities</h4>
                <ul class="recommendation-list">
                    ${recommendations.optimization_opportunities.map(opportunity => `<li>${opportunity}</li>`).join('')}
                </ul>
            </div>
        `;
    }
    
    html += '</div>';
    
    elements.recommendationsContainer.innerHTML = html;
}

/**
 * Render specific analysis (voltage, current, etc.)
 */
function renderSpecificAnalysis(data, type) {
    switch(type) {
        case 'harmonics':
            renderHarmonicAnalysis(data);
            break;
        case 'power_quality':
            renderPowerQualityAnalysis(data);
            break;
        case 'predictive':
            renderPredictiveAnalysis(data);
            break;
        default:
            renderStabilityMetrics({ [type]: data });
    }
}

/**
 * Render harmonic analysis
 */
function renderHarmonicAnalysis(data) {
    if (!elements.metricsContainer) return;

    let html = '<h3>Harmonic Analysis Results</h3>';

    if (data.voltage) {
        html += '<div class="harmonic-section"><h4>Voltage THD Analysis</h4>';
        Object.entries(data.voltage).forEach(([phase, thdData]) => {
            const statusClass = thdData.compliant ? 'good' : 'warning';
            html += `
                <div class="stability-metric-card ${statusClass}">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons-round">graphic_eq</span>
                        </div>
                        <h3 class="metric-title">${phase.toUpperCase()} THD</h3>
                    </div>
                    <div class="metric-value">${thdData.estimated_thd}%</div>
                    <div class="metric-description">
                        Quality: ${thdData.quality} | Threshold: ${thdData.threshold}%
                    </div>
                    <div class="compliance-indicator ${thdData.compliant ? 'compliant' : 'non-compliant'}">
                        ${thdData.compliant ? '✓ Compliant' : '⚠ Non-Compliant'}
                    </div>
                </div>
            `;
        });
        html += '</div>';
    }

    if (data.current) {
        html += '<div class="harmonic-section"><h4>Current THD Analysis</h4>';
        Object.entries(data.current).forEach(([phase, thdData]) => {
            const statusClass = thdData.compliant ? 'good' : 'warning';
            html += `
                <div class="stability-metric-card ${statusClass}">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons-round">electric_bolt</span>
                        </div>
                        <h3 class="metric-title">${phase.toUpperCase()} THD</h3>
                    </div>
                    <div class="metric-value">${thdData.estimated_thd}%</div>
                    <div class="metric-description">
                        Quality: ${thdData.quality} | Threshold: ${thdData.threshold}%
                    </div>
                    <div class="compliance-indicator ${thdData.compliant ? 'compliant' : 'non-compliant'}">
                        ${thdData.compliant ? '✓ Compliant' : '⚠ Non-Compliant'}
                    </div>
                </div>
            `;
        });
        html += '</div>';
    }

    elements.metricsContainer.innerHTML = html;
}

/**
 * Render power quality analysis
 */
function renderPowerQualityAnalysis(data) {
    if (!elements.metricsContainer) return;

    let html = '<h3>Power Quality Assessment</h3>';

    // Voltage Regulation
    if (data.voltage_regulation) {
        html += '<div class="power-quality-section"><h4>Voltage Regulation</h4>';
        Object.entries(data.voltage_regulation).forEach(([phase, regulation]) => {
            const statusClass = regulation.quality === 'Good' ? 'good' :
                               regulation.quality === 'Fair' ? 'warning' : 'critical';
            html += `
                <div class="stability-metric-card ${statusClass}">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons-round">tune</span>
                        </div>
                        <h3 class="metric-title">${phase.replace('_', ' ').toUpperCase()}</h3>
                    </div>
                    <div class="metric-value">${regulation.regulation_percentage}%</div>
                    <div class="metric-description">
                        Quality: ${regulation.quality}<br>
                        Range: ${regulation.min_voltage}V - ${regulation.max_voltage}V
                    </div>
                </div>
            `;
        });
        html += '</div>';
    }

    // Frequency Regulation
    if (data.frequency_regulation) {
        const freq = data.frequency_regulation;
        const statusClass = freq.quality === 'Good' ? 'good' :
                           freq.quality === 'Fair' ? 'warning' : 'critical';
        html += `
            <div class="power-quality-section">
                <h4>Frequency Regulation</h4>
                <div class="stability-metric-card ${statusClass}">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons-round">speed</span>
                        </div>
                        <h3 class="metric-title">Frequency Stability</h3>
                    </div>
                    <div class="metric-value">${freq.max_deviation}Hz</div>
                    <div class="metric-description">
                        Quality: ${freq.quality} | Status: ${freq.stability}<br>
                        Range: ${freq.frequency_range[0]}Hz - ${freq.frequency_range[1]}Hz
                    </div>
                </div>
            </div>
        `;
    }

    // Voltage Unbalance
    if (data.voltage_unbalance) {
        const unbalance = data.voltage_unbalance;
        const statusClass = unbalance.compliant ? 'good' : 'critical';
        html += `
            <div class="power-quality-section">
                <h4>Voltage Unbalance</h4>
                <div class="stability-metric-card ${statusClass}">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons-round">balance</span>
                        </div>
                        <h3 class="metric-title">Phase Unbalance</h3>
                    </div>
                    <div class="metric-value">${unbalance.unbalance_percentage}%</div>
                    <div class="metric-description">
                        Quality: ${unbalance.quality}<br>
                        Average: ${unbalance.average_voltage}V
                    </div>
                    <div class="compliance-indicator ${unbalance.compliant ? 'compliant' : 'non-compliant'}">
                        ${unbalance.compliant ? '✓ NEMA Compliant' : '⚠ Exceeds 2% Limit'}
                    </div>
                </div>
            </div>
        `;
    }

    elements.metricsContainer.innerHTML = html;
}

/**
 * Render predictive analysis
 */
function renderPredictiveAnalysis(data) {
    if (!elements.metricsContainer) return;

    let html = '<h3>Predictive Maintenance Analysis</h3>';

    // Stress Indicators
    if (data.stress_indicators) {
        const stress = data.stress_indicators;
        const overallStressClass = stress.overall_stress > 20 ? 'critical' :
                                  stress.overall_stress > 10 ? 'warning' : 'good';

        html += `
            <div class="predictive-section">
                <h4>Equipment Stress Indicators</h4>
                <div class="stability-metric-card ${overallStressClass}">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons-round">warning</span>
                        </div>
                        <h3 class="metric-title">Overall Stress Level</h3>
                    </div>
                    <div class="metric-value">${stress.overall_stress.toFixed(1)}%</div>
                    <div class="metric-details">
                        <div class="metric-detail">
                            <span class="metric-detail-label">Voltage Stress</span>
                            <span class="metric-detail-value">${stress.voltage_stress.toFixed(1)}%</span>
                        </div>
                        <div class="metric-detail">
                            <span class="metric-detail-label">Current Stress</span>
                            <span class="metric-detail-value">${stress.current_stress.toFixed(1)}%</span>
                        </div>
                        <div class="metric-detail">
                            <span class="metric-detail-label">Thermal Stress</span>
                            <span class="metric-detail-value">${stress.thermal_stress.toFixed(1)}%</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Risk Assessment
    if (data.risk_assessment) {
        const risk = data.risk_assessment;
        const riskClass = risk.level === 'Critical' ? 'critical' :
                         risk.level === 'High' ? 'warning' : 'good';

        html += `
            <div class="predictive-section">
                <h4>Risk Assessment</h4>
                <div class="stability-metric-card ${riskClass}">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons-round">assessment</span>
                        </div>
                        <h3 class="metric-title">Maintenance Risk</h3>
                    </div>
                    <div class="metric-value">${risk.level}</div>
                    <div class="metric-description">
                        Probability: ${risk.probability}<br>
                        Impact: ${risk.impact}<br>
                        <strong>${risk.recommendation}</strong>
                    </div>
                </div>
            </div>
        `;
    }

    // Maintenance Schedule
    if (data.maintenance_schedule && data.maintenance_schedule.length > 0) {
        html += '<div class="predictive-section"><h4>Recommended Maintenance Schedule</h4>';
        data.maintenance_schedule.forEach(item => {
            const priorityClass = item.priority === 'High' ? 'critical' :
                                 item.priority === 'Medium' ? 'warning' : 'good';
            html += `
                <div class="maintenance-item ${priorityClass}">
                    <div class="maintenance-type">${item.type}</div>
                    <div class="maintenance-priority">Priority: ${item.priority}</div>
                    <div class="maintenance-timeframe">Timeframe: ${item.timeframe}</div>
                    <div class="maintenance-reason">Reason: ${item.reason}</div>
                </div>
            `;
        });
        html += '</div>';
    }

    elements.metricsContainer.innerHTML = html;
}

/**
 * Show loading indicator
 */
function showLoading(show) {
    if (elements.loadingIndicator) {
        elements.loadingIndicator.style.display = show ? 'flex' : 'none';
    }
}

/**
 * Show message
 */
function showMessage(message, type = 'info') {
    if (elements.messageContainer) {
        utils.showMessage(message, type, elements.messageContainer);
    }
}

/**
 * Clear message
 */
function clearMessage() {
    if (elements.messageContainer) {
        elements.messageContainer.innerHTML = '';
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initStabilityAnalysis);
