import{D as o,u as d,C as g}from"./main-00adf0b6.js";let a={};const r={analysisType:"comprehensive",timeRange:"1h",analysisData:null,charts:{},isLoading:!1};function C(){a={analysisTypeButtons:document.querySelectorAll(".analysis-type-btn"),timeRangeSelect:document.getElementById("timeRange"),startDate:document.getElementById("startDate"),endDate:document.getElementById("endDate"),runAnalysisBtn:document.getElementById("runAnalysisBtn"),metricsContainer:document.getElementById("metricsContainer"),chartsContainer:document.getElementById("chartsContainer"),recommendationsContainer:document.getElementById("recommendationsContainer"),loadingIndicator:document.getElementById("loadingIndicator"),messageContainer:document.getElementById("messageContainer")},_("1h"),w(),f()}function w(){a.analysisTypeButtons&&a.analysisTypeButtons.forEach(e=>{e.addEventListener("click",()=>{T(e.dataset.type)})}),a.timeRangeSelect&&a.timeRangeSelect.addEventListener("change",e=>{r.timeRange=e.target.value,e.target.value!=="custom"&&_(e.target.value)}),a.runAnalysisBtn&&a.runAnalysisBtn.addEventListener("click",f)}function T(e){r.analysisType=e,a.analysisTypeButtons.forEach(i=>{i.classList.remove("active"),i.dataset.type===e&&i.classList.add("active")})}async function f(){if(!r.isLoading)try{r.isLoading=!0,h(!0),x();const e=S(),i=await fetch(`../backend/stability_analysis.php?type=${r.analysisType}&start=${encodeURIComponent(e.start)}&end=${encodeURIComponent(e.end)}`);if(!i.ok)throw new Error(`HTTP error! Status: ${i.status}`);const t=await i.json();if(t.error)throw new Error(t.error);r.analysisData=t,M(t),b("Stability analysis completed successfully","success")}catch(e){console.error("Error running stability analysis:",e),b(`Error: ${e.message}`,"error")}finally{r.isLoading=!1,h(!1)}}function _(e){const i=o.now().setZone("Asia/Kolkata"),t=i;let s=i;switch(e){case"1h":s=i.minus({hours:1});break;case"6h":s=i.minus({hours:6});break;case"24h":s=i.minus({hours:24});break;case"7d":s=i.minus({days:7});break;case"30d":s=i.minus({days:30});break;case"custom":return;default:s=i.minus({hours:1})}a.startDate&&(a.startDate.value=s.toFormat("yyyy-MM-dd'T'HH:mm")),a.endDate&&(a.endDate.value=t.toFormat("yyyy-MM-dd'T'HH:mm"))}function S(){if(!a.startDate||!a.endDate){const n=o.now().setZone("Asia/Kolkata");return{start:n.minus({hours:1}).toFormat("yyyy-MM-dd HH:mm:ss"),end:n.toFormat("yyyy-MM-dd HH:mm:ss")}}const e=a.startDate.value,i=a.endDate.value;if(!e||!i)throw new Error("Please select both start and end dates");const t=o.fromISO(e),s=o.fromISO(i);if(!t.isValid||!s.isValid)throw new Error("Invalid date format");if(t>=s)throw new Error("Start date must be before end date");return{start:t.toFormat("yyyy-MM-dd HH:mm:ss"),end:s.toFormat("yyyy-MM-dd HH:mm:ss")}}function M(e){r.analysisType==="comprehensive"?A(e):I(e,r.analysisType)}function A(e){$(e),H(e),D(e.recommendations)}function $(e){if(!a.metricsContainer)return;const i=e.stability_score||{score:0,grade:"N/A"};let t=`
        <div class="stability-metric-card">
            <div class="metric-header">
                <div class="metric-icon">
                    <span class="material-icons-round">analytics</span>
                </div>
                <h3 class="metric-title">Overall Stability Score</h3>
            </div>
            <div class="stability-score-indicator" style="--score-angle: ${i.score/100*360}deg;">
                <div class="score-circle">
                    <div class="score-text">
                        <div class="score-number">${i.score}</div>
                        <div class="score-label">Score</div>
                    </div>
                </div>
            </div>
            <div class="metric-description">Grade: ${i.grade}</div>
        </div>
    `;[{key:"voltage_stability",title:"Voltage Stability",icon:"bolt"},{key:"current_stability",title:"Current Stability",icon:"electric_bolt"},{key:"frequency_stability",title:"Frequency Stability",icon:"speed"},{key:"power_stability",title:"Power Stability",icon:"power"}].forEach(n=>{const c=e[n.key];c&&(t+=E(n,c))}),a.metricsContainer.innerHTML=t}function E(e,i){let t="N/A";return i.voltage_1&&i.voltage_1.stability?t=i.voltage_1.stability.stability_score||"N/A":i.statistics&&(t=Math.round(100-(i.statistics.coefficient_of_variation||0))),`
        <div class="stability-metric-card">
            <div class="metric-header">
                <div class="metric-icon">
                    <span class="material-icons-round">${e.icon}</span>
                </div>
                <h3 class="metric-title">${e.title}</h3>
            </div>
            <div class="metric-value">${t}${typeof t=="number"?"%":""}</div>
            <div class="metric-description">
                ${k(e.key)}
            </div>
            <div class="metric-details">
                ${q(i)}
            </div>
        </div>
    `}function k(e,i){switch(e){case"voltage_stability":return"Voltage stability across all three phases";case"current_stability":return"Current stability and load balance";case"frequency_stability":return"System frequency stability and quality";case"power_stability":return"Power factor and total power stability";default:return"Stability analysis metric"}}function q(e){let i="";return e.statistics&&(i+=`
            <div class="metric-detail">
                <span class="metric-detail-label">Mean</span>
                <span class="metric-detail-value">${d.formatNumber(e.statistics.mean)}</span>
            </div>
            <div class="metric-detail">
                <span class="metric-detail-label">Std Dev</span>
                <span class="metric-detail-value">${d.formatNumber(e.statistics.std_dev)}</span>
            </div>
        `),e.phase_balance&&(i+=`
            <div class="metric-detail">
                <span class="metric-detail-label">Balance</span>
                <span class="metric-detail-value">${e.phase_balance.balance_quality}</span>
            </div>
        `),i}function H(e){if(!a.chartsContainer)return;let i=`
        <div class="analysis-chart-card">
            <div class="chart-header">
                <h4 class="chart-title">Stability Trends</h4>
            </div>
            <div class="chart-container">
                <canvas id="stabilityTrendChart"></canvas>
            </div>
        </div>
        
        <div class="analysis-chart-card">
            <div class="chart-header">
                <h4 class="chart-title">Phase Balance</h4>
            </div>
            <div class="chart-container">
                <canvas id="phaseBalanceChart"></canvas>
            </div>
        </div>
    `;a.chartsContainer.innerHTML=i,setTimeout(()=>{B(e),R(e)},100)}function B(e){var n,c,l,m,v,u,y,p;const i=document.getElementById("stabilityTrendChart");if(!i)return;const t=i.getContext("2d");r.charts.stabilityTrend&&r.charts.stabilityTrend.destroy();const s={labels:["Voltage","Current","Power","Frequency"],datasets:[{label:"Stability Score",data:[((c=(n=e.stability_score)==null?void 0:n.factors)==null?void 0:c.voltage_stability)||0,((m=(l=e.stability_score)==null?void 0:l.factors)==null?void 0:m.current_stability)||0,((u=(v=e.stability_score)==null?void 0:v.factors)==null?void 0:u.power_stability)||0,((p=(y=e.stability_score)==null?void 0:y.factors)==null?void 0:p.frequency_stability)||0],backgroundColor:"rgba(6, 182, 212, 0.2)",borderColor:"#06b6d4",borderWidth:2,tension:.4}]};r.charts.stabilityTrend=new g(t,{type:"radar",data:s,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{labels:{color:"#f8fafc"}}},scales:{r:{beginAtZero:!0,max:100,grid:{color:"rgba(148, 163, 184, 0.15)"},pointLabels:{color:"#cbd5e1"},ticks:{color:"#64748b"}}}}})}function R(e){var c,l;const i=document.getElementById("phaseBalanceChart");if(!i)return;const t=i.getContext("2d");r.charts.phaseBalance&&r.charts.phaseBalance.destroy();const s=((l=(c=e.load_balance)==null?void 0:c.voltage_balance)==null?void 0:l.phase_averages)||{},n={labels:["Phase 1","Phase 2","Phase 3"],datasets:[{label:"Voltage (V)",data:[s.voltage_1||0,s.voltage_2||0,s.voltage_3||0],backgroundColor:["#06b6d4","#3b82f6","#8b5cf6"],borderColor:["#06b6d4","#3b82f6","#8b5cf6"],borderWidth:2}]};r.charts.phaseBalance=new g(t,{type:"bar",data:n,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{labels:{color:"#f8fafc"}}},scales:{x:{grid:{color:"rgba(148, 163, 184, 0.15)"},ticks:{color:"#cbd5e1"}},y:{grid:{color:"rgba(148, 163, 184, 0.15)"},ticks:{color:"#cbd5e1"}}}}})}function D(e){if(!a.recommendationsContainer||!e)return;let i=`
        <h3 class="recommendations-title">
            <span class="material-icons-round">lightbulb</span>
            Recommendations
        </h3>
        <div class="recommendations-grid">
    `;e.immediate_actions&&e.immediate_actions.length>0&&(i+=`
            <div class="recommendation-category">
                <h4>Immediate Actions</h4>
                <ul class="recommendation-list">
                    ${e.immediate_actions.map(t=>`<li>${t}</li>`).join("")}
                </ul>
            </div>
        `),e.preventive_measures&&e.preventive_measures.length>0&&(i+=`
            <div class="recommendation-category">
                <h4>Preventive Measures</h4>
                <ul class="recommendation-list">
                    ${e.preventive_measures.map(t=>`<li>${t}</li>`).join("")}
                </ul>
            </div>
        `),e.optimization_opportunities&&e.optimization_opportunities.length>0&&(i+=`
            <div class="recommendation-category">
                <h4>Optimization Opportunities</h4>
                <ul class="recommendation-list">
                    ${e.optimization_opportunities.map(t=>`<li>${t}</li>`).join("")}
                </ul>
            </div>
        `),i+="</div>",a.recommendationsContainer.innerHTML=i}function I(e,i){switch(i){case"harmonics":L(e);break;case"power_quality":P(e);break;case"predictive":F(e);break;default:$({[i]:e})}}function L(e){if(!a.metricsContainer)return;let i="<h3>Harmonic Analysis Results</h3>";e.voltage&&(i+='<div class="harmonic-section"><h4>Voltage THD Analysis</h4>',Object.entries(e.voltage).forEach(([t,s])=>{const n=s.compliant?"good":"warning";i+=`
                <div class="stability-metric-card ${n}">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons-round">graphic_eq</span>
                        </div>
                        <h3 class="metric-title">${t.toUpperCase()} THD</h3>
                    </div>
                    <div class="metric-value">${s.estimated_thd}%</div>
                    <div class="metric-description">
                        Quality: ${s.quality} | Threshold: ${s.threshold}%
                    </div>
                    <div class="compliance-indicator ${s.compliant?"compliant":"non-compliant"}">
                        ${s.compliant?"✓ Compliant":"⚠ Non-Compliant"}
                    </div>
                </div>
            `}),i+="</div>"),e.current&&(i+='<div class="harmonic-section"><h4>Current THD Analysis</h4>',Object.entries(e.current).forEach(([t,s])=>{const n=s.compliant?"good":"warning";i+=`
                <div class="stability-metric-card ${n}">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons-round">electric_bolt</span>
                        </div>
                        <h3 class="metric-title">${t.toUpperCase()} THD</h3>
                    </div>
                    <div class="metric-value">${s.estimated_thd}%</div>
                    <div class="metric-description">
                        Quality: ${s.quality} | Threshold: ${s.threshold}%
                    </div>
                    <div class="compliance-indicator ${s.compliant?"compliant":"non-compliant"}">
                        ${s.compliant?"✓ Compliant":"⚠ Non-Compliant"}
                    </div>
                </div>
            `}),i+="</div>"),a.metricsContainer.innerHTML=i}function P(e){if(!a.metricsContainer)return;let i="<h3>Power Quality Assessment</h3>";if(e.voltage_regulation&&(i+='<div class="power-quality-section"><h4>Voltage Regulation</h4>',Object.entries(e.voltage_regulation).forEach(([t,s])=>{const n=s.quality==="Good"?"good":s.quality==="Fair"?"warning":"critical";i+=`
                <div class="stability-metric-card ${n}">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons-round">tune</span>
                        </div>
                        <h3 class="metric-title">${t.replace("_"," ").toUpperCase()}</h3>
                    </div>
                    <div class="metric-value">${s.regulation_percentage}%</div>
                    <div class="metric-description">
                        Quality: ${s.quality}<br>
                        Range: ${s.min_voltage}V - ${s.max_voltage}V
                    </div>
                </div>
            `}),i+="</div>"),e.frequency_regulation){const t=e.frequency_regulation,s=t.quality==="Good"?"good":t.quality==="Fair"?"warning":"critical";i+=`
            <div class="power-quality-section">
                <h4>Frequency Regulation</h4>
                <div class="stability-metric-card ${s}">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons-round">speed</span>
                        </div>
                        <h3 class="metric-title">Frequency Stability</h3>
                    </div>
                    <div class="metric-value">${t.max_deviation}Hz</div>
                    <div class="metric-description">
                        Quality: ${t.quality} | Status: ${t.stability}<br>
                        Range: ${t.frequency_range[0]}Hz - ${t.frequency_range[1]}Hz
                    </div>
                </div>
            </div>
        `}if(e.voltage_unbalance){const t=e.voltage_unbalance,s=t.compliant?"good":"critical";i+=`
            <div class="power-quality-section">
                <h4>Voltage Unbalance</h4>
                <div class="stability-metric-card ${s}">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons-round">balance</span>
                        </div>
                        <h3 class="metric-title">Phase Unbalance</h3>
                    </div>
                    <div class="metric-value">${t.unbalance_percentage}%</div>
                    <div class="metric-description">
                        Quality: ${t.quality}<br>
                        Average: ${t.average_voltage}V
                    </div>
                    <div class="compliance-indicator ${t.compliant?"compliant":"non-compliant"}">
                        ${t.compliant?"✓ NEMA Compliant":"⚠ Exceeds 2% Limit"}
                    </div>
                </div>
            </div>
        `}a.metricsContainer.innerHTML=i}function F(e){if(!a.metricsContainer)return;let i="<h3>Predictive Maintenance Analysis</h3>";if(e.stress_indicators){const t=e.stress_indicators,s=t.overall_stress>20?"critical":t.overall_stress>10?"warning":"good";i+=`
            <div class="predictive-section">
                <h4>Equipment Stress Indicators</h4>
                <div class="stability-metric-card ${s}">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons-round">warning</span>
                        </div>
                        <h3 class="metric-title">Overall Stress Level</h3>
                    </div>
                    <div class="metric-value">${t.overall_stress.toFixed(1)}%</div>
                    <div class="metric-details">
                        <div class="metric-detail">
                            <span class="metric-detail-label">Voltage Stress</span>
                            <span class="metric-detail-value">${t.voltage_stress.toFixed(1)}%</span>
                        </div>
                        <div class="metric-detail">
                            <span class="metric-detail-label">Current Stress</span>
                            <span class="metric-detail-value">${t.current_stress.toFixed(1)}%</span>
                        </div>
                        <div class="metric-detail">
                            <span class="metric-detail-label">Thermal Stress</span>
                            <span class="metric-detail-value">${t.thermal_stress.toFixed(1)}%</span>
                        </div>
                    </div>
                </div>
            </div>
        `}if(e.risk_assessment){const t=e.risk_assessment,s=t.level==="Critical"?"critical":t.level==="High"?"warning":"good";i+=`
            <div class="predictive-section">
                <h4>Risk Assessment</h4>
                <div class="stability-metric-card ${s}">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons-round">assessment</span>
                        </div>
                        <h3 class="metric-title">Maintenance Risk</h3>
                    </div>
                    <div class="metric-value">${t.level}</div>
                    <div class="metric-description">
                        Probability: ${t.probability}<br>
                        Impact: ${t.impact}<br>
                        <strong>${t.recommendation}</strong>
                    </div>
                </div>
            </div>
        `}e.maintenance_schedule&&e.maintenance_schedule.length>0&&(i+='<div class="predictive-section"><h4>Recommended Maintenance Schedule</h4>',e.maintenance_schedule.forEach(t=>{const s=t.priority==="High"?"critical":t.priority==="Medium"?"warning":"good";i+=`
                <div class="maintenance-item ${s}">
                    <div class="maintenance-type">${t.type}</div>
                    <div class="maintenance-priority">Priority: ${t.priority}</div>
                    <div class="maintenance-timeframe">Timeframe: ${t.timeframe}</div>
                    <div class="maintenance-reason">Reason: ${t.reason}</div>
                </div>
            `}),i+="</div>"),a.metricsContainer.innerHTML=i}function h(e){a.loadingIndicator&&(a.loadingIndicator.style.display=e?"flex":"none")}function b(e,i="info"){a.messageContainer&&d.showMessage(e,i,a.messageContainer)}function x(){a.messageContainer&&(a.messageContainer.innerHTML="")}document.addEventListener("DOMContentLoaded",C);
