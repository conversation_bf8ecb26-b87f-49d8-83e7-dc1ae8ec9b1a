import{D as d,u as m}from"./main-35d3972b.js";let n={};const s={alertType:"all",currentPage:1,totalPages:1,alerts:[],events:[],isLoading:!1};function f(){n={alertType:document.getElementById("alertType"),startDate:document.getElementById("startDate"),endDate:document.getElementById("endDate"),timeRange:document.getElementById("timeRange"),refreshAlertsBtn:document.getElementById("refreshAlertsBtn"),acknowledgeAllBtn:document.getElementById("acknowledgeAllBtn"),clearResolvedBtn:document.getElementById("clearResolvedBtn"),exportEventsBtn:document.getElementById("exportEventsBtn"),alertStatsContainer:document.getElementById("alertStatsContainer"),activeAlertsList:document.getElementById("activeAlertsList"),eventsTableBody:document.getElementById("eventsTableBody"),prevBtn:document.getElementById("prevBtn"),nextBtn:document.getElementById("nextBtn"),pageInfo:document.getElementById("pageInfo"),messageContainer:document.getElementById("messageContainer"),criticalCount:document.getElementById("criticalCount"),warningCount:document.getElementById("warningCount"),infoCount:document.getElementById("infoCount"),alertConfigContainer:document.getElementById("alertConfigContainer")},v("24h"),p(),i(),$()}function p(){n.timeRange&&n.timeRange.addEventListener("change",e=>{e.target.value!=="custom"&&v(e.target.value)}),n.alertType&&n.alertType.addEventListener("change",e=>{s.alertType=e.target.value,i()}),n.refreshAlertsBtn&&n.refreshAlertsBtn.addEventListener("click",i),n.acknowledgeAllBtn&&n.acknowledgeAllBtn.addEventListener("click",k),n.clearResolvedBtn&&n.clearResolvedBtn.addEventListener("click",A),n.exportEventsBtn&&n.exportEventsBtn.addEventListener("click",T),n.prevBtn&&n.prevBtn.addEventListener("click",()=>{s.currentPage>1&&(s.currentPage--,i())}),n.nextBtn&&n.nextBtn.addEventListener("click",()=>{s.currentPage<s.totalPages&&(s.currentPage++,i())})}function v(e){const a=d.now().setZone("Asia/Kolkata"),t=a;let r=a;switch(e){case"1h":r=a.minus({hours:1});break;case"6h":r=a.minus({hours:6});break;case"24h":r=a.minus({hours:24});break;case"7d":r=a.minus({days:7});break;case"30d":r=a.minus({days:30});break;case"custom":return;default:r=a.minus({hours:24})}n.startDate&&(n.startDate.value=r.toFormat("yyyy-MM-dd'T'HH:mm")),n.endDate&&(n.endDate.value=t.toFormat("yyyy-MM-dd'T'HH:mm"))}async function i(){if(!s.isLoading)try{s.isLoading=!0;const e=g(),a=await fetch(`../backend/alerts_events.php?type=${s.alertType}&start=${encodeURIComponent(e.start)}&end=${encodeURIComponent(e.end)}&page=${s.currentPage}`);if(!a.ok)throw new Error(`HTTP error! Status: ${a.status}`);const t=await a.json();if(t.error)throw new Error(t.error);s.alerts=t.alerts||[],s.events=t.events||[],s.totalPages=t.totalPages||1,y(t.counts),h(t.stats),w(s.alerts),C(s.events),B(),o("Alerts loaded successfully","success")}catch(e){console.error("Error loading alerts:",e),o(`Error: ${e.message}`,"error")}finally{s.isLoading=!1}}function g(){if(!n.startDate||!n.endDate){const c=d.now().setZone("Asia/Kolkata");return{start:c.minus({hours:24}).toFormat("yyyy-MM-dd HH:mm:ss"),end:c.toFormat("yyyy-MM-dd HH:mm:ss")}}const e=n.startDate.value,a=n.endDate.value;if(!e||!a)throw new Error("Please select both start and end dates");const t=d.fromISO(e),r=d.fromISO(a);return{start:t.toFormat("yyyy-MM-dd HH:mm:ss"),end:r.toFormat("yyyy-MM-dd HH:mm:ss")}}function y(e){n.criticalCount&&(n.criticalCount.textContent=(e==null?void 0:e.critical)||0),n.warningCount&&(n.warningCount.textContent=(e==null?void 0:e.warning)||0),n.infoCount&&(n.infoCount.textContent=(e==null?void 0:e.info)||0)}function h(e){if(!n.alertStatsContainer||!e)return;let a=`
        <div class="stat-card">
            <div class="stat-icon critical">
                <span class="material-icons-round">error</span>
            </div>
            <div class="stat-content">
                <div class="stat-value">${e.total_critical||0}</div>
                <div class="stat-label">Critical Alerts</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon warning">
                <span class="material-icons-round">warning</span>
            </div>
            <div class="stat-content">
                <div class="stat-value">${e.total_warning||0}</div>
                <div class="stat-label">Warning Alerts</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon info">
                <span class="material-icons-round">info</span>
            </div>
            <div class="stat-content">
                <div class="stat-value">${e.total_info||0}</div>
                <div class="stat-label">Info Events</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon resolved">
                <span class="material-icons-round">check_circle</span>
            </div>
            <div class="stat-content">
                <div class="stat-value">${e.resolved||0}</div>
                <div class="stat-label">Resolved</div>
            </div>
        </div>
    `;n.alertStatsContainer.innerHTML=a}function w(e){if(!n.activeAlertsList)return;if(!e||e.length===0){n.activeAlertsList.innerHTML='<div class="no-alerts">No active alerts</div>';return}let a="";e.forEach(t=>{var l;const r=((l=t.severity)==null?void 0:l.toLowerCase())||"info",c=b(t.timestamp);a+=`
            <div class="alert-item ${r}" data-alert-id="${t.id}">
                <div class="alert-icon">
                    <span class="material-icons-round">${E(t.severity)}</span>
                </div>
                <div class="alert-content">
                    <div class="alert-title">${t.title||"Alert"}</div>
                    <div class="alert-message">${t.message||"No message"}</div>
                    <div class="alert-meta">
                        <span class="alert-source">${t.source||"System"}</span>
                        <span class="alert-time">${c}</span>
                    </div>
                </div>
                <div class="alert-actions">
                    <button class="action-btn acknowledge" onclick="acknowledgeAlert('${t.id}')">
                        <span class="material-icons-round">check</span>
                    </button>
                    <button class="action-btn dismiss" onclick="dismissAlert('${t.id}')">
                        <span class="material-icons-round">close</span>
                    </button>
                </div>
            </div>
        `}),n.activeAlertsList.innerHTML=a}function E(e){switch(e==null?void 0:e.toLowerCase()){case"critical":return"error";case"warning":return"warning";case"info":return"info";default:return"notifications"}}function b(e){if(!e)return"Unknown";try{const a=d.fromSQL(e),r=d.now().diff(a,["hours","minutes"]);return r.hours>=1?`${Math.floor(r.hours)}h ago`:`${Math.floor(r.minutes)}m ago`}catch{return e}}function C(e){if(!n.eventsTableBody)return;if(!e||e.length===0){n.eventsTableBody.innerHTML='<tr><td colspan="7" style="text-align: center;">No events found</td></tr>';return}let a="";e.forEach(t=>{var l,u;const r=m.formatTimestamp(t.timestamp),c=((l=t.severity)==null?void 0:l.toLowerCase())||"info";a+=`
            <tr class="event-row ${c}">
                <td>${r}</td>
                <td>${t.type||"Event"}</td>
                <td><span class="severity-badge ${c}">${t.severity||"Info"}</span></td>
                <td>${t.source||"System"}</td>
                <td>${t.message||"No message"}</td>
                <td><span class="status-badge ${((u=t.status)==null?void 0:u.toLowerCase())||"active"}">${t.status||"Active"}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="viewEventDetails('${t.id}')">
                            <span class="material-icons-round">visibility</span>
                        </button>
                        <button class="action-btn" onclick="exportEvent('${t.id}')">
                            <span class="material-icons-round">file_download</span>
                        </button>
                    </div>
                </td>
            </tr>
        `}),n.eventsTableBody.innerHTML=a}function B(){n.pageInfo&&(n.pageInfo.textContent=`Page ${s.currentPage} of ${s.totalPages}`),n.prevBtn&&(n.prevBtn.disabled=s.currentPage<=1),n.nextBtn&&(n.nextBtn.disabled=s.currentPage>=s.totalPages)}async function $(){if(!n.alertConfigContainer)return;const e=[{parameter:"Voltage",threshold:"±10%",enabled:!0},{parameter:"Current",threshold:"±20%",enabled:!0},{parameter:"Frequency",threshold:"±1Hz",enabled:!0},{parameter:"Power Factor",threshold:"<0.8",enabled:!0}];let a="";e.forEach(t=>{a+=`
            <div class="config-item">
                <div class="config-parameter">${t.parameter}</div>
                <div class="config-threshold">${t.threshold}</div>
                <div class="config-status">
                    <span class="status-indicator ${t.enabled?"enabled":"disabled"}">
                        ${t.enabled?"Enabled":"Disabled"}
                    </span>
                </div>
                <div class="config-actions">
                    <button class="action-btn edit">
                        <span class="material-icons-round">edit</span>
                    </button>
                </div>
            </div>
        `}),n.alertConfigContainer.innerHTML=a}async function k(){try{const a=await(await fetch("../backend/alerts_events.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"acknowledge_all"})})).json();if(a.success)o("All alerts acknowledged","success"),i();else throw new Error(a.error||"Failed to acknowledge alerts")}catch(e){o(`Error: ${e.message}`,"error")}}async function A(){try{const a=await(await fetch("../backend/alerts_events.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"clear_resolved"})})).json();if(a.success)o("Resolved alerts cleared","success"),i();else throw new Error(a.error||"Failed to clear alerts")}catch(e){o(`Error: ${e.message}`,"error")}}async function T(){try{const e=g(),a=`../backend/export_events.php?start=${encodeURIComponent(e.start)}&end=${encodeURIComponent(e.end)}`,t=document.createElement("a");t.href=a,t.download=`events_${d.now().toFormat("yyyy-MM-dd")}.csv`,document.body.appendChild(t),t.click(),document.body.removeChild(t),o("Events export initiated","success")}catch(e){o(`Error: ${e.message}`,"error")}}function o(e,a="info"){n.messageContainer&&m.showMessage(e,a,n.messageContainer)}window.acknowledgeAlert=async function(e){try{const t=await(await fetch("../backend/alerts_events.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"acknowledge",alert_id:e})})).json();if(t.success)o("Alert acknowledged","success"),i();else throw new Error(t.error||"Failed to acknowledge alert")}catch(a){o(`Error: ${a.message}`,"error")}};window.dismissAlert=async function(e){try{const t=await(await fetch("../backend/alerts_events.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"dismiss",alert_id:e})})).json();if(t.success)o("Alert dismissed","success"),i();else throw new Error(t.error||"Failed to dismiss alert")}catch(a){o(`Error: ${a.message}`,"error")}};window.viewEventDetails=function(e){o("Event details feature coming soon","info")};window.exportEvent=function(e){o("Single event export feature coming soon","info")};document.addEventListener("DOMContentLoaded",f);
