<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>PowerMonitor Pro - Enterprise Electrical Monitoring</title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="../assets/images/company-logo.svg">

    <!-- Meta tags for better SEO and sharing -->
    <meta name="description" content="Enterprise-grade electrical monitoring system for industrial applications. Real-time power monitoring dashboard with advanced analytics.">
    <meta name="keywords" content="power monitoring, electrical monitoring, industrial monitoring, energy management, power quality">
    <meta property="og:title" content="PowerMonitor Pro - Enterprise Electrical Monitoring">
    <meta property="og:description" content="Enterprise-grade electrical monitoring system for industrial applications. Real-time power monitoring dashboard with advanced analytics.">
    <meta property="og:type" content="website">

    <!-- React bundle -->
    <script src="react/dist/runtime.bundle.js" defer></script>
    <script src="react/dist/vendor.react-dom.bundle.js" defer></script>
    <script src="react/dist/vendor.chart.js.bundle.js" defer></script>
    <script src="react/dist/vendor.luxon.bundle.js" defer></script>
    <script src="react/dist/main.bundle.js" defer></script>

    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc;
            color: #0f172a;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #f8fafc;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }

        .loading-content {
            text-align: center;
        }

        .loading-logo {
            width: 120px;
            height: auto;
            margin-bottom: 2rem;
            animation: pulse 2s infinite;
        }

        .loading-text {
            font-size: 1.25rem;
            font-weight: 600;
            color: #2563eb;
            margin-bottom: 1rem;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(37, 99, 235, 0.2);
            border-radius: 50%;
            border-top-color: #2563eb;
            animation: spin 1s linear infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        #root {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading">
        <div class="loading-content">
            <img src="../assets/images/company-logo.svg" alt="PowerMonitor Pro" class="loading-logo">
            <div class="loading-text">Loading Dashboard</div>
            <div class="loading-spinner"></div>
        </div>
    </div>

    <!-- React Root -->
    <div id="root"></div>

    <script>
        // Hide loading screen when React app is loaded
        window.addEventListener('load', function() {
            setTimeout(function() {
                const loading = document.getElementById('loading');
                loading.style.opacity = '0';
                setTimeout(function() {
                    loading.style.display = 'none';
                }, 500);
            }, 1000);
        });

        // Check if we're in development mode
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            // Log development mode
            console.log('Running in development mode');

            // In development mode, we'll use the files from the dist directory
            // No need to load from localhost:3000 as we're not running a dev server
        }
    </script>
</body>
</html>
