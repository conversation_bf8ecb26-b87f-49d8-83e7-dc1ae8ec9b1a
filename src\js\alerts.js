// Import styles
import '../scss/main.scss';
import '../scss/alerts.scss';

// Import utilities from main
import { utils } from './main';
import { DateTime } from 'luxon';

// DOM Elements
let elements = {};

// State
const state = {
    alertType: 'all',
    currentPage: 1,
    totalPages: 1,
    alerts: [],
    events: [],
    isLoading: false
};

/**
 * Initialize the alerts & events page
 */
function initAlertsEvents() {
    // Get DOM elements
    elements = {
        alertType: document.getElementById('alertType'),
        startDate: document.getElementById('startDate'),
        endDate: document.getElementById('endDate'),
        timeRange: document.getElementById('timeRange'),
        refreshAlertsBtn: document.getElementById('refreshAlertsBtn'),
        acknowledgeAllBtn: document.getElementById('acknowledgeAllBtn'),
        clearResolvedBtn: document.getElementById('clearResolvedBtn'),
        exportEventsBtn: document.getElementById('exportEventsBtn'),
        alertStatsContainer: document.getElementById('alertStatsContainer'),
        activeAlertsList: document.getElementById('activeAlertsList'),
        eventsTableBody: document.getElementById('eventsTableBody'),
        prevBtn: document.getElementById('prevBtn'),
        nextBtn: document.getElementById('nextBtn'),
        pageInfo: document.getElementById('pageInfo'),
        messageContainer: document.getElementById('messageContainer'),
        criticalCount: document.getElementById('criticalCount'),
        warningCount: document.getElementById('warningCount'),
        infoCount: document.getElementById('infoCount'),
        alertConfigContainer: document.getElementById('alertConfigContainer')
    };

    // Set default date range
    setTimeRange('24h');

    // Bind events
    bindEvents();
    
    // Load initial data
    loadAlerts();
    loadAlertConfiguration();
}

/**
 * Bind event listeners
 */
function bindEvents() {
    // Time range selector
    if (elements.timeRange) {
        elements.timeRange.addEventListener('change', (e) => {
            if (e.target.value !== 'custom') {
                setTimeRange(e.target.value);
            }
        });
    }

    // Alert type filter
    if (elements.alertType) {
        elements.alertType.addEventListener('change', (e) => {
            state.alertType = e.target.value;
            loadAlerts();
        });
    }

    // Refresh button
    if (elements.refreshAlertsBtn) {
        elements.refreshAlertsBtn.addEventListener('click', loadAlerts);
    }

    // Acknowledge all button
    if (elements.acknowledgeAllBtn) {
        elements.acknowledgeAllBtn.addEventListener('click', acknowledgeAllAlerts);
    }

    // Clear resolved button
    if (elements.clearResolvedBtn) {
        elements.clearResolvedBtn.addEventListener('click', clearResolvedAlerts);
    }

    // Export events button
    if (elements.exportEventsBtn) {
        elements.exportEventsBtn.addEventListener('click', exportEvents);
    }

    // Pagination
    if (elements.prevBtn) {
        elements.prevBtn.addEventListener('click', () => {
            if (state.currentPage > 1) {
                state.currentPage--;
                loadAlerts();
            }
        });
    }

    if (elements.nextBtn) {
        elements.nextBtn.addEventListener('click', () => {
            if (state.currentPage < state.totalPages) {
                state.currentPage++;
                loadAlerts();
            }
        });
    }
}

/**
 * Set time range based on quick filter
 */
function setTimeRange(range) {
    const now = DateTime.now().setZone('Asia/Kolkata');
    const endDate = now;
    let startDate = now;
    
    switch(range) {
        case '1h':
            startDate = now.minus({ hours: 1 });
            break;
        case '6h':
            startDate = now.minus({ hours: 6 });
            break;
        case '24h':
            startDate = now.minus({ hours: 24 });
            break;
        case '7d':
            startDate = now.minus({ days: 7 });
            break;
        case '30d':
            startDate = now.minus({ days: 30 });
            break;
        case 'custom':
            return;
        default:
            startDate = now.minus({ hours: 24 });
    }
    
    if (elements.startDate) {
        elements.startDate.value = startDate.toFormat("yyyy-MM-dd'T'HH:mm");
    }
    
    if (elements.endDate) {
        elements.endDate.value = endDate.toFormat("yyyy-MM-dd'T'HH:mm");
    }
}

/**
 * Load alerts and events
 */
async function loadAlerts() {
    if (state.isLoading) return;
    
    try {
        state.isLoading = true;
        showLoading(true);
        
        // Get time range
        const timeRange = getTimeRange();
        
        // Fetch alerts
        const response = await fetch(
            `../backend/alerts_events.php?type=${state.alertType}&start=${encodeURIComponent(timeRange.start)}&end=${encodeURIComponent(timeRange.end)}&page=${state.currentPage}`
        );
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.error) {
            throw new Error(data.error);
        }
        
        // Update state
        state.alerts = data.alerts || [];
        state.events = data.events || [];
        state.totalPages = data.totalPages || 1;
        
        // Update UI
        updateAlertCounts(data.counts);
        renderAlertStats(data.stats);
        renderActiveAlerts(state.alerts);
        renderEventsTable(state.events);
        updatePagination();
        
        showMessage('Alerts loaded successfully', 'success');
        
    } catch (error) {
        console.error('Error loading alerts:', error);
        showMessage(`Error: ${error.message}`, 'error');
    } finally {
        state.isLoading = false;
        showLoading(false);
    }
}

/**
 * Get time range from form inputs
 */
function getTimeRange() {
    if (!elements.startDate || !elements.endDate) {
        const now = DateTime.now().setZone('Asia/Kolkata');
        return {
            start: now.minus({ hours: 24 }).toFormat('yyyy-MM-dd HH:mm:ss'),
            end: now.toFormat('yyyy-MM-dd HH:mm:ss')
        };
    }
    
    const startDate = elements.startDate.value;
    const endDate = elements.endDate.value;
    
    if (!startDate || !endDate) {
        throw new Error('Please select both start and end dates');
    }
    
    const startDateObj = DateTime.fromISO(startDate);
    const endDateObj = DateTime.fromISO(endDate);
    
    return {
        start: startDateObj.toFormat('yyyy-MM-dd HH:mm:ss'),
        end: endDateObj.toFormat('yyyy-MM-dd HH:mm:ss')
    };
}

/**
 * Update alert counts in header
 */
function updateAlertCounts(counts) {
    if (elements.criticalCount) {
        elements.criticalCount.textContent = counts?.critical || 0;
    }
    if (elements.warningCount) {
        elements.warningCount.textContent = counts?.warning || 0;
    }
    if (elements.infoCount) {
        elements.infoCount.textContent = counts?.info || 0;
    }
}

/**
 * Render alert statistics
 */
function renderAlertStats(stats) {
    if (!elements.alertStatsContainer || !stats) return;
    
    let html = `
        <div class="stat-card">
            <div class="stat-icon critical">
                <span class="material-icons-round">error</span>
            </div>
            <div class="stat-content">
                <div class="stat-value">${stats.total_critical || 0}</div>
                <div class="stat-label">Critical Alerts</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon warning">
                <span class="material-icons-round">warning</span>
            </div>
            <div class="stat-content">
                <div class="stat-value">${stats.total_warning || 0}</div>
                <div class="stat-label">Warning Alerts</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon info">
                <span class="material-icons-round">info</span>
            </div>
            <div class="stat-content">
                <div class="stat-value">${stats.total_info || 0}</div>
                <div class="stat-label">Info Events</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon resolved">
                <span class="material-icons-round">check_circle</span>
            </div>
            <div class="stat-content">
                <div class="stat-value">${stats.resolved || 0}</div>
                <div class="stat-label">Resolved</div>
            </div>
        </div>
    `;
    
    elements.alertStatsContainer.innerHTML = html;
}

/**
 * Render active alerts
 */
function renderActiveAlerts(alerts) {
    if (!elements.activeAlertsList) return;
    
    if (!alerts || alerts.length === 0) {
        elements.activeAlertsList.innerHTML = '<div class="no-alerts">No active alerts</div>';
        return;
    }
    
    let html = '';
    alerts.forEach(alert => {
        const severityClass = alert.severity?.toLowerCase() || 'info';
        const timeAgo = getTimeAgo(alert.timestamp);
        
        html += `
            <div class="alert-item ${severityClass}" data-alert-id="${alert.id}">
                <div class="alert-icon">
                    <span class="material-icons-round">${getSeverityIcon(alert.severity)}</span>
                </div>
                <div class="alert-content">
                    <div class="alert-title">${alert.title || 'Alert'}</div>
                    <div class="alert-message">${alert.message || 'No message'}</div>
                    <div class="alert-meta">
                        <span class="alert-source">${alert.source || 'System'}</span>
                        <span class="alert-time">${timeAgo}</span>
                    </div>
                </div>
                <div class="alert-actions">
                    <button class="action-btn acknowledge" onclick="acknowledgeAlert('${alert.id}')">
                        <span class="material-icons-round">check</span>
                    </button>
                    <button class="action-btn dismiss" onclick="dismissAlert('${alert.id}')">
                        <span class="material-icons-round">close</span>
                    </button>
                </div>
            </div>
        `;
    });
    
    elements.activeAlertsList.innerHTML = html;
}

/**
 * Get severity icon
 */
function getSeverityIcon(severity) {
    switch(severity?.toLowerCase()) {
        case 'critical': return 'error';
        case 'warning': return 'warning';
        case 'info': return 'info';
        default: return 'notifications';
    }
}

/**
 * Get time ago string
 */
function getTimeAgo(timestamp) {
    if (!timestamp) return 'Unknown';
    
    try {
        const date = DateTime.fromSQL(timestamp);
        const now = DateTime.now();
        const diff = now.diff(date, ['hours', 'minutes']);
        
        if (diff.hours >= 1) {
            return `${Math.floor(diff.hours)}h ago`;
        } else {
            return `${Math.floor(diff.minutes)}m ago`;
        }
    } catch (e) {
        return timestamp;
    }
}

/**
 * Render events table
 */
function renderEventsTable(events) {
    if (!elements.eventsTableBody) return;
    
    if (!events || events.length === 0) {
        elements.eventsTableBody.innerHTML = '<tr><td colspan="7" style="text-align: center;">No events found</td></tr>';
        return;
    }
    
    let html = '';
    events.forEach(event => {
        const timestamp = utils.formatTimestamp(event.timestamp);
        const severityClass = event.severity?.toLowerCase() || 'info';
        
        html += `
            <tr class="event-row ${severityClass}">
                <td>${timestamp}</td>
                <td>${event.type || 'Event'}</td>
                <td><span class="severity-badge ${severityClass}">${event.severity || 'Info'}</span></td>
                <td>${event.source || 'System'}</td>
                <td>${event.message || 'No message'}</td>
                <td><span class="status-badge ${event.status?.toLowerCase() || 'active'}">${event.status || 'Active'}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="viewEventDetails('${event.id}')">
                            <span class="material-icons-round">visibility</span>
                        </button>
                        <button class="action-btn" onclick="exportEvent('${event.id}')">
                            <span class="material-icons-round">file_download</span>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    elements.eventsTableBody.innerHTML = html;
}

/**
 * Update pagination
 */
function updatePagination() {
    if (elements.pageInfo) {
        elements.pageInfo.textContent = `Page ${state.currentPage} of ${state.totalPages}`;
    }
    
    if (elements.prevBtn) {
        elements.prevBtn.disabled = state.currentPage <= 1;
    }
    
    if (elements.nextBtn) {
        elements.nextBtn.disabled = state.currentPage >= state.totalPages;
    }
}

/**
 * Load alert configuration
 */
async function loadAlertConfiguration() {
    // Implementation for loading alert configuration
    if (!elements.alertConfigContainer) return;
    
    // Placeholder configuration
    const config = [
        { parameter: 'Voltage', threshold: '±10%', enabled: true },
        { parameter: 'Current', threshold: '±20%', enabled: true },
        { parameter: 'Frequency', threshold: '±1Hz', enabled: true },
        { parameter: 'Power Factor', threshold: '<0.8', enabled: true }
    ];
    
    let html = '';
    config.forEach(item => {
        html += `
            <div class="config-item">
                <div class="config-parameter">${item.parameter}</div>
                <div class="config-threshold">${item.threshold}</div>
                <div class="config-status">
                    <span class="status-indicator ${item.enabled ? 'enabled' : 'disabled'}">
                        ${item.enabled ? 'Enabled' : 'Disabled'}
                    </span>
                </div>
                <div class="config-actions">
                    <button class="action-btn edit">
                        <span class="material-icons-round">edit</span>
                    </button>
                </div>
            </div>
        `;
    });
    
    elements.alertConfigContainer.innerHTML = html;
}

/**
 * Acknowledge all alerts
 */
async function acknowledgeAllAlerts() {
    try {
        const response = await fetch('../backend/alerts_events.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ action: 'acknowledge_all' })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showMessage('All alerts acknowledged', 'success');
            loadAlerts();
        } else {
            throw new Error(data.error || 'Failed to acknowledge alerts');
        }
    } catch (error) {
        showMessage(`Error: ${error.message}`, 'error');
    }
}

/**
 * Clear resolved alerts
 */
async function clearResolvedAlerts() {
    try {
        const response = await fetch('../backend/alerts_events.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ action: 'clear_resolved' })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showMessage('Resolved alerts cleared', 'success');
            loadAlerts();
        } else {
            throw new Error(data.error || 'Failed to clear alerts');
        }
    } catch (error) {
        showMessage(`Error: ${error.message}`, 'error');
    }
}

/**
 * Export events
 */
async function exportEvents() {
    try {
        const timeRange = getTimeRange();
        const url = `../backend/export_events.php?start=${encodeURIComponent(timeRange.start)}&end=${encodeURIComponent(timeRange.end)}`;
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `events_${DateTime.now().toFormat('yyyy-MM-dd')}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        showMessage('Events export initiated', 'success');
    } catch (error) {
        showMessage(`Error: ${error.message}`, 'error');
    }
}

/**
 * Show loading indicator
 */
function showLoading(show) {
    // Implementation for loading indicator
}

/**
 * Show message
 */
function showMessage(message, type = 'info') {
    if (elements.messageContainer) {
        utils.showMessage(message, type, elements.messageContainer);
    }
}

// Global functions for onclick handlers
window.acknowledgeAlert = async function(alertId) {
    try {
        const response = await fetch('../backend/alerts_events.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ action: 'acknowledge', alert_id: alertId })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showMessage('Alert acknowledged', 'success');
            loadAlerts();
        } else {
            throw new Error(data.error || 'Failed to acknowledge alert');
        }
    } catch (error) {
        showMessage(`Error: ${error.message}`, 'error');
    }
};

window.dismissAlert = async function(alertId) {
    try {
        const response = await fetch('../backend/alerts_events.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ action: 'dismiss', alert_id: alertId })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showMessage('Alert dismissed', 'success');
            loadAlerts();
        } else {
            throw new Error(data.error || 'Failed to dismiss alert');
        }
    } catch (error) {
        showMessage(`Error: ${error.message}`, 'error');
    }
};

window.viewEventDetails = function(eventId) {
    // Implementation for viewing event details
    showMessage('Event details feature coming soon', 'info');
};

window.exportEvent = function(eventId) {
    // Implementation for exporting single event
    showMessage('Single event export feature coming soon', 'info');
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initAlertsEvents);

// Export for module usage
export { initAlertsEvents };
