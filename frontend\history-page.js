/**
 * History Page JavaScript
 * Enhanced functionality for the historical data page
 */

// DOM Elements
const elements = {
    startDate: document.getElementById('startDate'),
    endDate: document.getElementById('endDate'),
    limit: document.getElementById('limit'),
    filterBtn: document.getElementById('filterBtn'),
    exportBtn: document.getElementById('exportBtn'),
    dataRows: document.getElementById('dataRows'),
    prevBtn: document.getElementById('prevBtn'),
    nextBtn: document.getElementById('nextBtn'),
    pageInfo: document.getElementById('pageInfo'),
    messageContainer: document.getElementById('messageContainer'),
    historyChart: document.getElementById('historyChart'),
    lineChartBtn: document.getElementById('lineChartBtn'),
    barChartBtn: document.getElementById('barChartBtn'),
    tableViewBtn: document.getElementById('tableViewBtn'),
    quickFilters: document.querySelectorAll('.quick-filter')
};

// State
const state = {
    currentPage: 1,
    totalPages: 1,
    data: [],
    chartInstance: null,
    chartType: 'line',
    activeParameter: 'voltage_1',
    isVisualizationVisible: true
};

// Utility Functions
const utils = {
    // Show a message to the user
    showMessage: (message, type = 'info') => {
        elements.messageContainer.innerHTML = `
            <div class="message ${type}">
                ${message}
            </div>
        `;

        // Auto-hide success messages after 5 seconds
        if (type === 'success') {
            setTimeout(() => {
                if (elements.messageContainer.querySelector('.message.success')) {
                    utils.clearMessage();
                }
            }, 5000);
        }
    },

    // Clear any displayed messages
    clearMessage: () => {
        elements.messageContainer.innerHTML = '';
    },

    // Format date for datetime-local input (in IST - Indian Standard Time)
    formatDateForInput: (date) => {
        // Convert to IST by adding 5 hours and 30 minutes to UTC
        // This is needed because the input expects local time
        const istDate = new Date(date.getTime() + (5.5 * 60 * 60 * 1000));
        return istDate.toISOString().slice(0, 16);
    },

    // Format date for server (YYYY-MM-DD HH:MM:SS)
    formatDateForServer: (dateObj) => {
        if (typeof dateObj === 'string') {
            dateObj = new Date(dateObj);
        }

        // Format as YYYY-MM-DD HH:MM:SS
        const year = dateObj.getFullYear();
        const month = String(dateObj.getMonth() + 1).padStart(2, '0');
        const day = String(dateObj.getDate()).padStart(2, '0');
        const hours = String(dateObj.getHours()).padStart(2, '0');
        const minutes = String(dateObj.getMinutes()).padStart(2, '0');
        const seconds = String(dateObj.getSeconds()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // Format number with 3 decimal places
    formatNumber: (value) => {
        if (value === undefined || value === null) return '0.000';
        try {
            return parseFloat(value).toFixed(3);
        } catch (e) {
            return '0.000';
        }
    },

    // Format timestamp for display in Indian Standard Time (IST)
    formatTimestamp: (timestamp) => {
        if (!timestamp) return 'N/A';
        try {
            // Parse the timestamp
            const date = new Date(timestamp);
            if (isNaN(date.getTime())) return timestamp;

            // Format with explicit IST timezone
            const options = {
                timeZone: 'Asia/Kolkata', // Indian Standard Time
                year: 'numeric',
                month: 'numeric',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: true
            };

            return date.toLocaleString('en-IN', options);
        } catch (e) {
            console.error('Error formatting timestamp:', e);
            return timestamp;
        }
    },

    // Set time range based on quick filter
    setTimeRange: (range) => {
        // Get current time
        const now = new Date();

        // Set end date to current time
        const endDate = new Date(now);

        // Set start date based on range
        const startDate = new Date(now);

        switch(range) {
            case '1h':
                startDate.setHours(startDate.getHours() - 1);
                break;
            case '6h':
                startDate.setHours(startDate.getHours() - 6);
                break;
            case '24h':
                startDate.setHours(startDate.getHours() - 24);
                break;
            case '7d':
                startDate.setDate(startDate.getDate() - 7);
                break;
            case '30d':
                startDate.setDate(startDate.getDate() - 30);
                break;
            case 'custom':
                // Don't change dates, just return
                return;
            default:
                startDate.setHours(startDate.getHours() - 1);
        }

        // Format dates for the input fields
        elements.startDate.value = utils.formatDateForInput(startDate);
        elements.endDate.value = utils.formatDateForInput(endDate);

        // Fetch data with the new date range
        api.fetchHistoricalData();
    },

    // Update active quick filter
    updateActiveQuickFilter: (activeFilter) => {
        elements.quickFilters.forEach(filter => {
            filter.classList.remove('active');
            if (filter.dataset.range === activeFilter) {
                filter.classList.add('active');
            }
        });
    }
};

// API Functions
const api = {
    // Fetch historical data from the backend
    fetchHistoricalData: async () => {
        try {
            utils.clearMessage();

            // Show loading state
            elements.dataRows.innerHTML = '<tr><td colspan="12" class="loading">Loading data...</td></tr>';

            // Get the input values
            const startDate = elements.startDate.value;
            const endDate = elements.endDate.value;

            // Validate inputs
            if (!startDate || !endDate) {
                utils.showMessage('Please select both start and end dates', 'error');
                return null;
            }

            // Parse dates from input fields
            const startDateObj = new Date(startDate);
            const endDateObj = new Date(endDate);

            // Format dates for server in YYYY-MM-DD HH:MM:SS format
            const startFormatted = utils.formatDateForServer(startDateObj);
            const endFormatted = utils.formatDateForServer(endDateObj);

            // Prepare request parameters
            const limit = elements.limit.value;
            const offset = (state.currentPage - 1) * parseInt(limit);
            const cacheBuster = new Date().getTime();

            // Build URL
            const url = `../backend/get_historical_data.php?start=${encodeURIComponent(startFormatted)}&end=${encodeURIComponent(endFormatted)}&limit=${limit}&offset=${offset}&_=${cacheBuster}`;

            // Fetch data
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            // Parse response
            const responseText = await response.text();

            let data;
            try {
                data = JSON.parse(responseText);
            } catch (parseError) {
                throw new Error(`Failed to parse JSON response: ${parseError.message}`);
            }

            // Handle error in response
            if (data.error) {
                utils.showMessage(`Error: ${data.error}`, 'error');
                return null;
            }

            // Process data
            state.data = Array.isArray(data.data) ? data.data : [];
            state.totalPages = Math.ceil(data.total / parseInt(limit));

            // Check if we have no data but sample data exists
            if (state.data.length === 0 && data.debug && data.debug.test_query_result && data.debug.test_query_result.length > 0) {
                const sampleData = data.debug.test_query_result[0];
                const sampleTimestamp = sampleData.timestamp;

                utils.showMessage(`
                    <h3>No data found for the selected time range</h3>
                    <p>There is data in the database, but not for the specific time range you selected.</p>
                    <p>We found data at: <strong>${sampleTimestamp}</strong></p>
                    <button id="useSampleDateBtn" class="button button-primary" style="margin-top: 10px;">
                        <span class="material-icons-round">schedule</span>
                        View Data Around This Time
                    </button>
                `, 'warning');

                // Add event listener to the button
                document.getElementById('useSampleDateBtn').addEventListener('click', () => {
                    const sampleDate = new Date(sampleTimestamp);

                    // Set start date to 1 hour before sample time
                    const startDate = new Date(sampleDate);
                    startDate.setHours(startDate.getHours() - 1);

                    // Set end date to 1 hour after sample time
                    const endDate = new Date(sampleDate);
                    endDate.setHours(endDate.getHours() + 1);

                    // Format dates for the input fields
                    elements.startDate.value = utils.formatDateForInput(startDate);
                    elements.endDate.value = utils.formatDateForInput(endDate);

                    // Fetch data with the new date range
                    api.fetchHistoricalData();
                });
            } else if (state.data.length === 0) {
                utils.showMessage('No data found for the selected period', 'warning');
            } else {
                utils.showMessage(`Found ${state.data.length} records`, 'success');
            }

            // Update UI
            ui.renderTable();
            ui.updatePagination();
            ui.updateChart();

            return data;
        } catch (error) {
            console.error('Error fetching historical data:', error);
            utils.showMessage(`Error: ${error.message}`, 'error');
            return null;
        }
    },

    // Export data as CSV - downloads ALL pages of data
    exportCSV: async () => {
        try {
            // Show loading message and disable the export button
            utils.showMessage('Preparing CSV export of all data...', 'info');
            const exportBtn = elements.exportBtn;

            // Save original button text and disable the button
            const originalBtnText = exportBtn.innerHTML;
            exportBtn.disabled = true;
            exportBtn.innerHTML = `<span class="spinner"></span> Exporting...`;

            // Get the input values
            const startDate = elements.startDate.value;
            const endDate = elements.endDate.value;

            // Validate inputs
            if (!startDate || !endDate) {
                utils.showMessage('Please select both start and end dates', 'error');
                exportBtn.disabled = false;
                exportBtn.innerHTML = originalBtnText;
                return;
            }

            // Parse dates from input fields
            const startDateObj = new Date(startDate);
            const endDateObj = new Date(endDate);

            // Format dates for server in YYYY-MM-DD HH:MM:SS format
            const startFormatted = utils.formatDateForServer(startDateObj);
            const endFormatted = utils.formatDateForServer(endDateObj);

            // Use the backend export_csv.php endpoint to get all data at once
            const url = `../backend/export_csv.php?parameter=all&start=${encodeURIComponent(startFormatted)}&end=${encodeURIComponent(endFormatted)}`;

            utils.showMessage('Downloading all data as CSV...', 'info');

            // Create a hidden link and trigger the download
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `electrical_data_${new Date().toISOString().slice(0, 10)}.csv`;
            document.body.appendChild(a);
            a.click();

            // Clean up and restore button after a delay
            setTimeout(() => {
                document.body.removeChild(a);
                exportBtn.disabled = false;
                exportBtn.innerHTML = originalBtnText;
                utils.showMessage('CSV export initiated. If the file is large, it may take a moment to download.', 'success');
            }, 3000);
        } catch (error) {
            console.error('Error exporting CSV:', error);
            utils.showMessage(`Error exporting CSV: ${error.message}`, 'error');

            // Restore button on error
            elements.exportBtn.disabled = false;
            elements.exportBtn.innerHTML = `<span class="material-icons-round">file_download</span> Export CSV`;
        }
    }
};

// UI Functions
const ui = {
    // Render the data table
    renderTable: () => {
        if (state.data.length === 0) {
            elements.dataRows.innerHTML = '<tr><td colspan="12" style="text-align: center;">No data available</td></tr>';
            return;
        }

        let html = '';
        state.data.forEach(row => {
            const timestamp = utils.formatTimestamp(row.timestamp);
            html += `<tr>
                <td>${timestamp}</td>
                <td>${utils.formatNumber(row.voltage_1)}</td>
                <td>${utils.formatNumber(row.voltage_2)}</td>
                <td>${utils.formatNumber(row.voltage_3)}</td>
                <td>${utils.formatNumber(row.current_1)}</td>
                <td>${utils.formatNumber(row.current_2)}</td>
                <td>${utils.formatNumber(row.current_3)}</td>
                <td>${utils.formatNumber(row.pf_1)}</td>
                <td>${utils.formatNumber(row.pf_2)}</td>
                <td>${utils.formatNumber(row.pf_3)}</td>
                <td>${utils.formatNumber(row.total_kw)}</td>
                <td>${utils.formatNumber(row.frequency)}</td>
            </tr>`;
        });

        elements.dataRows.innerHTML = html;
    },

    // Update pagination controls
    updatePagination: () => {
        elements.pageInfo.textContent = `Page ${state.currentPage} of ${state.totalPages || 1}`;
        elements.prevBtn.disabled = state.currentPage <= 1;
        elements.nextBtn.disabled = state.currentPage >= state.totalPages;
    },

    // Update chart with data
    updateChart: () => {
        // Destroy existing chart if it exists
        if (state.chartInstance) {
            state.chartInstance.destroy();
        }

        if (state.data.length === 0) {
            return;
        }

        // Prepare data for chart
        const labels = state.data.map(d => new Date(d.timestamp));

        // Get data for the active parameter
        const getDataset = (paramName, label, color) => {
            return {
                label: label,
                data: state.data.map(d => d[paramName]),
                borderColor: color,
                backgroundColor: color + '33', // Add 20% opacity
                borderWidth: 2,
                tension: 0.3,
                pointRadius: 0, // Hide dots by default
                pointHoverRadius: 5 // Show dots on hover
            };
        };

        // Create datasets based on chart type
        let datasets = [];

        if (state.chartType === 'line' || state.chartType === 'bar') {
            datasets = [
                getDataset('voltage_1', 'V1N (V)', '#06b6d4'),
                getDataset('voltage_2', 'V2N (V)', '#3b82f6'),
                getDataset('voltage_3', 'V3N (V)', '#8b5cf6')
            ];

            // Add glow effect to line charts
            if (state.chartType === 'line') {
                datasets.forEach(dataset => {
                    dataset.borderWidth = 2;
                    dataset.tension = 0.4;
                    dataset.pointRadius = 0; // Hide dots by default
                    dataset.pointHoverRadius = 6; // Show dots on hover
                    dataset.pointBackgroundColor = '#0f172a';
                    dataset.pointBorderWidth = 2;
                    dataset.pointBorderColor = dataset.borderColor;
                    dataset.shadowBlur = 10;
                    dataset.shadowColor = dataset.borderColor;
                });
            }
        }

        // Create chart
        const ctx = elements.historyChart.getContext('2d');
        state.chartInstance = new Chart(ctx, {
            type: state.chartType,
            data: {
                labels: labels,
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(15, 23, 42, 0.85)',
                        titleColor: '#f8fafc',
                        bodyColor: '#cbd5e1',
                        borderColor: 'rgba(148, 163, 184, 0.2)',
                        borderWidth: 1,
                        padding: 12,
                        displayColors: true,
                        cornerRadius: 8,
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                        callbacks: {
                            label: (context) => {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += context.parsed.y.toFixed(3);
                                }
                                return label;
                            },
                            labelTextColor: (context) => {
                                return context.dataset.borderColor;
                            }
                        }
                    },
                    legend: {
                        position: 'top',
                        labels: {
                            color: '#f8fafc',
                            usePointStyle: true,
                            pointStyle: 'rectRounded',
                            padding: 15,
                            font: {
                                family: "'JetBrains Mono', monospace",
                                size: 11
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'minute',
                            displayFormats: {
                                minute: 'HH:mm'
                            },
                            tooltipFormat: 'yyyy-MM-dd HH:mm:ss'
                        },
                        grid: {
                            color: 'rgba(148, 163, 184, 0.15)',
                            lineWidth: 0.5
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            color: '#cbd5e1',
                            font: {
                                family: "'JetBrains Mono', monospace",
                                size: 10
                            },
                            maxRotation: 0
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(148, 163, 184, 0.15)',
                            lineWidth: 0.5
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            color: '#cbd5e1',
                            font: {
                                family: "'JetBrains Mono', monospace",
                                size: 10
                            },
                            callback: function(value) {
                                return value.toFixed(2);
                            }
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeOutQuart'
                },
                elements: {
                    line: {
                        tension: 0.4
                    },
                    point: {
                        radius: 3,
                        hoverRadius: 6
                    }
                },
                interaction: {
                    mode: 'index',
                    intersect: false
                }
            }
        });
    },

    // Initialize the page
    init: () => {
        // Set default date range to current time
        utils.setTimeRange('1h');
        utils.updateActiveQuickFilter('1h');

        // Add event listeners
        elements.filterBtn.addEventListener('click', api.fetchHistoricalData);
        elements.exportBtn.addEventListener('click', api.exportCSV);

        elements.prevBtn.addEventListener('click', () => {
            if (state.currentPage > 1) {
                state.currentPage--;
                api.fetchHistoricalData();
            }
        });

        elements.nextBtn.addEventListener('click', () => {
            if (state.currentPage < state.totalPages) {
                state.currentPage++;
                api.fetchHistoricalData();
            }
        });

        // Chart type buttons
        elements.lineChartBtn.addEventListener('click', () => {
            state.chartType = 'line';
            ui.updateChart();
        });

        elements.barChartBtn.addEventListener('click', () => {
            state.chartType = 'bar';
            ui.updateChart();
        });

        elements.tableViewBtn.addEventListener('click', () => {
            const visualizationContainer = document.querySelector('.data-visualization');
            state.isVisualizationVisible = !state.isVisualizationVisible;

            if (state.isVisualizationVisible) {
                visualizationContainer.style.display = 'block';
                elements.tableViewBtn.innerHTML = '<span class="material-icons-round">table_chart</span>';
            } else {
                visualizationContainer.style.display = 'none';
                elements.tableViewBtn.innerHTML = '<span class="material-icons-round">insert_chart</span>';
            }
        });

        // Quick filters
        elements.quickFilters.forEach(filter => {
            filter.addEventListener('click', () => {
                const range = filter.dataset.range;
                utils.updateActiveQuickFilter(range);
                utils.setTimeRange(range);
            });
        });
    }
};

// Initialize the page
document.addEventListener('DOMContentLoaded', ui.init);
