var Nh=Object.defineProperty;var zh=(n,t,e)=>t in n?Nh(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e;var v=(n,t,e)=>(zh(n,typeof t!="symbol"?t+"":t,e),e);/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 <PERSON><PERSON>
 * Released under the MIT License
 */function Nn(n){return n+.5|0}const Kt=(n,t,e)=>Math.max(Math.min(n,e),t);function fn(n){return Kt(Nn(n*2.55),0,255)}function ie(n){return Kt(Nn(n*255),0,255)}function Vt(n){return Kt(Nn(n/2.55)/100,0,1)}function $o(n){return Kt(Nn(n*100),0,100)}const wt={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},Cs=[..."0123456789ABCDEF"],Wh=n=>Cs[n&15],Vh=n=>Cs[(n&240)>>4]+Cs[n&15],Gn=n=>(n&240)>>4===(n&15),Bh=n=>Gn(n.r)&&Gn(n.g)&&Gn(n.b)&&Gn(n.a);function Hh(n){var t=n.length,e;return n[0]==="#"&&(t===4||t===5?e={r:255&wt[n[1]]*17,g:255&wt[n[2]]*17,b:255&wt[n[3]]*17,a:t===5?wt[n[4]]*17:255}:(t===7||t===9)&&(e={r:wt[n[1]]<<4|wt[n[2]],g:wt[n[3]]<<4|wt[n[4]],b:wt[n[5]]<<4|wt[n[6]],a:t===9?wt[n[7]]<<4|wt[n[8]]:255})),e}const $h=(n,t)=>n<255?t(n):"";function jh(n){var t=Bh(n)?Wh:Vh;return n?"#"+t(n.r)+t(n.g)+t(n.b)+$h(n.a,t):void 0}const Yh=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function al(n,t,e){const i=t*Math.min(e,1-e),s=(o,r=(o+n/30)%12)=>e-i*Math.max(Math.min(r-3,9-r,1),-1);return[s(0),s(8),s(4)]}function Uh(n,t,e){const i=(s,o=(s+n/60)%6)=>e-e*t*Math.max(Math.min(o,4-o,1),0);return[i(5),i(3),i(1)]}function Xh(n,t,e){const i=al(n,1,.5);let s;for(t+e>1&&(s=1/(t+e),t*=s,e*=s),s=0;s<3;s++)i[s]*=1-t-e,i[s]+=t;return i}function Zh(n,t,e,i,s){return n===s?(t-e)/i+(t<e?6:0):t===s?(e-n)/i+2:(n-t)/i+4}function co(n){const e=n.r/255,i=n.g/255,s=n.b/255,o=Math.max(e,i,s),r=Math.min(e,i,s),a=(o+r)/2;let l,c,h;return o!==r&&(h=o-r,c=a>.5?h/(2-o-r):h/(o+r),l=Zh(e,i,s,h,o),l=l*60+.5),[l|0,c||0,a]}function ho(n,t,e,i){return(Array.isArray(t)?n(t[0],t[1],t[2]):n(t,e,i)).map(ie)}function uo(n,t,e){return ho(al,n,t,e)}function qh(n,t,e){return ho(Xh,n,t,e)}function Gh(n,t,e){return ho(Uh,n,t,e)}function ll(n){return(n%360+360)%360}function Jh(n){const t=Yh.exec(n);let e=255,i;if(!t)return;t[5]!==i&&(e=t[6]?fn(+t[5]):ie(+t[5]));const s=ll(+t[2]),o=+t[3]/100,r=+t[4]/100;return t[1]==="hwb"?i=qh(s,o,r):t[1]==="hsv"?i=Gh(s,o,r):i=uo(s,o,r),{r:i[0],g:i[1],b:i[2],a:e}}function Kh(n,t){var e=co(n);e[0]=ll(e[0]+t),e=uo(e),n.r=e[0],n.g=e[1],n.b=e[2]}function Qh(n){if(!n)return;const t=co(n),e=t[0],i=$o(t[1]),s=$o(t[2]);return n.a<255?`hsla(${e}, ${i}%, ${s}%, ${Vt(n.a)})`:`hsl(${e}, ${i}%, ${s}%)`}const jo={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},Yo={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function tu(){const n={},t=Object.keys(Yo),e=Object.keys(jo);let i,s,o,r,a;for(i=0;i<t.length;i++){for(r=a=t[i],s=0;s<e.length;s++)o=e[s],a=a.replace(o,jo[o]);o=parseInt(Yo[r],16),n[a]=[o>>16&255,o>>8&255,o&255]}return n}let Jn;function eu(n){Jn||(Jn=tu(),Jn.transparent=[0,0,0,0]);const t=Jn[n.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const nu=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function iu(n){const t=nu.exec(n);let e=255,i,s,o;if(t){if(t[7]!==i){const r=+t[7];e=t[8]?fn(r):Kt(r*255,0,255)}return i=+t[1],s=+t[3],o=+t[5],i=255&(t[2]?fn(i):Kt(i,0,255)),s=255&(t[4]?fn(s):Kt(s,0,255)),o=255&(t[6]?fn(o):Kt(o,0,255)),{r:i,g:s,b:o,a:e}}}function su(n){return n&&(n.a<255?`rgba(${n.r}, ${n.g}, ${n.b}, ${Vt(n.a)})`:`rgb(${n.r}, ${n.g}, ${n.b})`)}const os=n=>n<=.0031308?n*12.92:Math.pow(n,1/2.4)*1.055-.055,Ee=n=>n<=.04045?n/12.92:Math.pow((n+.055)/1.055,2.4);function ou(n,t,e){const i=Ee(Vt(n.r)),s=Ee(Vt(n.g)),o=Ee(Vt(n.b));return{r:ie(os(i+e*(Ee(Vt(t.r))-i))),g:ie(os(s+e*(Ee(Vt(t.g))-s))),b:ie(os(o+e*(Ee(Vt(t.b))-o))),a:n.a+e*(t.a-n.a)}}function Kn(n,t,e){if(n){let i=co(n);i[t]=Math.max(0,Math.min(i[t]+i[t]*e,t===0?360:1)),i=uo(i),n.r=i[0],n.g=i[1],n.b=i[2]}}function cl(n,t){return n&&Object.assign(t||{},n)}function Uo(n){var t={r:0,g:0,b:0,a:255};return Array.isArray(n)?n.length>=3&&(t={r:n[0],g:n[1],b:n[2],a:255},n.length>3&&(t.a=ie(n[3]))):(t=cl(n,{r:0,g:0,b:0,a:1}),t.a=ie(t.a)),t}function ru(n){return n.charAt(0)==="r"?iu(n):Jh(n)}class An{constructor(t){if(t instanceof An)return t;const e=typeof t;let i;e==="object"?i=Uo(t):e==="string"&&(i=Hh(t)||eu(t)||ru(t)),this._rgb=i,this._valid=!!i}get valid(){return this._valid}get rgb(){var t=cl(this._rgb);return t&&(t.a=Vt(t.a)),t}set rgb(t){this._rgb=Uo(t)}rgbString(){return this._valid?su(this._rgb):void 0}hexString(){return this._valid?jh(this._rgb):void 0}hslString(){return this._valid?Qh(this._rgb):void 0}mix(t,e){if(t){const i=this.rgb,s=t.rgb;let o;const r=e===o?.5:e,a=2*r-1,l=i.a-s.a,c=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;o=1-c,i.r=255&c*i.r+o*s.r+.5,i.g=255&c*i.g+o*s.g+.5,i.b=255&c*i.b+o*s.b+.5,i.a=r*i.a+(1-r)*s.a,this.rgb=i}return this}interpolate(t,e){return t&&(this._rgb=ou(this._rgb,t._rgb,e)),this}clone(){return new An(this.rgb)}alpha(t){return this._rgb.a=ie(t),this}clearer(t){const e=this._rgb;return e.a*=1-t,this}greyscale(){const t=this._rgb,e=Nn(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=e,this}opaquer(t){const e=this._rgb;return e.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return Kn(this._rgb,2,t),this}darken(t){return Kn(this._rgb,2,-t),this}saturate(t){return Kn(this._rgb,1,t),this}desaturate(t){return Kn(this._rgb,1,-t),this}rotate(t){return Kh(this._rgb,t),this}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function Nt(){}const au=(()=>{let n=0;return()=>n++})();function I(n){return n==null}function V(n){if(Array.isArray&&Array.isArray(n))return!0;const t=Object.prototype.toString.call(n);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function E(n){return n!==null&&Object.prototype.toString.call(n)==="[object Object]"}function q(n){return(typeof n=="number"||n instanceof Number)&&isFinite(+n)}function bt(n,t){return q(n)?n:t}function C(n,t){return typeof n>"u"?t:n}const lu=(n,t)=>typeof n=="string"&&n.endsWith("%")?parseFloat(n)/100:+n/t,hl=(n,t)=>typeof n=="string"&&n.endsWith("%")?parseFloat(n)/100*t:+n;function W(n,t,e){if(n&&typeof n.call=="function")return n.apply(e,t)}function B(n,t,e,i){let s,o,r;if(V(n))if(o=n.length,i)for(s=o-1;s>=0;s--)t.call(e,n[s],s);else for(s=0;s<o;s++)t.call(e,n[s],s);else if(E(n))for(r=Object.keys(n),o=r.length,s=0;s<o;s++)t.call(e,n[r[s]],r[s])}function Di(n,t){let e,i,s,o;if(!n||!t||n.length!==t.length)return!1;for(e=0,i=n.length;e<i;++e)if(s=n[e],o=t[e],s.datasetIndex!==o.datasetIndex||s.index!==o.index)return!1;return!0}function Ci(n){if(V(n))return n.map(Ci);if(E(n)){const t=Object.create(null),e=Object.keys(n),i=e.length;let s=0;for(;s<i;++s)t[e[s]]=Ci(n[e[s]]);return t}return n}function ul(n){return["__proto__","prototype","constructor"].indexOf(n)===-1}function cu(n,t,e,i){if(!ul(n))return;const s=t[n],o=e[n];E(s)&&E(o)?Ft(s,o,i):t[n]=Ci(o)}function Ft(n,t,e){const i=V(t)?t:[t],s=i.length;if(!E(n))return n;e=e||{};const o=e.merger||cu;let r;for(let a=0;a<s;++a){if(r=i[a],!E(r))continue;const l=Object.keys(r);for(let c=0,h=l.length;c<h;++c)o(l[c],n,r,e)}return n}function wn(n,t){return Ft(n,t,{merger:hu})}function hu(n,t,e){if(!ul(n))return;const i=t[n],s=e[n];E(i)&&E(s)?wn(i,s):Object.prototype.hasOwnProperty.call(t,n)||(t[n]=Ci(s))}const Xo={"":n=>n,x:n=>n.x,y:n=>n.y};function uu(n){const t=n.split("."),e=[];let i="";for(const s of t)i+=s,i.endsWith("\\")?i=i.slice(0,-1)+".":(e.push(i),i="");return e}function du(n){const t=uu(n);return e=>{for(const i of t){if(i==="")break;e=e&&e[i]}return e}}function ae(n,t){return(Xo[t]||(Xo[t]=du(t)))(n)}function fo(n){return n.charAt(0).toUpperCase()+n.slice(1)}const pt=n=>typeof n<"u",mt=n=>typeof n=="function",Zo=(n,t)=>{if(n.size!==t.size)return!1;for(const e of n)if(!t.has(e))return!1;return!0};function fu(n){return n.type==="mouseup"||n.type==="click"||n.type==="contextmenu"}const R=Math.PI,j=2*R,gu=j+R,Pi=Number.POSITIVE_INFINITY,go=R/180,X=R/2,kt=R/4,Ai=R*2/3,Qt=Math.log10,Rt=Math.sign;function vn(n,t,e){return Math.abs(n-t)<e}function qo(n){const t=Math.round(n);n=vn(n,t,n/1e3)?t:n;const e=Math.pow(10,Math.floor(Qt(n))),i=n/e;return(i<=1?1:i<=2?2:i<=5?5:10)*e}function mu(n){const t=[],e=Math.sqrt(n);let i;for(i=1;i<e;i++)n%i===0&&(t.push(i),t.push(n/i));return e===(e|0)&&t.push(e),t.sort((s,o)=>s-o).pop(),t}function pu(n){return typeof n=="symbol"||typeof n=="object"&&n!==null&&!(Symbol.toPrimitive in n||"toString"in n||"valueOf"in n)}function le(n){return!pu(n)&&!isNaN(parseFloat(n))&&isFinite(n)}function yu(n,t){const e=Math.round(n);return e-t<=n&&e+t>=n}function dl(n,t,e){let i,s,o;for(i=0,s=n.length;i<s;i++)o=n[i][e],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function nt(n){return n*(R/180)}function $i(n){return n*(180/R)}function Go(n){if(!q(n))return;let t=1,e=0;for(;Math.round(n*t)/t!==n;)t*=10,e++;return e}function Ei(n,t){const e=t.x-n.x,i=t.y-n.y,s=Math.sqrt(e*e+i*i);let o=Math.atan2(i,e);return o<-.5*R&&(o+=j),{angle:o,distance:s}}function He(n,t){return Math.sqrt(Math.pow(t.x-n.x,2)+Math.pow(t.y-n.y,2))}function bu(n,t){return(n-t+gu)%j-R}function xt(n){return(n%j+j)%j}function En(n,t,e,i){const s=xt(n),o=xt(t),r=xt(e),a=xt(o-s),l=xt(r-s),c=xt(s-o),h=xt(s-r);return s===o||s===r||i&&o===r||a>l&&c<h}function it(n,t,e){return Math.max(t,Math.min(e,n))}function xu(n){return it(n,-32768,32767)}function Bt(n,t,e,i=1e-6){return n>=Math.min(t,e)-i&&n<=Math.max(t,e)+i}function mo(n,t,e){e=e||(r=>n[r]<t);let i=n.length-1,s=0,o;for(;i-s>1;)o=s+i>>1,e(o)?s=o:i=o;return{lo:s,hi:i}}const Ht=(n,t,e,i)=>mo(n,e,i?s=>{const o=n[s][t];return o<e||o===e&&n[s+1][t]===e}:s=>n[s][t]<e),_u=(n,t,e)=>mo(n,e,i=>n[i][t]>=e);function wu(n,t,e){let i=0,s=n.length;for(;i<s&&n[i]<t;)i++;for(;s>i&&n[s-1]>e;)s--;return i>0||s<n.length?n.slice(i,s):n}const fl=["push","pop","shift","splice","unshift"];function vu(n,t){if(n._chartjs){n._chartjs.listeners.push(t);return}Object.defineProperty(n,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),fl.forEach(e=>{const i="_onData"+fo(e),s=n[e];Object.defineProperty(n,e,{configurable:!0,enumerable:!1,value(...o){const r=s.apply(this,o);return n._chartjs.listeners.forEach(a=>{typeof a[i]=="function"&&a[i](...o)}),r}})})}function Jo(n,t){const e=n._chartjs;if(!e)return;const i=e.listeners,s=i.indexOf(t);s!==-1&&i.splice(s,1),!(i.length>0)&&(fl.forEach(o=>{delete n[o]}),delete n._chartjs)}function gl(n){const t=new Set(n);return t.size===n.length?n:Array.from(t)}const ml=function(){return typeof window>"u"?function(n){return n()}:window.requestAnimationFrame}();function pl(n,t){let e=[],i=!1;return function(...s){e=s,i||(i=!0,ml.call(window,()=>{i=!1,n.apply(t,e)}))}}function ku(n,t){let e;return function(...i){return t?(clearTimeout(e),e=setTimeout(n,t,i)):n.apply(this,i),t}}const po=n=>n==="start"?"left":n==="end"?"right":"center",at=(n,t,e)=>n==="start"?t:n==="end"?e:(t+e)/2,Su=(n,t,e,i)=>n===(i?"left":"right")?e:n==="center"?(t+e)/2:t;function yl(n,t,e){const i=t.length;let s=0,o=i;if(n._sorted){const{iScale:r,vScale:a,_parsed:l}=n,c=n.dataset&&n.dataset.options?n.dataset.options.spanGaps:null,h=r.axis,{min:u,max:d,minDefined:f,maxDefined:g}=r.getUserBounds();if(f){if(s=Math.min(Ht(l,h,u).lo,e?i:Ht(t,h,r.getPixelForValue(u)).lo),c){const m=l.slice(0,s+1).reverse().findIndex(p=>!I(p[a.axis]));s-=Math.max(0,m)}s=it(s,0,i-1)}if(g){let m=Math.max(Ht(l,r.axis,d,!0).hi+1,e?0:Ht(t,h,r.getPixelForValue(d),!0).hi+1);if(c){const p=l.slice(m-1).findIndex(y=>!I(y[a.axis]));m+=Math.max(0,p)}o=it(m,s,i)-s}else o=i-s}return{start:s,count:o}}function bl(n){const{xScale:t,yScale:e,_scaleRanges:i}=n,s={xmin:t.min,xmax:t.max,ymin:e.min,ymax:e.max};if(!i)return n._scaleRanges=s,!0;const o=i.xmin!==t.min||i.xmax!==t.max||i.ymin!==e.min||i.ymax!==e.max;return Object.assign(i,s),o}const Qn=n=>n===0||n===1,Ko=(n,t,e)=>-(Math.pow(2,10*(n-=1))*Math.sin((n-t)*j/e)),Qo=(n,t,e)=>Math.pow(2,-10*n)*Math.sin((n-t)*j/e)+1,kn={linear:n=>n,easeInQuad:n=>n*n,easeOutQuad:n=>-n*(n-2),easeInOutQuad:n=>(n/=.5)<1?.5*n*n:-.5*(--n*(n-2)-1),easeInCubic:n=>n*n*n,easeOutCubic:n=>(n-=1)*n*n+1,easeInOutCubic:n=>(n/=.5)<1?.5*n*n*n:.5*((n-=2)*n*n+2),easeInQuart:n=>n*n*n*n,easeOutQuart:n=>-((n-=1)*n*n*n-1),easeInOutQuart:n=>(n/=.5)<1?.5*n*n*n*n:-.5*((n-=2)*n*n*n-2),easeInQuint:n=>n*n*n*n*n,easeOutQuint:n=>(n-=1)*n*n*n*n+1,easeInOutQuint:n=>(n/=.5)<1?.5*n*n*n*n*n:.5*((n-=2)*n*n*n*n+2),easeInSine:n=>-Math.cos(n*X)+1,easeOutSine:n=>Math.sin(n*X),easeInOutSine:n=>-.5*(Math.cos(R*n)-1),easeInExpo:n=>n===0?0:Math.pow(2,10*(n-1)),easeOutExpo:n=>n===1?1:-Math.pow(2,-10*n)+1,easeInOutExpo:n=>Qn(n)?n:n<.5?.5*Math.pow(2,10*(n*2-1)):.5*(-Math.pow(2,-10*(n*2-1))+2),easeInCirc:n=>n>=1?n:-(Math.sqrt(1-n*n)-1),easeOutCirc:n=>Math.sqrt(1-(n-=1)*n),easeInOutCirc:n=>(n/=.5)<1?-.5*(Math.sqrt(1-n*n)-1):.5*(Math.sqrt(1-(n-=2)*n)+1),easeInElastic:n=>Qn(n)?n:Ko(n,.075,.3),easeOutElastic:n=>Qn(n)?n:Qo(n,.075,.3),easeInOutElastic(n){return Qn(n)?n:n<.5?.5*Ko(n*2,.1125,.45):.5+.5*Qo(n*2-1,.1125,.45)},easeInBack(n){return n*n*((1.70158+1)*n-1.70158)},easeOutBack(n){return(n-=1)*n*((1.70158+1)*n********)+1},easeInOutBack(n){let t=1.70158;return(n/=.5)<1?.5*(n*n*(((t*=1.525)+1)*n-t)):.5*((n-=2)*n*(((t*=1.525)+1)*n+t)+2)},easeInBounce:n=>1-kn.easeOutBounce(1-n),easeOutBounce(n){return n<1/2.75?7.5625*n*n:n<2/2.75?7.5625*(n-=1.5/2.75)*n+.75:n<2.5/2.75?7.5625*(n-=2.25/2.75)*n+.9375:7.5625*(n-=2.625/2.75)*n+.984375},easeInOutBounce:n=>n<.5?kn.easeInBounce(n*2)*.5:kn.easeOutBounce(n*2-1)*.5+.5};function yo(n){if(n&&typeof n=="object"){const t=n.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function tr(n){return yo(n)?n:new An(n)}function rs(n){return yo(n)?n:new An(n).saturate(.5).darken(.1).hexString()}const Mu=["x","y","borderWidth","radius","tension"],Tu=["color","borderColor","backgroundColor"];function Ou(n){n.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),n.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),n.set("animations",{colors:{type:"color",properties:Tu},numbers:{type:"number",properties:Mu}}),n.describe("animations",{_fallback:"animation"}),n.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function Du(n){n.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const er=new Map;function Cu(n,t){t=t||{};const e=n+JSON.stringify(t);let i=er.get(e);return i||(i=new Intl.NumberFormat(n,t),er.set(e,i)),i}function zn(n,t,e){return Cu(t,e).format(n)}const xl={values(n){return V(n)?n:""+n},numeric(n,t,e){if(n===0)return"0";const i=this.chart.options.locale;let s,o=n;if(e.length>1){const c=Math.max(Math.abs(e[0].value),Math.abs(e[e.length-1].value));(c<1e-4||c>1e15)&&(s="scientific"),o=Pu(n,e)}const r=Qt(Math.abs(o)),a=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:s,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),zn(n,i,l)},logarithmic(n,t,e){if(n===0)return"0";const i=e[t].significand||n/Math.pow(10,Math.floor(Qt(n)));return[1,2,3,5,10,15].includes(i)||t>.8*e.length?xl.numeric.call(this,n,t,e):""}};function Pu(n,t){let e=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(e)>=1&&n!==Math.floor(n)&&(e=n-Math.floor(n)),e}var ji={formatters:xl};function Au(n){n.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:ji.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),n.route("scale.ticks","color","","color"),n.route("scale.grid","color","","borderColor"),n.route("scale.border","color","","borderColor"),n.route("scale.title","color","","color"),n.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),n.describe("scales",{_fallback:"scale"}),n.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const ke=Object.create(null),Ps=Object.create(null);function Sn(n,t){if(!t)return n;const e=t.split(".");for(let i=0,s=e.length;i<s;++i){const o=e[i];n=n[o]||(n[o]=Object.create(null))}return n}function as(n,t,e){return typeof t=="string"?Ft(Sn(n,t),e):Ft(Sn(n,""),t)}class Eu{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=i=>i.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(i,s)=>rs(s.backgroundColor),this.hoverBorderColor=(i,s)=>rs(s.borderColor),this.hoverColor=(i,s)=>rs(s.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return as(this,t,e)}get(t){return Sn(this,t)}describe(t,e){return as(Ps,t,e)}override(t,e){return as(ke,t,e)}route(t,e,i,s){const o=Sn(this,t),r=Sn(this,i),a="_"+e;Object.defineProperties(o,{[a]:{value:o[e],writable:!0},[e]:{enumerable:!0,get(){const l=this[a],c=r[s];return E(l)?Object.assign({},c,l):C(l,c)},set(l){this[a]=l}}})}apply(t){t.forEach(e=>e(this))}}var Y=new Eu({_scriptable:n=>!n.startsWith("on"),_indexable:n=>n!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[Ou,Du,Au]);function Iu(n){return!n||I(n.size)||I(n.family)?null:(n.style?n.style+" ":"")+(n.weight?n.weight+" ":"")+n.size+"px "+n.family}function Ii(n,t,e,i,s){let o=t[s];return o||(o=t[s]=n.measureText(s).width,e.push(s)),o>i&&(i=o),i}function Lu(n,t,e,i){i=i||{};let s=i.data=i.data||{},o=i.garbageCollect=i.garbageCollect||[];i.font!==t&&(s=i.data={},o=i.garbageCollect=[],i.font=t),n.save(),n.font=t;let r=0;const a=e.length;let l,c,h,u,d;for(l=0;l<a;l++)if(u=e[l],u!=null&&!V(u))r=Ii(n,s,o,r,u);else if(V(u))for(c=0,h=u.length;c<h;c++)d=u[c],d!=null&&!V(d)&&(r=Ii(n,s,o,r,d));n.restore();const f=o.length/2;if(f>e.length){for(l=0;l<f;l++)delete s[o[l]];o.splice(0,f)}return r}function fe(n,t,e){const i=n.currentDevicePixelRatio,s=e!==0?Math.max(e/2,.5):0;return Math.round((t-s)*i)/i+s}function nr(n,t){!t&&!n||(t=t||n.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,n.width,n.height),t.restore())}function As(n,t,e,i){_l(n,t,e,i,null)}function _l(n,t,e,i,s){let o,r,a,l,c,h,u,d;const f=t.pointStyle,g=t.rotation,m=t.radius;let p=(g||0)*go;if(f&&typeof f=="object"&&(o=f.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){n.save(),n.translate(e,i),n.rotate(p),n.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),n.restore();return}if(!(isNaN(m)||m<=0)){switch(n.beginPath(),f){default:s?n.ellipse(e,i,s/2,m,0,0,j):n.arc(e,i,m,0,j),n.closePath();break;case"triangle":h=s?s/2:m,n.moveTo(e+Math.sin(p)*h,i-Math.cos(p)*m),p+=Ai,n.lineTo(e+Math.sin(p)*h,i-Math.cos(p)*m),p+=Ai,n.lineTo(e+Math.sin(p)*h,i-Math.cos(p)*m),n.closePath();break;case"rectRounded":c=m*.516,l=m-c,r=Math.cos(p+kt)*l,u=Math.cos(p+kt)*(s?s/2-c:l),a=Math.sin(p+kt)*l,d=Math.sin(p+kt)*(s?s/2-c:l),n.arc(e-u,i-a,c,p-R,p-X),n.arc(e+d,i-r,c,p-X,p),n.arc(e+u,i+a,c,p,p+X),n.arc(e-d,i+r,c,p+X,p+R),n.closePath();break;case"rect":if(!g){l=Math.SQRT1_2*m,h=s?s/2:l,n.rect(e-h,i-l,2*h,2*l);break}p+=kt;case"rectRot":u=Math.cos(p)*(s?s/2:m),r=Math.cos(p)*m,a=Math.sin(p)*m,d=Math.sin(p)*(s?s/2:m),n.moveTo(e-u,i-a),n.lineTo(e+d,i-r),n.lineTo(e+u,i+a),n.lineTo(e-d,i+r),n.closePath();break;case"crossRot":p+=kt;case"cross":u=Math.cos(p)*(s?s/2:m),r=Math.cos(p)*m,a=Math.sin(p)*m,d=Math.sin(p)*(s?s/2:m),n.moveTo(e-u,i-a),n.lineTo(e+u,i+a),n.moveTo(e+d,i-r),n.lineTo(e-d,i+r);break;case"star":u=Math.cos(p)*(s?s/2:m),r=Math.cos(p)*m,a=Math.sin(p)*m,d=Math.sin(p)*(s?s/2:m),n.moveTo(e-u,i-a),n.lineTo(e+u,i+a),n.moveTo(e+d,i-r),n.lineTo(e-d,i+r),p+=kt,u=Math.cos(p)*(s?s/2:m),r=Math.cos(p)*m,a=Math.sin(p)*m,d=Math.sin(p)*(s?s/2:m),n.moveTo(e-u,i-a),n.lineTo(e+u,i+a),n.moveTo(e+d,i-r),n.lineTo(e-d,i+r);break;case"line":r=s?s/2:Math.cos(p)*m,a=Math.sin(p)*m,n.moveTo(e-r,i-a),n.lineTo(e+r,i+a);break;case"dash":n.moveTo(e,i),n.lineTo(e+Math.cos(p)*(s?s/2:m),i+Math.sin(p)*m);break;case!1:n.closePath();break}n.fill(),t.borderWidth>0&&n.stroke()}}function $t(n,t,e){return e=e||.5,!t||n&&n.x>t.left-e&&n.x<t.right+e&&n.y>t.top-e&&n.y<t.bottom+e}function Wn(n,t){n.save(),n.beginPath(),n.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),n.clip()}function Vn(n){n.restore()}function Ru(n,t,e,i,s){if(!t)return n.lineTo(e.x,e.y);if(s==="middle"){const o=(t.x+e.x)/2;n.lineTo(o,t.y),n.lineTo(o,e.y)}else s==="after"!=!!i?n.lineTo(t.x,e.y):n.lineTo(e.x,t.y);n.lineTo(e.x,e.y)}function Fu(n,t,e,i){if(!t)return n.lineTo(e.x,e.y);n.bezierCurveTo(i?t.cp1x:t.cp2x,i?t.cp1y:t.cp2y,i?e.cp2x:e.cp1x,i?e.cp2y:e.cp1y,e.x,e.y)}function Nu(n,t){t.translation&&n.translate(t.translation[0],t.translation[1]),I(t.rotation)||n.rotate(t.rotation),t.color&&(n.fillStyle=t.color),t.textAlign&&(n.textAlign=t.textAlign),t.textBaseline&&(n.textBaseline=t.textBaseline)}function zu(n,t,e,i,s){if(s.strikethrough||s.underline){const o=n.measureText(i),r=t-o.actualBoundingBoxLeft,a=t+o.actualBoundingBoxRight,l=e-o.actualBoundingBoxAscent,c=e+o.actualBoundingBoxDescent,h=s.strikethrough?(l+c)/2:c;n.strokeStyle=n.fillStyle,n.beginPath(),n.lineWidth=s.decorationWidth||2,n.moveTo(r,h),n.lineTo(a,h),n.stroke()}}function Wu(n,t){const e=n.fillStyle;n.fillStyle=t.color,n.fillRect(t.left,t.top,t.width,t.height),n.fillStyle=e}function Se(n,t,e,i,s,o={}){const r=V(t)?t:[t],a=o.strokeWidth>0&&o.strokeColor!=="";let l,c;for(n.save(),n.font=s.string,Nu(n,o),l=0;l<r.length;++l)c=r[l],o.backdrop&&Wu(n,o.backdrop),a&&(o.strokeColor&&(n.strokeStyle=o.strokeColor),I(o.strokeWidth)||(n.lineWidth=o.strokeWidth),n.strokeText(c,e,i,o.maxWidth)),n.fillText(c,e,i,o.maxWidth),zu(n,e,i,c,o),i+=Number(s.lineHeight);n.restore()}function $e(n,t){const{x:e,y:i,w:s,h:o,radius:r}=t;n.arc(e+r.topLeft,i+r.topLeft,r.topLeft,1.5*R,R,!0),n.lineTo(e,i+o-r.bottomLeft),n.arc(e+r.bottomLeft,i+o-r.bottomLeft,r.bottomLeft,R,X,!0),n.lineTo(e+s-r.bottomRight,i+o),n.arc(e+s-r.bottomRight,i+o-r.bottomRight,r.bottomRight,X,0,!0),n.lineTo(e+s,i+r.topRight),n.arc(e+s-r.topRight,i+r.topRight,r.topRight,0,-X,!0),n.lineTo(e+r.topLeft,i)}const Vu=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Bu=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function Hu(n,t){const e=(""+n).match(Vu);if(!e||e[1]==="normal")return t*1.2;switch(n=+e[2],e[3]){case"px":return n;case"%":n/=100;break}return t*n}const $u=n=>+n||0;function bo(n,t){const e={},i=E(t),s=i?Object.keys(t):t,o=E(n)?i?r=>C(n[r],n[t[r]]):r=>n[r]:()=>n;for(const r of s)e[r]=$u(o(r));return e}function wl(n){return bo(n,{top:"y",right:"x",bottom:"y",left:"x"})}function se(n){return bo(n,["topLeft","topRight","bottomLeft","bottomRight"])}function tt(n){const t=wl(n);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function G(n,t){n=n||{},t=t||Y.font;let e=C(n.size,t.size);typeof e=="string"&&(e=parseInt(e,10));let i=C(n.style,t.style);i&&!(""+i).match(Bu)&&(console.warn('Invalid font style specified: "'+i+'"'),i=void 0);const s={family:C(n.family,t.family),lineHeight:Hu(C(n.lineHeight,t.lineHeight),e),size:e,style:i,weight:C(n.weight,t.weight),string:""};return s.string=Iu(s),s}function U(n,t,e,i){let s=!0,o,r,a;for(o=0,r=n.length;o<r;++o)if(a=n[o],a!==void 0&&(t!==void 0&&typeof a=="function"&&(a=a(t),s=!1),e!==void 0&&V(a)&&(a=a[e%a.length],s=!1),a!==void 0))return i&&!s&&(i.cacheable=!1),a}function ju(n,t,e){const{min:i,max:s}=n,o=hl(t,(s-i)/2),r=(a,l)=>e&&a===0?0:a+l;return{min:r(i,-Math.abs(o)),max:r(s,o)}}function he(n,t){return Object.assign(Object.create(n),t)}function xo(n,t=[""],e,i,s=()=>n[0]){const o=e||n;typeof i>"u"&&(i=Ml("_fallback",n));const r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:n,_rootScopes:o,_fallback:i,_getTarget:s,override:a=>xo([a,...n],t,o,i)};return new Proxy(r,{deleteProperty(a,l){return delete a[l],delete a._keys,delete n[0][l],!0},get(a,l){return kl(a,l,()=>Ku(l,t,n,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(n[0])},has(a,l){return sr(a).includes(l)},ownKeys(a){return sr(a)},set(a,l,c){const h=a._storage||(a._storage=s());return a[l]=h[l]=c,delete a._keys,!0}})}function je(n,t,e,i){const s={_cacheable:!1,_proxy:n,_context:t,_subProxy:e,_stack:new Set,_descriptors:vl(n,i),setContext:o=>je(n,o,e,i),override:o=>je(n.override(o),t,e,i)};return new Proxy(s,{deleteProperty(o,r){return delete o[r],delete n[r],!0},get(o,r,a){return kl(o,r,()=>Uu(o,r,a))},getOwnPropertyDescriptor(o,r){return o._descriptors.allKeys?Reflect.has(n,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(n,r)},getPrototypeOf(){return Reflect.getPrototypeOf(n)},has(o,r){return Reflect.has(n,r)},ownKeys(){return Reflect.ownKeys(n)},set(o,r,a){return n[r]=a,delete o[r],!0}})}function vl(n,t={scriptable:!0,indexable:!0}){const{_scriptable:e=t.scriptable,_indexable:i=t.indexable,_allKeys:s=t.allKeys}=n;return{allKeys:s,scriptable:e,indexable:i,isScriptable:mt(e)?e:()=>e,isIndexable:mt(i)?i:()=>i}}const Yu=(n,t)=>n?n+fo(t):t,_o=(n,t)=>E(t)&&n!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function kl(n,t,e){if(Object.prototype.hasOwnProperty.call(n,t)||t==="constructor")return n[t];const i=e();return n[t]=i,i}function Uu(n,t,e){const{_proxy:i,_context:s,_subProxy:o,_descriptors:r}=n;let a=i[t];return mt(a)&&r.isScriptable(t)&&(a=Xu(t,a,n,e)),V(a)&&a.length&&(a=Zu(t,a,n,r.isIndexable)),_o(t,a)&&(a=je(a,s,o&&o[t],r)),a}function Xu(n,t,e,i){const{_proxy:s,_context:o,_subProxy:r,_stack:a}=e;if(a.has(n))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+n);a.add(n);let l=t(o,r||i);return a.delete(n),_o(n,l)&&(l=wo(s._scopes,s,n,l)),l}function Zu(n,t,e,i){const{_proxy:s,_context:o,_subProxy:r,_descriptors:a}=e;if(typeof o.index<"u"&&i(n))return t[o.index%t.length];if(E(t[0])){const l=t,c=s._scopes.filter(h=>h!==l);t=[];for(const h of l){const u=wo(c,s,n,h);t.push(je(u,o,r&&r[n],a))}}return t}function Sl(n,t,e){return mt(n)?n(t,e):n}const qu=(n,t)=>n===!0?t:typeof n=="string"?ae(t,n):void 0;function Gu(n,t,e,i,s){for(const o of t){const r=qu(e,o);if(r){n.add(r);const a=Sl(r._fallback,e,s);if(typeof a<"u"&&a!==e&&a!==i)return a}else if(r===!1&&typeof i<"u"&&e!==i)return null}return!1}function wo(n,t,e,i){const s=t._rootScopes,o=Sl(t._fallback,e,i),r=[...n,...s],a=new Set;a.add(i);let l=ir(a,r,e,o||e,i);return l===null||typeof o<"u"&&o!==e&&(l=ir(a,r,o,l,i),l===null)?!1:xo(Array.from(a),[""],s,o,()=>Ju(t,e,i))}function ir(n,t,e,i,s){for(;e;)e=Gu(n,t,e,i,s);return e}function Ju(n,t,e){const i=n._getTarget();t in i||(i[t]={});const s=i[t];return V(s)&&E(e)?e:s||{}}function Ku(n,t,e,i){let s;for(const o of t)if(s=Ml(Yu(o,n),e),typeof s<"u")return _o(n,s)?wo(e,i,n,s):s}function Ml(n,t){for(const e of t){if(!e)continue;const i=e[n];if(typeof i<"u")return i}}function sr(n){let t=n._keys;return t||(t=n._keys=Qu(n._scopes)),t}function Qu(n){const t=new Set;for(const e of n)for(const i of Object.keys(e).filter(s=>!s.startsWith("_")))t.add(i);return Array.from(t)}function Tl(n,t,e,i){const{iScale:s}=n,{key:o="r"}=this._parsing,r=new Array(i);let a,l,c,h;for(a=0,l=i;a<l;++a)c=a+e,h=t[c],r[a]={r:s.parse(ae(h,o),c)};return r}const td=Number.EPSILON||1e-14,Ye=(n,t)=>t<n.length&&!n[t].skip&&n[t],Ol=n=>n==="x"?"y":"x";function ed(n,t,e,i){const s=n.skip?t:n,o=t,r=e.skip?t:e,a=He(o,s),l=He(r,o);let c=a/(a+l),h=l/(a+l);c=isNaN(c)?0:c,h=isNaN(h)?0:h;const u=i*c,d=i*h;return{previous:{x:o.x-u*(r.x-s.x),y:o.y-u*(r.y-s.y)},next:{x:o.x+d*(r.x-s.x),y:o.y+d*(r.y-s.y)}}}function nd(n,t,e){const i=n.length;let s,o,r,a,l,c=Ye(n,0);for(let h=0;h<i-1;++h)if(l=c,c=Ye(n,h+1),!(!l||!c)){if(vn(t[h],0,td)){e[h]=e[h+1]=0;continue}s=e[h]/t[h],o=e[h+1]/t[h],a=Math.pow(s,2)+Math.pow(o,2),!(a<=9)&&(r=3/Math.sqrt(a),e[h]=s*r*t[h],e[h+1]=o*r*t[h])}}function id(n,t,e="x"){const i=Ol(e),s=n.length;let o,r,a,l=Ye(n,0);for(let c=0;c<s;++c){if(r=a,a=l,l=Ye(n,c+1),!a)continue;const h=a[e],u=a[i];r&&(o=(h-r[e])/3,a[`cp1${e}`]=h-o,a[`cp1${i}`]=u-o*t[c]),l&&(o=(l[e]-h)/3,a[`cp2${e}`]=h+o,a[`cp2${i}`]=u+o*t[c])}}function sd(n,t="x"){const e=Ol(t),i=n.length,s=Array(i).fill(0),o=Array(i);let r,a,l,c=Ye(n,0);for(r=0;r<i;++r)if(a=l,l=c,c=Ye(n,r+1),!!l){if(c){const h=c[t]-l[t];s[r]=h!==0?(c[e]-l[e])/h:0}o[r]=a?c?Rt(s[r-1])!==Rt(s[r])?0:(s[r-1]+s[r])/2:s[r-1]:s[r]}nd(n,s,o),id(n,o,t)}function ti(n,t,e){return Math.max(Math.min(n,e),t)}function od(n,t){let e,i,s,o,r,a=$t(n[0],t);for(e=0,i=n.length;e<i;++e)r=o,o=a,a=e<i-1&&$t(n[e+1],t),o&&(s=n[e],r&&(s.cp1x=ti(s.cp1x,t.left,t.right),s.cp1y=ti(s.cp1y,t.top,t.bottom)),a&&(s.cp2x=ti(s.cp2x,t.left,t.right),s.cp2y=ti(s.cp2y,t.top,t.bottom)))}function rd(n,t,e,i,s){let o,r,a,l;if(t.spanGaps&&(n=n.filter(c=>!c.skip)),t.cubicInterpolationMode==="monotone")sd(n,s);else{let c=i?n[n.length-1]:n[0];for(o=0,r=n.length;o<r;++o)a=n[o],l=ed(c,a,n[Math.min(o+1,r-(i?0:1))%r],t.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,c=a}t.capBezierPoints&&od(n,e)}function vo(){return typeof window<"u"&&typeof document<"u"}function ko(n){let t=n.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function Li(n,t,e){let i;return typeof n=="string"?(i=parseInt(n,10),n.indexOf("%")!==-1&&(i=i/100*t.parentNode[e])):i=n,i}const Yi=n=>n.ownerDocument.defaultView.getComputedStyle(n,null);function ad(n,t){return Yi(n).getPropertyValue(t)}const ld=["top","right","bottom","left"];function ve(n,t,e){const i={};e=e?"-"+e:"";for(let s=0;s<4;s++){const o=ld[s];i[o]=parseFloat(n[t+"-"+o+e])||0}return i.width=i.left+i.right,i.height=i.top+i.bottom,i}const cd=(n,t,e)=>(n>0||t>0)&&(!e||!e.shadowRoot);function hd(n,t){const e=n.touches,i=e&&e.length?e[0]:n,{offsetX:s,offsetY:o}=i;let r=!1,a,l;if(cd(s,o,n.target))a=s,l=o;else{const c=t.getBoundingClientRect();a=i.clientX-c.left,l=i.clientY-c.top,r=!0}return{x:a,y:l,box:r}}function be(n,t){if("native"in n)return n;const{canvas:e,currentDevicePixelRatio:i}=t,s=Yi(e),o=s.boxSizing==="border-box",r=ve(s,"padding"),a=ve(s,"border","width"),{x:l,y:c,box:h}=hd(n,e),u=r.left+(h&&a.left),d=r.top+(h&&a.top);let{width:f,height:g}=t;return o&&(f-=r.width+a.width,g-=r.height+a.height),{x:Math.round((l-u)/f*e.width/i),y:Math.round((c-d)/g*e.height/i)}}function ud(n,t,e){let i,s;if(t===void 0||e===void 0){const o=n&&ko(n);if(!o)t=n.clientWidth,e=n.clientHeight;else{const r=o.getBoundingClientRect(),a=Yi(o),l=ve(a,"border","width"),c=ve(a,"padding");t=r.width-c.width-l.width,e=r.height-c.height-l.height,i=Li(a.maxWidth,o,"clientWidth"),s=Li(a.maxHeight,o,"clientHeight")}}return{width:t,height:e,maxWidth:i||Pi,maxHeight:s||Pi}}const ei=n=>Math.round(n*10)/10;function dd(n,t,e,i){const s=Yi(n),o=ve(s,"margin"),r=Li(s.maxWidth,n,"clientWidth")||Pi,a=Li(s.maxHeight,n,"clientHeight")||Pi,l=ud(n,t,e);let{width:c,height:h}=l;if(s.boxSizing==="content-box"){const d=ve(s,"border","width"),f=ve(s,"padding");c-=f.width+d.width,h-=f.height+d.height}return c=Math.max(0,c-o.width),h=Math.max(0,i?c/i:h-o.height),c=ei(Math.min(c,r,l.maxWidth)),h=ei(Math.min(h,a,l.maxHeight)),c&&!h&&(h=ei(c/2)),(t!==void 0||e!==void 0)&&i&&l.height&&h>l.height&&(h=l.height,c=ei(Math.floor(h*i))),{width:c,height:h}}function or(n,t,e){const i=t||1,s=Math.floor(n.height*i),o=Math.floor(n.width*i);n.height=Math.floor(n.height),n.width=Math.floor(n.width);const r=n.canvas;return r.style&&(e||!r.style.height&&!r.style.width)&&(r.style.height=`${n.height}px`,r.style.width=`${n.width}px`),n.currentDevicePixelRatio!==i||r.height!==s||r.width!==o?(n.currentDevicePixelRatio=i,r.height=s,r.width=o,n.ctx.setTransform(i,0,0,i,0,0),!0):!1}const fd=function(){let n=!1;try{const t={get passive(){return n=!0,!1}};vo()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch{}return n}();function rr(n,t){const e=ad(n,t),i=e&&e.match(/^(\d+)(\.\d+)?px$/);return i?+i[1]:void 0}function xe(n,t,e,i){return{x:n.x+e*(t.x-n.x),y:n.y+e*(t.y-n.y)}}function gd(n,t,e,i){return{x:n.x+e*(t.x-n.x),y:i==="middle"?e<.5?n.y:t.y:i==="after"?e<1?n.y:t.y:e>0?t.y:n.y}}function md(n,t,e,i){const s={x:n.cp2x,y:n.cp2y},o={x:t.cp1x,y:t.cp1y},r=xe(n,s,e),a=xe(s,o,e),l=xe(o,t,e),c=xe(r,a,e),h=xe(a,l,e);return xe(c,h,e)}const pd=function(n,t){return{x(e){return n+n+t-e},setWidth(e){t=e},textAlign(e){return e==="center"?e:e==="right"?"left":"right"},xPlus(e,i){return e-i},leftForLtr(e,i){return e-i}}},yd=function(){return{x(n){return n},setWidth(n){},textAlign(n){return n},xPlus(n,t){return n+t},leftForLtr(n,t){return n}}};function We(n,t,e){return n?pd(t,e):yd()}function Dl(n,t){let e,i;(t==="ltr"||t==="rtl")&&(e=n.canvas.style,i=[e.getPropertyValue("direction"),e.getPropertyPriority("direction")],e.setProperty("direction",t,"important"),n.prevTextDirection=i)}function Cl(n,t){t!==void 0&&(delete n.prevTextDirection,n.canvas.style.setProperty("direction",t[0],t[1]))}function Pl(n){return n==="angle"?{between:En,compare:bu,normalize:xt}:{between:Bt,compare:(t,e)=>t-e,normalize:t=>t}}function ar({start:n,end:t,count:e,loop:i,style:s}){return{start:n%e,end:t%e,loop:i&&(t-n+1)%e===0,style:s}}function bd(n,t,e){const{property:i,start:s,end:o}=e,{between:r,normalize:a}=Pl(i),l=t.length;let{start:c,end:h,loop:u}=n,d,f;if(u){for(c+=l,h+=l,d=0,f=l;d<f&&r(a(t[c%l][i]),s,o);++d)c--,h--;c%=l,h%=l}return h<c&&(h+=l),{start:c,end:h,loop:u,style:n.style}}function Al(n,t,e){if(!e)return[n];const{property:i,start:s,end:o}=e,r=t.length,{compare:a,between:l,normalize:c}=Pl(i),{start:h,end:u,loop:d,style:f}=bd(n,t,e),g=[];let m=!1,p=null,y,b,_;const w=()=>l(s,_,y)&&a(s,_)!==0,x=()=>a(o,y)===0||l(o,_,y),k=()=>m||w(),S=()=>!m||x();for(let M=h,O=h;M<=u;++M)b=t[M%r],!b.skip&&(y=c(b[i]),y!==_&&(m=l(y,s,o),p===null&&k()&&(p=a(y,s)===0?M:O),p!==null&&S()&&(g.push(ar({start:p,end:M,loop:d,count:r,style:f})),p=null),O=M,_=y));return p!==null&&g.push(ar({start:p,end:u,loop:d,count:r,style:f})),g}function El(n,t){const e=[],i=n.segments;for(let s=0;s<i.length;s++){const o=Al(i[s],n.points,t);o.length&&e.push(...o)}return e}function xd(n,t,e,i){let s=0,o=t-1;if(e&&!i)for(;s<t&&!n[s].skip;)s++;for(;s<t&&n[s].skip;)s++;for(s%=t,e&&(o+=s);o>s&&n[o%t].skip;)o--;return o%=t,{start:s,end:o}}function _d(n,t,e,i){const s=n.length,o=[];let r=t,a=n[t],l;for(l=t+1;l<=e;++l){const c=n[l%s];c.skip||c.stop?a.skip||(i=!1,o.push({start:t%s,end:(l-1)%s,loop:i}),t=r=c.stop?l:null):(r=l,a.skip&&(t=l)),a=c}return r!==null&&o.push({start:t%s,end:r%s,loop:i}),o}function wd(n,t){const e=n.points,i=n.options.spanGaps,s=e.length;if(!s)return[];const o=!!n._loop,{start:r,end:a}=xd(e,s,o,i);if(i===!0)return lr(n,[{start:r,end:a,loop:o}],e,t);const l=a<r?a+s:a,c=!!n._fullLoop&&r===0&&a===s-1;return lr(n,_d(e,r,l,c),e,t)}function lr(n,t,e,i){return!i||!i.setContext||!e?t:vd(n,t,e,i)}function vd(n,t,e,i){const s=n._chart.getContext(),o=cr(n.options),{_datasetIndex:r,options:{spanGaps:a}}=n,l=e.length,c=[];let h=o,u=t[0].start,d=u;function f(g,m,p,y){const b=a?-1:1;if(g!==m){for(g+=l;e[g%l].skip;)g-=b;for(;e[m%l].skip;)m+=b;g%l!==m%l&&(c.push({start:g%l,end:m%l,loop:p,style:y}),h=y,u=m%l)}}for(const g of t){u=a?u:g.start;let m=e[u%l],p;for(d=u+1;d<=g.end;d++){const y=e[d%l];p=cr(i.setContext(he(s,{type:"segment",p0:m,p1:y,p0DataIndex:(d-1)%l,p1DataIndex:d%l,datasetIndex:r}))),kd(p,h)&&f(u,d-1,g.loop,h),m=y,h=p}u<d-1&&f(u,d-1,g.loop,h)}return c}function cr(n){return{backgroundColor:n.backgroundColor,borderCapStyle:n.borderCapStyle,borderDash:n.borderDash,borderDashOffset:n.borderDashOffset,borderJoinStyle:n.borderJoinStyle,borderWidth:n.borderWidth,borderColor:n.borderColor}}function kd(n,t){if(!t)return!1;const e=[],i=function(s,o){return yo(o)?(e.includes(o)||e.push(o),e.indexOf(o)):o};return JSON.stringify(n,i)!==JSON.stringify(t,i)}function ni(n,t,e){return n.options.clip?n[e]:t[e]}function Sd(n,t){const{xScale:e,yScale:i}=n;return e&&i?{left:ni(e,t,"left"),right:ni(e,t,"right"),top:ni(i,t,"top"),bottom:ni(i,t,"bottom")}:t}function Il(n,t){const e=t._clip;if(e.disabled)return!1;const i=Sd(t,n.chartArea);return{left:e.left===!1?0:i.left-(e.left===!0?0:e.left),right:e.right===!1?n.width:i.right+(e.right===!0?0:e.right),top:e.top===!1?0:i.top-(e.top===!0?0:e.top),bottom:e.bottom===!1?n.height:i.bottom+(e.bottom===!0?0:e.bottom)}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class Md{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,i,s){const o=e.listeners[s],r=e.duration;o.forEach(a=>a({chart:t,initial:e.initial,numSteps:r,currentStep:Math.min(i-e.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=ml.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((i,s)=>{if(!i.running||!i.items.length)return;const o=i.items;let r=o.length-1,a=!1,l;for(;r>=0;--r)l=o[r],l._active?(l._total>i.duration&&(i.duration=l._total),l.tick(t),a=!0):(o[r]=o[o.length-1],o.pop());a&&(s.draw(),this._notify(s,i,t,"progress")),o.length||(i.running=!1,this._notify(s,i,t,"complete"),i.initial=!1),e+=o.length}),this._lastDate=t,e===0&&(this._running=!1)}_getAnims(t){const e=this._charts;let i=e.get(t);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,i)),i}listen(t,e,i){this._getAnims(t).listeners[e].push(i)}add(t,e){!e||!e.length||this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((i,s)=>Math.max(i,s._duration),0),this._refresh())}running(t){if(!this._running)return!1;const e=this._charts.get(t);return!(!e||!e.running||!e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const i=e.items;let s=i.length-1;for(;s>=0;--s)i[s].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var zt=new Md;const hr="transparent",Td={boolean(n,t,e){return e>.5?t:n},color(n,t,e){const i=tr(n||hr),s=i.valid&&tr(t||hr);return s&&s.valid?s.mix(i,e).hexString():t},number(n,t,e){return n+(t-n)*e}};class Od{constructor(t,e,i,s){const o=e[i];s=U([t.to,s,o,t.from]);const r=U([t.from,o,s]);this._active=!0,this._fn=t.fn||Td[t.type||typeof r],this._easing=kn[t.easing]||kn.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=i,this._from=r,this._to=s,this._promises=void 0}active(){return this._active}update(t,e,i){if(this._active){this._notify(!1);const s=this._target[this._prop],o=i-this._start,r=this._duration-o;this._start=i,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=U([t.to,e,s,t.from]),this._from=U([t.from,s,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const e=t-this._start,i=this._duration,s=this._prop,o=this._from,r=this._loop,a=this._to;let l;if(this._active=o!==a&&(r||e<i),!this._active){this._target[s]=a,this._notify(!0);return}if(e<0){this._target[s]=o;return}l=e/i%2,l=r&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[s]=this._fn(o,a,l)}wait(){const t=this._promises||(this._promises=[]);return new Promise((e,i)=>{t.push({res:e,rej:i})})}_notify(t){const e=t?"res":"rej",i=this._promises||[];for(let s=0;s<i.length;s++)i[s][e]()}}class So{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!E(t))return;const e=Object.keys(Y.animation),i=this._properties;Object.getOwnPropertyNames(t).forEach(s=>{const o=t[s];if(!E(o))return;const r={};for(const a of e)r[a]=o[a];(V(o.properties)&&o.properties||[s]).forEach(a=>{(a===s||!i.has(a))&&i.set(a,r)})})}_animateOptions(t,e){const i=e.options,s=Cd(t,i);if(!s)return[];const o=this._createAnimations(s,i);return i.$shared&&Dd(t.options.$animations,i).then(()=>{t.options=i},()=>{}),o}_createAnimations(t,e){const i=this._properties,s=[],o=t.$animations||(t.$animations={}),r=Object.keys(e),a=Date.now();let l;for(l=r.length-1;l>=0;--l){const c=r[l];if(c.charAt(0)==="$")continue;if(c==="options"){s.push(...this._animateOptions(t,e));continue}const h=e[c];let u=o[c];const d=i.get(c);if(u)if(d&&u.active()){u.update(d,h,a);continue}else u.cancel();if(!d||!d.duration){t[c]=h;continue}o[c]=u=new Od(d,t,c,h),s.push(u)}return s}update(t,e){if(this._properties.size===0){Object.assign(t,e);return}const i=this._createAnimations(t,e);if(i.length)return zt.add(this._chart,i),!0}}function Dd(n,t){const e=[],i=Object.keys(t);for(let s=0;s<i.length;s++){const o=n[i[s]];o&&o.active()&&e.push(o.wait())}return Promise.all(e)}function Cd(n,t){if(!t)return;let e=n.options;if(!e){n.options=t;return}return e.$shared&&(n.options=e=Object.assign({},e,{$shared:!1,$animations:{}})),e}function ur(n,t){const e=n&&n.options||{},i=e.reverse,s=e.min===void 0?t:0,o=e.max===void 0?t:0;return{start:i?o:s,end:i?s:o}}function Pd(n,t,e){if(e===!1)return!1;const i=ur(n,e),s=ur(t,e);return{top:s.end,right:i.end,bottom:s.start,left:i.start}}function Ad(n){let t,e,i,s;return E(n)?(t=n.top,e=n.right,i=n.bottom,s=n.left):t=e=i=s=n,{top:t,right:e,bottom:i,left:s,disabled:n===!1}}function Ll(n,t){const e=[],i=n._getSortedDatasetMetas(t);let s,o;for(s=0,o=i.length;s<o;++s)e.push(i[s].index);return e}function dr(n,t,e,i={}){const s=n.keys,o=i.mode==="single";let r,a,l,c;if(t===null)return;let h=!1;for(r=0,a=s.length;r<a;++r){if(l=+s[r],l===e){if(h=!0,i.all)continue;break}c=n.values[l],q(c)&&(o||t===0||Rt(t)===Rt(c))&&(t+=c)}return!h&&!i.all?0:t}function Ed(n,t){const{iScale:e,vScale:i}=t,s=e.axis==="x"?"x":"y",o=i.axis==="x"?"x":"y",r=Object.keys(n),a=new Array(r.length);let l,c,h;for(l=0,c=r.length;l<c;++l)h=r[l],a[l]={[s]:h,[o]:n[h]};return a}function ls(n,t){const e=n&&n.options.stacked;return e||e===void 0&&t.stack!==void 0}function Id(n,t,e){return`${n.id}.${t.id}.${e.stack||e.type}`}function Ld(n){const{min:t,max:e,minDefined:i,maxDefined:s}=n.getUserBounds();return{min:i?t:Number.NEGATIVE_INFINITY,max:s?e:Number.POSITIVE_INFINITY}}function Rd(n,t,e){const i=n[t]||(n[t]={});return i[e]||(i[e]={})}function fr(n,t,e,i){for(const s of t.getMatchingVisibleMetas(i).reverse()){const o=n[s.index];if(e&&o>0||!e&&o<0)return s.index}return null}function gr(n,t){const{chart:e,_cachedMeta:i}=n,s=e._stacks||(e._stacks={}),{iScale:o,vScale:r,index:a}=i,l=o.axis,c=r.axis,h=Id(o,r,i),u=t.length;let d;for(let f=0;f<u;++f){const g=t[f],{[l]:m,[c]:p}=g,y=g._stacks||(g._stacks={});d=y[c]=Rd(s,h,m),d[a]=p,d._top=fr(d,r,!0,i.type),d._bottom=fr(d,r,!1,i.type);const b=d._visualValues||(d._visualValues={});b[a]=p}}function cs(n,t){const e=n.scales;return Object.keys(e).filter(i=>e[i].axis===t).shift()}function Fd(n,t){return he(n,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function Nd(n,t,e){return he(n,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"})}function sn(n,t){const e=n.controller.index,i=n.vScale&&n.vScale.axis;if(i){t=t||n._parsed;for(const s of t){const o=s._stacks;if(!o||o[i]===void 0||o[i][e]===void 0)return;delete o[i][e],o[i]._visualValues!==void 0&&o[i]._visualValues[e]!==void 0&&delete o[i]._visualValues[e]}}}const hs=n=>n==="reset"||n==="none",mr=(n,t)=>t?n:Object.assign({},n),zd=(n,t,e)=>n&&!t.hidden&&t._stacked&&{keys:Ll(e,!0),values:null};class At{constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=ls(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&sn(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,i=this.getDataset(),s=(u,d,f,g)=>u==="x"?d:u==="r"?g:f,o=e.xAxisID=C(i.xAxisID,cs(t,"x")),r=e.yAxisID=C(i.yAxisID,cs(t,"y")),a=e.rAxisID=C(i.rAxisID,cs(t,"r")),l=e.indexAxis,c=e.iAxisID=s(l,o,r,a),h=e.vAxisID=s(l,r,o,a);e.xScale=this.getScaleForId(o),e.yScale=this.getScaleForId(r),e.rScale=this.getScaleForId(a),e.iScale=this.getScaleForId(c),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&Jo(this._data,this),t._stacked&&sn(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),i=this._data;if(E(e)){const s=this._cachedMeta;this._data=Ed(e,s)}else if(i!==e){if(i){Jo(i,this);const s=this._cachedMeta;sn(s),s._parsed=[]}e&&Object.isExtensible(e)&&vu(e,this),this._syncList=[],this._data=e}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,i=this.getDataset();let s=!1;this._dataCheck();const o=e._stacked;e._stacked=ls(e.vScale,e),e.stack!==i.stack&&(s=!0,sn(e),e.stack=i.stack),this._resyncElements(t),(s||o!==e._stacked)&&(gr(this,e._parsed),e._stacked=ls(e.vScale,e))}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),i=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:i,_data:s}=this,{iScale:o,_stacked:r}=i,a=o.axis;let l=t===0&&e===s.length?!0:i._sorted,c=t>0&&i._parsed[t-1],h,u,d;if(this._parsing===!1)i._parsed=s,i._sorted=!0,d=s;else{V(s[t])?d=this.parseArrayData(i,s,t,e):E(s[t])?d=this.parseObjectData(i,s,t,e):d=this.parsePrimitiveData(i,s,t,e);const f=()=>u[a]===null||c&&u[a]<c[a];for(h=0;h<e;++h)i._parsed[h+t]=u=d[h],l&&(f()&&(l=!1),c=u);i._sorted=l}r&&gr(this,d)}parsePrimitiveData(t,e,i,s){const{iScale:o,vScale:r}=t,a=o.axis,l=r.axis,c=o.getLabels(),h=o===r,u=new Array(s);let d,f,g;for(d=0,f=s;d<f;++d)g=d+i,u[d]={[a]:h||o.parse(c[g],g),[l]:r.parse(e[g],g)};return u}parseArrayData(t,e,i,s){const{xScale:o,yScale:r}=t,a=new Array(s);let l,c,h,u;for(l=0,c=s;l<c;++l)h=l+i,u=e[h],a[l]={x:o.parse(u[0],h),y:r.parse(u[1],h)};return a}parseObjectData(t,e,i,s){const{xScale:o,yScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(s);let h,u,d,f;for(h=0,u=s;h<u;++h)d=h+i,f=e[d],c[h]={x:o.parse(ae(f,a),d),y:r.parse(ae(f,l),d)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,i){const s=this.chart,o=this._cachedMeta,r=e[t.axis],a={keys:Ll(s,!0),values:e._stacks[t.axis]._visualValues};return dr(a,r,o.index,{mode:i})}updateRangeFromParsed(t,e,i,s){const o=i[e.axis];let r=o===null?NaN:o;const a=s&&i._stacks[e.axis];s&&a&&(s.values=a,r=dr(s,o,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,e){const i=this._cachedMeta,s=i._parsed,o=i._sorted&&t===i.iScale,r=s.length,a=this._getOtherScale(t),l=zd(e,i,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:u}=Ld(a);let d,f;function g(){f=s[d];const m=f[a.axis];return!q(f[t.axis])||h>m||u<m}for(d=0;d<r&&!(!g()&&(this.updateRangeFromParsed(c,t,f,l),o));++d);if(o){for(d=r-1;d>=0;--d)if(!g()){this.updateRangeFromParsed(c,t,f,l);break}}return c}getAllParsedValues(t){const e=this._cachedMeta._parsed,i=[];let s,o,r;for(s=0,o=e.length;s<o;++s)r=e[s][t.axis],q(r)&&i.push(r);return i}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,i=e.iScale,s=e.vScale,o=this.getParsed(t);return{label:i?""+i.getLabelForValue(o[i.axis]):"",value:s?""+s.getLabelForValue(o[s.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=Ad(C(this.options.clip,Pd(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,e=this.chart,i=this._cachedMeta,s=i.data||[],o=e.chartArea,r=[],a=this._drawStart||0,l=this._drawCount||s.length-a,c=this.options.drawActiveElementsOnTop;let h;for(i.dataset&&i.dataset.draw(t,o,a,l),h=a;h<a+l;++h){const u=s[h];u.hidden||(u.active&&c?r.push(u):u.draw(t,o))}for(h=0;h<r.length;++h)r[h].draw(t,o)}getStyle(t,e){const i=e?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(t||0,i)}getContext(t,e,i){const s=this.getDataset();let o;if(t>=0&&t<this._cachedMeta.data.length){const r=this._cachedMeta.data[t];o=r.$context||(r.$context=Nd(this.getContext(),t,r)),o.parsed=this.getParsed(t),o.raw=s.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=Fd(this.chart.getContext(),this.index)),o.dataset=s,o.index=o.datasetIndex=this.index;return o.active=!!e,o.mode=i,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",i){const s=e==="active",o=this._cachedDataOpts,r=t+"-"+e,a=o[r],l=this.enableOptionSharing&&pt(i);if(a)return mr(a,l);const c=this.chart.config,h=c.datasetElementScopeKeys(this._type,t),u=s?[`${t}Hover`,"hover",t,""]:[t,""],d=c.getOptionScopes(this.getDataset(),h),f=Object.keys(Y.elements[t]),g=()=>this.getContext(i,s,e),m=c.resolveNamedOptions(d,f,g,u);return m.$shared&&(m.$shared=l,o[r]=Object.freeze(mr(m,l))),m}_resolveAnimations(t,e,i){const s=this.chart,o=this._cachedDataOpts,r=`animation-${e}`,a=o[r];if(a)return a;let l;if(s.options.animation!==!1){const h=this.chart.config,u=h.datasetAnimationScopeKeys(this._type,e),d=h.getOptionScopes(this.getDataset(),u);l=h.createResolver(d,this.getContext(t,i,e))}const c=new So(s,l&&l.animations);return l&&l._cacheable&&(o[r]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||hs(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const i=this.resolveDataElementOptions(t,e),s=this._sharedOptions,o=this.getSharedOptions(i),r=this.includeOptions(e,o)||o!==s;return this.updateSharedOptions(o,e,i),{sharedOptions:o,includeOptions:r}}updateElement(t,e,i,s){hs(s)?Object.assign(t,i):this._resolveAnimations(e,s).update(t,i)}updateSharedOptions(t,e,i){t&&!hs(e)&&this._resolveAnimations(void 0,e).update(t,i)}_setStyle(t,e,i,s){t.active=s;const o=this.getStyle(e,s);this._resolveAnimations(e,i,s).update(t,{options:!s&&this.getSharedOptions(o)||o})}removeHoverStyle(t,e,i){this._setStyle(t,i,"active",!1)}setHoverStyle(t,e,i){this._setStyle(t,i,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,i=this._cachedMeta.data;for(const[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];const s=i.length,o=e.length,r=Math.min(o,s);r&&this.parse(0,r),o>s?this._insertElements(s,o-s,t):o<s&&this._removeElements(o,s-o)}_insertElements(t,e,i=!0){const s=this._cachedMeta,o=s.data,r=t+e;let a;const l=c=>{for(c.length+=e,a=c.length-1;a>=r;a--)c[a]=c[a-e]};for(l(o),a=t;a<r;++a)o[a]=new this.dataElementType;this._parsing&&l(s._parsed),this.parse(t,e),i&&this.updateElements(o,t,e,"reset")}updateElements(t,e,i,s){}_removeElements(t,e){const i=this._cachedMeta;if(this._parsing){const s=i._parsed.splice(t,e);i._stacked&&sn(i,s)}i.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,i,s]=t;this[e](i,s)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const i=arguments.length-2;i&&this._sync(["_insertElements",t,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}v(At,"defaults",{}),v(At,"datasetElementType",null),v(At,"dataElementType",null);function Wd(n,t){if(!n._cache.$bar){const e=n.getMatchingVisibleMetas(t);let i=[];for(let s=0,o=e.length;s<o;s++)i=i.concat(e[s].controller.getAllParsedValues(n));n._cache.$bar=gl(i.sort((s,o)=>s-o))}return n._cache.$bar}function Vd(n){const t=n.iScale,e=Wd(t,n.type);let i=t._length,s,o,r,a;const l=()=>{r===32767||r===-32768||(pt(a)&&(i=Math.min(i,Math.abs(r-a)||i)),a=r)};for(s=0,o=e.length;s<o;++s)r=t.getPixelForValue(e[s]),l();for(a=void 0,s=0,o=t.ticks.length;s<o;++s)r=t.getPixelForTick(s),l();return i}function Bd(n,t,e,i){const s=e.barThickness;let o,r;return I(s)?(o=t.min*e.categoryPercentage,r=e.barPercentage):(o=s*i,r=1),{chunk:o/i,ratio:r,start:t.pixels[n]-o/2}}function Hd(n,t,e,i){const s=t.pixels,o=s[n];let r=n>0?s[n-1]:null,a=n<s.length-1?s[n+1]:null;const l=e.categoryPercentage;r===null&&(r=o-(a===null?t.end-t.start:a-o)),a===null&&(a=o+o-r);const c=o-(o-Math.min(r,a))/2*l;return{chunk:Math.abs(a-r)/2*l/i,ratio:e.barPercentage,start:c}}function $d(n,t,e,i){const s=e.parse(n[0],i),o=e.parse(n[1],i),r=Math.min(s,o),a=Math.max(s,o);let l=r,c=a;Math.abs(r)>Math.abs(a)&&(l=a,c=r),t[e.axis]=c,t._custom={barStart:l,barEnd:c,start:s,end:o,min:r,max:a}}function Rl(n,t,e,i){return V(n)?$d(n,t,e,i):t[e.axis]=e.parse(n,i),t}function pr(n,t,e,i){const s=n.iScale,o=n.vScale,r=s.getLabels(),a=s===o,l=[];let c,h,u,d;for(c=e,h=e+i;c<h;++c)d=t[c],u={},u[s.axis]=a||s.parse(r[c],c),l.push(Rl(d,u,o,c));return l}function us(n){return n&&n.barStart!==void 0&&n.barEnd!==void 0}function jd(n,t,e){return n!==0?Rt(n):(t.isHorizontal()?1:-1)*(t.min>=e?1:-1)}function Yd(n){let t,e,i,s,o;return n.horizontal?(t=n.base>n.x,e="left",i="right"):(t=n.base<n.y,e="bottom",i="top"),t?(s="end",o="start"):(s="start",o="end"),{start:e,end:i,reverse:t,top:s,bottom:o}}function Ud(n,t,e,i){let s=t.borderSkipped;const o={};if(!s){n.borderSkipped=o;return}if(s===!0){n.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:r,end:a,reverse:l,top:c,bottom:h}=Yd(n);s==="middle"&&e&&(n.enableBorderRadius=!0,(e._top||0)===i?s=c:(e._bottom||0)===i?s=h:(o[yr(h,r,a,l)]=!0,s=c)),o[yr(s,r,a,l)]=!0,n.borderSkipped=o}function yr(n,t,e,i){return i?(n=Xd(n,t,e),n=br(n,e,t)):n=br(n,t,e),n}function Xd(n,t,e){return n===t?e:n===e?t:n}function br(n,t,e){return n==="start"?t:n==="end"?e:n}function Zd(n,{inflateAmount:t},e){n.inflateAmount=t==="auto"?e===1?.33:0:t}class xi extends At{parsePrimitiveData(t,e,i,s){return pr(t,e,i,s)}parseArrayData(t,e,i,s){return pr(t,e,i,s)}parseObjectData(t,e,i,s){const{iScale:o,vScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=o.axis==="x"?a:l,h=r.axis==="x"?a:l,u=[];let d,f,g,m;for(d=i,f=i+s;d<f;++d)m=e[d],g={},g[o.axis]=o.parse(ae(m,c),d),u.push(Rl(ae(m,h),g,r,d));return u}updateRangeFromParsed(t,e,i,s){super.updateRangeFromParsed(t,e,i,s);const o=i._custom;o&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,o.min),t.max=Math.max(t.max,o.max))}getMaxOverflow(){return 0}getLabelAndValue(t){const e=this._cachedMeta,{iScale:i,vScale:s}=e,o=this.getParsed(t),r=o._custom,a=us(r)?"["+r.start+", "+r.end+"]":""+s.getLabelForValue(o[s.axis]);return{label:""+i.getLabelForValue(o[i.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize();const t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){const e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,i,s){const o=s==="reset",{index:r,_cachedMeta:{vScale:a}}=this,l=a.getBasePixel(),c=a.isHorizontal(),h=this._getRuler(),{sharedOptions:u,includeOptions:d}=this._getSharedOptions(e,s);for(let f=e;f<e+i;f++){const g=this.getParsed(f),m=o||I(g[a.axis])?{base:l,head:l}:this._calculateBarValuePixels(f),p=this._calculateBarIndexPixels(f,h),y=(g._stacks||{})[a.axis],b={horizontal:c,base:m.base,enableBorderRadius:!y||us(g._custom)||r===y._top||r===y._bottom,x:c?m.head:p.center,y:c?p.center:m.head,height:c?p.size:Math.abs(m.size),width:c?Math.abs(m.size):p.size};d&&(b.options=u||this.resolveDataElementOptions(f,t[f].active?"active":s));const _=b.options||t[f].options;Ud(b,_,y,r),Zd(b,_,h.ratio),this.updateElement(t[f],f,b,s)}}_getStacks(t,e){const{iScale:i}=this._cachedMeta,s=i.getMatchingVisibleMetas(this._type).filter(h=>h.controller.options.grouped),o=i.options.stacked,r=[],a=this._cachedMeta.controller.getParsed(e),l=a&&a[i.axis],c=h=>{const u=h._parsed.find(f=>f[i.axis]===l),d=u&&u[h.vScale.axis];if(I(d)||isNaN(d))return!0};for(const h of s)if(!(e!==void 0&&c(h))&&((o===!1||r.indexOf(h.stack)===-1||o===void 0&&h.stack===void 0)&&r.push(h.stack),h.index===t))break;return r.length||r.push(void 0),r}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,e,i){const s=this._getStacks(t,i),o=e!==void 0?s.indexOf(e):-1;return o===-1?s.length-1:o}_getRuler(){const t=this.options,e=this._cachedMeta,i=e.iScale,s=[];let o,r;for(o=0,r=e.data.length;o<r;++o)s.push(i.getPixelForValue(this.getParsed(o)[i.axis],o));const a=t.barThickness;return{min:a||Vd(e),pixels:s,start:i._startPixel,end:i._endPixel,stackCount:this._getStackCount(),scale:i,grouped:t.grouped,ratio:a?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:e,_stacked:i,index:s},options:{base:o,minBarLength:r}}=this,a=o||0,l=this.getParsed(t),c=l._custom,h=us(c);let u=l[e.axis],d=0,f=i?this.applyStack(e,l,i):u,g,m;f!==u&&(d=f-u,f=u),h&&(u=c.barStart,f=c.barEnd-c.barStart,u!==0&&Rt(u)!==Rt(c.barEnd)&&(d=0),d+=u);const p=!I(o)&&!h?o:d;let y=e.getPixelForValue(p);if(this.chart.getDataVisibility(t)?g=e.getPixelForValue(d+f):g=y,m=g-y,Math.abs(m)<r){m=jd(m,e,a)*r,u===a&&(y-=m/2);const b=e.getPixelForDecimal(0),_=e.getPixelForDecimal(1),w=Math.min(b,_),x=Math.max(b,_);y=Math.max(Math.min(y,x),w),g=y+m,i&&!h&&(l._stacks[e.axis]._visualValues[s]=e.getValueForPixel(g)-e.getValueForPixel(y))}if(y===e.getPixelForValue(a)){const b=Rt(m)*e.getLineWidthForValue(a)/2;y+=b,m-=b}return{size:m,base:y,head:g,center:g+m/2}}_calculateBarIndexPixels(t,e){const i=e.scale,s=this.options,o=s.skipNull,r=C(s.maxBarThickness,1/0);let a,l;if(e.grouped){const c=o?this._getStackCount(t):e.stackCount,h=s.barThickness==="flex"?Hd(t,e,s,c):Bd(t,e,s,c),u=this._getStackIndex(this.index,this._cachedMeta.stack,o?t:void 0);a=h.start+h.chunk*u+h.chunk/2,l=Math.min(r,h.chunk*h.ratio)}else a=i.getPixelForValue(this.getParsed(t)[i.axis],t),l=Math.min(r,e.min*e.ratio);return{base:a-l/2,head:a+l/2,center:a,size:l}}draw(){const t=this._cachedMeta,e=t.vScale,i=t.data,s=i.length;let o=0;for(;o<s;++o)this.getParsed(o)[e.axis]!==null&&!i[o].hidden&&i[o].draw(this._ctx)}}v(xi,"id","bar"),v(xi,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),v(xi,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});class _i extends At{initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,i,s){const o=super.parsePrimitiveData(t,e,i,s);for(let r=0;r<o.length;r++)o[r]._custom=this.resolveDataElementOptions(r+i).radius;return o}parseArrayData(t,e,i,s){const o=super.parseArrayData(t,e,i,s);for(let r=0;r<o.length;r++){const a=e[i+r];o[r]._custom=C(a[2],this.resolveDataElementOptions(r+i).radius)}return o}parseObjectData(t,e,i,s){const o=super.parseObjectData(t,e,i,s);for(let r=0;r<o.length;r++){const a=e[i+r];o[r]._custom=C(a&&a.r&&+a.r,this.resolveDataElementOptions(r+i).radius)}return o}getMaxOverflow(){const t=this._cachedMeta.data;let e=0;for(let i=t.length-1;i>=0;--i)e=Math.max(e,t[i].size(this.resolveDataElementOptions(i))/2);return e>0&&e}getLabelAndValue(t){const e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:o}=e,r=this.getParsed(t),a=s.getLabelForValue(r.x),l=o.getLabelForValue(r.y),c=r._custom;return{label:i[t]||"",value:"("+a+", "+l+(c?", "+c:"")+")"}}update(t){const e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,i,s){const o=s==="reset",{iScale:r,vScale:a}=this._cachedMeta,{sharedOptions:l,includeOptions:c}=this._getSharedOptions(e,s),h=r.axis,u=a.axis;for(let d=e;d<e+i;d++){const f=t[d],g=!o&&this.getParsed(d),m={},p=m[h]=o?r.getPixelForDecimal(.5):r.getPixelForValue(g[h]),y=m[u]=o?a.getBasePixel():a.getPixelForValue(g[u]);m.skip=isNaN(p)||isNaN(y),c&&(m.options=l||this.resolveDataElementOptions(d,f.active?"active":s),o&&(m.options.radius=0)),this.updateElement(f,d,m,s)}}resolveDataElementOptions(t,e){const i=this.getParsed(t);let s=super.resolveDataElementOptions(t,e);s.$shared&&(s=Object.assign({},s,{$shared:!1}));const o=s.radius;return e!=="active"&&(s.radius=0),s.radius+=C(i&&i._custom,o),s}}v(_i,"id","bubble"),v(_i,"defaults",{datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}}),v(_i,"overrides",{scales:{x:{type:"linear"},y:{type:"linear"}}});function qd(n,t,e){let i=1,s=1,o=0,r=0;if(t<j){const a=n,l=a+t,c=Math.cos(a),h=Math.sin(a),u=Math.cos(l),d=Math.sin(l),f=(_,w,x)=>En(_,a,l,!0)?1:Math.max(w,w*e,x,x*e),g=(_,w,x)=>En(_,a,l,!0)?-1:Math.min(w,w*e,x,x*e),m=f(0,c,u),p=f(X,h,d),y=g(R,c,u),b=g(R+X,h,d);i=(m-y)/2,s=(p-b)/2,o=-(m+y)/2,r=-(p+b)/2}return{ratioX:i,ratioY:s,offsetX:o,offsetY:r}}class te extends At{constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){const i=this.getDataset().data,s=this._cachedMeta;if(this._parsing===!1)s._parsed=i;else{let o=l=>+i[l];if(E(i[t])){const{key:l="value"}=this._parsing;o=c=>+ae(i[c],l)}let r,a;for(r=t,a=t+e;r<a;++r)s._parsed[r]=o(r)}}_getRotation(){return nt(this.options.rotation-90)}_getCircumference(){return nt(this.options.circumference)}_getRotationExtents(){let t=j,e=-j;for(let i=0;i<this.chart.data.datasets.length;++i)if(this.chart.isDatasetVisible(i)&&this.chart.getDatasetMeta(i).type===this._type){const s=this.chart.getDatasetMeta(i).controller,o=s._getRotation(),r=s._getCircumference();t=Math.min(t,o),e=Math.max(e,o+r)}return{rotation:t,circumference:e-t}}update(t){const e=this.chart,{chartArea:i}=e,s=this._cachedMeta,o=s.data,r=this.getMaxBorderWidth()+this.getMaxOffset(o)+this.options.spacing,a=Math.max((Math.min(i.width,i.height)-r)/2,0),l=Math.min(lu(this.options.cutout,a),1),c=this._getRingWeight(this.index),{circumference:h,rotation:u}=this._getRotationExtents(),{ratioX:d,ratioY:f,offsetX:g,offsetY:m}=qd(u,h,l),p=(i.width-r)/d,y=(i.height-r)/f,b=Math.max(Math.min(p,y)/2,0),_=hl(this.options.radius,b),w=Math.max(_*l,0),x=(_-w)/this._getVisibleDatasetWeightTotal();this.offsetX=g*_,this.offsetY=m*_,s.total=this.calculateTotal(),this.outerRadius=_-x*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-x*c,0),this.updateElements(o,0,o.length,t)}_circumference(t,e){const i=this.options,s=this._cachedMeta,o=this._getCircumference();return e&&i.animation.animateRotate||!this.chart.getDataVisibility(t)||s._parsed[t]===null||s.data[t].hidden?0:this.calculateCircumference(s._parsed[t]*o/j)}updateElements(t,e,i,s){const o=s==="reset",r=this.chart,a=r.chartArea,c=r.options.animation,h=(a.left+a.right)/2,u=(a.top+a.bottom)/2,d=o&&c.animateScale,f=d?0:this.innerRadius,g=d?0:this.outerRadius,{sharedOptions:m,includeOptions:p}=this._getSharedOptions(e,s);let y=this._getRotation(),b;for(b=0;b<e;++b)y+=this._circumference(b,o);for(b=e;b<e+i;++b){const _=this._circumference(b,o),w=t[b],x={x:h+this.offsetX,y:u+this.offsetY,startAngle:y,endAngle:y+_,circumference:_,outerRadius:g,innerRadius:f};p&&(x.options=m||this.resolveDataElementOptions(b,w.active?"active":s)),y+=_,this.updateElement(w,b,x,s)}}calculateTotal(){const t=this._cachedMeta,e=t.data;let i=0,s;for(s=0;s<e.length;s++){const o=t._parsed[s];o!==null&&!isNaN(o)&&this.chart.getDataVisibility(s)&&!e[s].hidden&&(i+=Math.abs(o))}return i}calculateCircumference(t){const e=this._cachedMeta.total;return e>0&&!isNaN(t)?j*(Math.abs(t)/e):0}getLabelAndValue(t){const e=this._cachedMeta,i=this.chart,s=i.data.labels||[],o=zn(e._parsed[t],i.options.locale);return{label:s[t]||"",value:o}}getMaxBorderWidth(t){let e=0;const i=this.chart;let s,o,r,a,l;if(!t){for(s=0,o=i.data.datasets.length;s<o;++s)if(i.isDatasetVisible(s)){r=i.getDatasetMeta(s),t=r.data,a=r.controller;break}}if(!t)return 0;for(s=0,o=t.length;s<o;++s)l=a.resolveDataElementOptions(s),l.borderAlign!=="inner"&&(e=Math.max(e,l.borderWidth||0,l.hoverBorderWidth||0));return e}getMaxOffset(t){let e=0;for(let i=0,s=t.length;i<s;++i){const o=this.resolveDataElementOptions(i);e=Math.max(e,o.offset||0,o.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let i=0;i<t;++i)this.chart.isDatasetVisible(i)&&(e+=this._getRingWeight(i));return e}_getRingWeight(t){return Math.max(C(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}v(te,"id","doughnut"),v(te,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),v(te,"descriptors",{_scriptable:t=>t!=="spacing",_indexable:t=>t!=="spacing"&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")}),v(te,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:i,color:s}}=t.legend.options;return e.labels.map((o,r)=>{const l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:s,lineWidth:l.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}}});class wi extends At{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const e=this._cachedMeta,{dataset:i,data:s=[],_dataset:o}=e,r=this.chart._animationsDisabled;let{start:a,count:l}=yl(e,s,r);this._drawStart=a,this._drawCount=l,bl(e)&&(a=0,l=s.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!o._decimated,i.points=s;const c=this.resolveDatasetElementOptions(t);this.options.showLine||(c.borderWidth=0),c.segment=this.options.segment,this.updateElement(i,void 0,{animated:!r,options:c},t),this.updateElements(s,a,l,t)}updateElements(t,e,i,s){const o=s==="reset",{iScale:r,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,{sharedOptions:h,includeOptions:u}=this._getSharedOptions(e,s),d=r.axis,f=a.axis,{spanGaps:g,segment:m}=this.options,p=le(g)?g:Number.POSITIVE_INFINITY,y=this.chart._animationsDisabled||o||s==="none",b=e+i,_=t.length;let w=e>0&&this.getParsed(e-1);for(let x=0;x<_;++x){const k=t[x],S=y?k:{};if(x<e||x>=b){S.skip=!0;continue}const M=this.getParsed(x),O=I(M[f]),A=S[d]=r.getPixelForValue(M[d],x),L=S[f]=o||O?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,M,l):M[f],x);S.skip=isNaN(A)||isNaN(L)||O,S.stop=x>0&&Math.abs(M[d]-w[d])>p,m&&(S.parsed=M,S.raw=c.data[x]),u&&(S.options=h||this.resolveDataElementOptions(x,k.active?"active":s)),y||this.updateElement(k,x,S,s),w=M}}getMaxOverflow(){const t=this._cachedMeta,e=t.dataset,i=e.options&&e.options.borderWidth||0,s=t.data||[];if(!s.length)return i;const o=s[0].size(this.resolveDataElementOptions(0)),r=s[s.length-1].size(this.resolveDataElementOptions(s.length-1));return Math.max(i,o,r)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}v(wi,"id","line"),v(wi,"defaults",{datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1}),v(wi,"overrides",{scales:{_index_:{type:"category"},_value_:{type:"linear"}}});class Mn extends At{constructor(t,e){super(t,e),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){const e=this._cachedMeta,i=this.chart,s=i.data.labels||[],o=zn(e._parsed[t].r,i.options.locale);return{label:s[t]||"",value:o}}parseObjectData(t,e,i,s){return Tl.bind(this)(t,e,i,s)}update(t){const e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){const t=this._cachedMeta,e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((i,s)=>{const o=this.getParsed(s).r;!isNaN(o)&&this.chart.getDataVisibility(s)&&(o<e.min&&(e.min=o),o>e.max&&(e.max=o))}),e}_updateRadius(){const t=this.chart,e=t.chartArea,i=t.options,s=Math.min(e.right-e.left,e.bottom-e.top),o=Math.max(s/2,0),r=Math.max(i.cutoutPercentage?o/100*i.cutoutPercentage:1,0),a=(o-r)/t.getVisibleDatasetCount();this.outerRadius=o-a*this.index,this.innerRadius=this.outerRadius-a}updateElements(t,e,i,s){const o=s==="reset",r=this.chart,l=r.options.animation,c=this._cachedMeta.rScale,h=c.xCenter,u=c.yCenter,d=c.getIndexAngle(0)-.5*R;let f=d,g;const m=360/this.countVisibleElements();for(g=0;g<e;++g)f+=this._computeAngle(g,s,m);for(g=e;g<e+i;g++){const p=t[g];let y=f,b=f+this._computeAngle(g,s,m),_=r.getDataVisibility(g)?c.getDistanceFromCenterForValue(this.getParsed(g).r):0;f=b,o&&(l.animateScale&&(_=0),l.animateRotate&&(y=b=d));const w={x:h,y:u,innerRadius:0,outerRadius:_,startAngle:y,endAngle:b,options:this.resolveDataElementOptions(g,p.active?"active":s)};this.updateElement(p,g,w,s)}}countVisibleElements(){const t=this._cachedMeta;let e=0;return t.data.forEach((i,s)=>{!isNaN(this.getParsed(s).r)&&this.chart.getDataVisibility(s)&&e++}),e}_computeAngle(t,e,i){return this.chart.getDataVisibility(t)?nt(this.resolveDataElementOptions(t,e).angle||i):0}}v(Mn,"id","polarArea"),v(Mn,"defaults",{dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0}),v(Mn,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:i,color:s}}=t.legend.options;return e.labels.map((o,r)=>{const l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:s,lineWidth:l.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}});class Es extends te{}v(Es,"id","pie"),v(Es,"defaults",{cutout:0,rotation:0,circumference:360,radius:"100%"});class vi extends At{getLabelAndValue(t){const e=this._cachedMeta.vScale,i=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(i[e.axis])}}parseObjectData(t,e,i,s){return Tl.bind(this)(t,e,i,s)}update(t){const e=this._cachedMeta,i=e.dataset,s=e.data||[],o=e.iScale.getLabels();if(i.points=s,t!=="resize"){const r=this.resolveDatasetElementOptions(t);this.options.showLine||(r.borderWidth=0);const a={_loop:!0,_fullLoop:o.length===s.length,options:r};this.updateElement(i,void 0,a,t)}this.updateElements(s,0,s.length,t)}updateElements(t,e,i,s){const o=this._cachedMeta.rScale,r=s==="reset";for(let a=e;a<e+i;a++){const l=t[a],c=this.resolveDataElementOptions(a,l.active?"active":s),h=o.getPointPositionForValue(a,this.getParsed(a).r),u=r?o.xCenter:h.x,d=r?o.yCenter:h.y,f={x:u,y:d,angle:h.angle,skip:isNaN(u)||isNaN(d),options:c};this.updateElement(l,a,f,s)}}}v(vi,"id","radar"),v(vi,"defaults",{datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}}),v(vi,"overrides",{aspectRatio:1,scales:{r:{type:"radialLinear"}}});class ki extends At{getLabelAndValue(t){const e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:o}=e,r=this.getParsed(t),a=s.getLabelForValue(r.x),l=o.getLabelForValue(r.y);return{label:i[t]||"",value:"("+a+", "+l+")"}}update(t){const e=this._cachedMeta,{data:i=[]}=e,s=this.chart._animationsDisabled;let{start:o,count:r}=yl(e,i,s);if(this._drawStart=o,this._drawCount=r,bl(e)&&(o=0,r=i.length),this.options.showLine){this.datasetElementType||this.addElements();const{dataset:a,_dataset:l}=e;a._chart=this.chart,a._datasetIndex=this.index,a._decimated=!!l._decimated,a.points=i;const c=this.resolveDatasetElementOptions(t);c.segment=this.options.segment,this.updateElement(a,void 0,{animated:!s,options:c},t)}else this.datasetElementType&&(delete e.dataset,this.datasetElementType=!1);this.updateElements(i,o,r,t)}addElements(){const{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,e,i,s){const o=s==="reset",{iScale:r,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,h=this.resolveDataElementOptions(e,s),u=this.getSharedOptions(h),d=this.includeOptions(s,u),f=r.axis,g=a.axis,{spanGaps:m,segment:p}=this.options,y=le(m)?m:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||o||s==="none";let _=e>0&&this.getParsed(e-1);for(let w=e;w<e+i;++w){const x=t[w],k=this.getParsed(w),S=b?x:{},M=I(k[g]),O=S[f]=r.getPixelForValue(k[f],w),A=S[g]=o||M?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,k,l):k[g],w);S.skip=isNaN(O)||isNaN(A)||M,S.stop=w>0&&Math.abs(k[f]-_[f])>y,p&&(S.parsed=k,S.raw=c.data[w]),d&&(S.options=u||this.resolveDataElementOptions(w,x.active?"active":s)),b||this.updateElement(x,w,S,s),_=k}this.updateSharedOptions(u,s,h)}getMaxOverflow(){const t=this._cachedMeta,e=t.data||[];if(!this.options.showLine){let a=0;for(let l=e.length-1;l>=0;--l)a=Math.max(a,e[l].size(this.resolveDataElementOptions(l))/2);return a>0&&a}const i=t.dataset,s=i.options&&i.options.borderWidth||0;if(!e.length)return s;const o=e[0].size(this.resolveDataElementOptions(0)),r=e[e.length-1].size(this.resolveDataElementOptions(e.length-1));return Math.max(s,o,r)/2}}v(ki,"id","scatter"),v(ki,"defaults",{datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1}),v(ki,"overrides",{interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}});var Gd=Object.freeze({__proto__:null,BarController:xi,BubbleController:_i,DoughnutController:te,LineController:wi,PieController:Es,PolarAreaController:Mn,RadarController:vi,ScatterController:ki});function ge(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class Mo{constructor(t){v(this,"options");this.options=t||{}}static override(t){Object.assign(Mo.prototype,t)}init(){}formats(){return ge()}parse(){return ge()}format(){return ge()}add(){return ge()}diff(){return ge()}startOf(){return ge()}endOf(){return ge()}}var Fl={_date:Mo};function Jd(n,t,e,i){const{controller:s,data:o,_sorted:r}=n,a=s._cachedMeta.iScale,l=n.dataset&&n.dataset.options?n.dataset.options.spanGaps:null;if(a&&t===a.axis&&t!=="r"&&r&&o.length){const c=a._reversePixels?_u:Ht;if(i){if(s._sharedOptions){const h=o[0],u=typeof h.getRange=="function"&&h.getRange(t);if(u){const d=c(o,t,e-u),f=c(o,t,e+u);return{lo:d.lo,hi:f.hi}}}}else{const h=c(o,t,e);if(l){const{vScale:u}=s._cachedMeta,{_parsed:d}=n,f=d.slice(0,h.lo+1).reverse().findIndex(m=>!I(m[u.axis]));h.lo-=Math.max(0,f);const g=d.slice(h.hi).findIndex(m=>!I(m[u.axis]));h.hi+=Math.max(0,g)}return h}}return{lo:0,hi:o.length-1}}function Bn(n,t,e,i,s){const o=n.getSortedVisibleDatasetMetas(),r=e[t];for(let a=0,l=o.length;a<l;++a){const{index:c,data:h}=o[a],{lo:u,hi:d}=Jd(o[a],t,r,s);for(let f=u;f<=d;++f){const g=h[f];g.skip||i(g,c,f)}}}function Kd(n){const t=n.indexOf("x")!==-1,e=n.indexOf("y")!==-1;return function(i,s){const o=t?Math.abs(i.x-s.x):0,r=e?Math.abs(i.y-s.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(r,2))}}function ds(n,t,e,i,s){const o=[];return!s&&!n.isPointInArea(t)||Bn(n,e,t,function(a,l,c){!s&&!$t(a,n.chartArea,0)||a.inRange(t.x,t.y,i)&&o.push({element:a,datasetIndex:l,index:c})},!0),o}function Qd(n,t,e,i){let s=[];function o(r,a,l){const{startAngle:c,endAngle:h}=r.getProps(["startAngle","endAngle"],i),{angle:u}=Ei(r,{x:t.x,y:t.y});En(u,c,h)&&s.push({element:r,datasetIndex:a,index:l})}return Bn(n,e,t,o),s}function tf(n,t,e,i,s,o){let r=[];const a=Kd(e);let l=Number.POSITIVE_INFINITY;function c(h,u,d){const f=h.inRange(t.x,t.y,s);if(i&&!f)return;const g=h.getCenterPoint(s);if(!(!!o||n.isPointInArea(g))&&!f)return;const p=a(t,g);p<l?(r=[{element:h,datasetIndex:u,index:d}],l=p):p===l&&r.push({element:h,datasetIndex:u,index:d})}return Bn(n,e,t,c),r}function fs(n,t,e,i,s,o){return!o&&!n.isPointInArea(t)?[]:e==="r"&&!i?Qd(n,t,e,s):tf(n,t,e,i,s,o)}function xr(n,t,e,i,s){const o=[],r=e==="x"?"inXRange":"inYRange";let a=!1;return Bn(n,e,t,(l,c,h)=>{l[r]&&l[r](t[e],s)&&(o.push({element:l,datasetIndex:c,index:h}),a=a||l.inRange(t.x,t.y,s))}),i&&!a?[]:o}var ef={evaluateInteractionItems:Bn,modes:{index(n,t,e,i){const s=be(t,n),o=e.axis||"x",r=e.includeInvisible||!1,a=e.intersect?ds(n,s,o,i,r):fs(n,s,o,!1,i,r),l=[];return a.length?(n.getSortedVisibleDatasetMetas().forEach(c=>{const h=a[0].index,u=c.data[h];u&&!u.skip&&l.push({element:u,datasetIndex:c.index,index:h})}),l):[]},dataset(n,t,e,i){const s=be(t,n),o=e.axis||"xy",r=e.includeInvisible||!1;let a=e.intersect?ds(n,s,o,i,r):fs(n,s,o,!1,i,r);if(a.length>0){const l=a[0].datasetIndex,c=n.getDatasetMeta(l).data;a=[];for(let h=0;h<c.length;++h)a.push({element:c[h],datasetIndex:l,index:h})}return a},point(n,t,e,i){const s=be(t,n),o=e.axis||"xy",r=e.includeInvisible||!1;return ds(n,s,o,i,r)},nearest(n,t,e,i){const s=be(t,n),o=e.axis||"xy",r=e.includeInvisible||!1;return fs(n,s,o,e.intersect,i,r)},x(n,t,e,i){const s=be(t,n);return xr(n,s,"x",e.intersect,i)},y(n,t,e,i){const s=be(t,n);return xr(n,s,"y",e.intersect,i)}}};const Nl=["left","top","right","bottom"];function on(n,t){return n.filter(e=>e.pos===t)}function _r(n,t){return n.filter(e=>Nl.indexOf(e.pos)===-1&&e.box.axis===t)}function rn(n,t){return n.sort((e,i)=>{const s=t?i:e,o=t?e:i;return s.weight===o.weight?s.index-o.index:s.weight-o.weight})}function nf(n){const t=[];let e,i,s,o,r,a;for(e=0,i=(n||[]).length;e<i;++e)s=n[e],{position:o,options:{stack:r,stackWeight:a=1}}=s,t.push({index:e,box:s,pos:o,horizontal:s.isHorizontal(),weight:s.weight,stack:r&&o+r,stackWeight:a});return t}function sf(n){const t={};for(const e of n){const{stack:i,pos:s,stackWeight:o}=e;if(!i||!Nl.includes(s))continue;const r=t[i]||(t[i]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=o}return t}function of(n,t){const e=sf(n),{vBoxMaxWidth:i,hBoxMaxHeight:s}=t;let o,r,a;for(o=0,r=n.length;o<r;++o){a=n[o];const{fullSize:l}=a.box,c=e[a.stack],h=c&&a.stackWeight/c.weight;a.horizontal?(a.width=h?h*i:l&&t.availableWidth,a.height=s):(a.width=i,a.height=h?h*s:l&&t.availableHeight)}return e}function rf(n){const t=nf(n),e=rn(t.filter(c=>c.box.fullSize),!0),i=rn(on(t,"left"),!0),s=rn(on(t,"right")),o=rn(on(t,"top"),!0),r=rn(on(t,"bottom")),a=_r(t,"x"),l=_r(t,"y");return{fullSize:e,leftAndTop:i.concat(o),rightAndBottom:s.concat(l).concat(r).concat(a),chartArea:on(t,"chartArea"),vertical:i.concat(s).concat(l),horizontal:o.concat(r).concat(a)}}function wr(n,t,e,i){return Math.max(n[e],t[e])+Math.max(n[i],t[i])}function zl(n,t){n.top=Math.max(n.top,t.top),n.left=Math.max(n.left,t.left),n.bottom=Math.max(n.bottom,t.bottom),n.right=Math.max(n.right,t.right)}function af(n,t,e,i){const{pos:s,box:o}=e,r=n.maxPadding;if(!E(s)){e.size&&(n[s]-=e.size);const u=i[e.stack]||{size:0,count:1};u.size=Math.max(u.size,e.horizontal?o.height:o.width),e.size=u.size/u.count,n[s]+=e.size}o.getPadding&&zl(r,o.getPadding());const a=Math.max(0,t.outerWidth-wr(r,n,"left","right")),l=Math.max(0,t.outerHeight-wr(r,n,"top","bottom")),c=a!==n.w,h=l!==n.h;return n.w=a,n.h=l,e.horizontal?{same:c,other:h}:{same:h,other:c}}function lf(n){const t=n.maxPadding;function e(i){const s=Math.max(t[i]-n[i],0);return n[i]+=s,s}n.y+=e("top"),n.x+=e("left"),e("right"),e("bottom")}function cf(n,t){const e=t.maxPadding;function i(s){const o={left:0,top:0,right:0,bottom:0};return s.forEach(r=>{o[r]=Math.max(t[r],e[r])}),o}return i(n?["left","right"]:["top","bottom"])}function gn(n,t,e,i){const s=[];let o,r,a,l,c,h;for(o=0,r=n.length,c=0;o<r;++o){a=n[o],l=a.box,l.update(a.width||t.w,a.height||t.h,cf(a.horizontal,t));const{same:u,other:d}=af(t,e,a,i);c|=u&&s.length,h=h||d,l.fullSize||s.push(a)}return c&&gn(s,t,e,i)||h}function ii(n,t,e,i,s){n.top=e,n.left=t,n.right=t+i,n.bottom=e+s,n.width=i,n.height=s}function vr(n,t,e,i){const s=e.padding;let{x:o,y:r}=t;for(const a of n){const l=a.box,c=i[a.stack]||{count:1,placed:0,weight:1},h=a.stackWeight/c.weight||1;if(a.horizontal){const u=t.w*h,d=c.size||l.height;pt(c.start)&&(r=c.start),l.fullSize?ii(l,s.left,r,e.outerWidth-s.right-s.left,d):ii(l,t.left+c.placed,r,u,d),c.start=r,c.placed+=u,r=l.bottom}else{const u=t.h*h,d=c.size||l.width;pt(c.start)&&(o=c.start),l.fullSize?ii(l,o,s.top,d,e.outerHeight-s.bottom-s.top):ii(l,o,t.top+c.placed,d,u),c.start=o,c.placed+=u,o=l.right}}t.x=o,t.y=r}var ht={addBox(n,t){n.boxes||(n.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},n.boxes.push(t)},removeBox(n,t){const e=n.boxes?n.boxes.indexOf(t):-1;e!==-1&&n.boxes.splice(e,1)},configure(n,t,e){t.fullSize=e.fullSize,t.position=e.position,t.weight=e.weight},update(n,t,e,i){if(!n)return;const s=tt(n.options.layout.padding),o=Math.max(t-s.width,0),r=Math.max(e-s.height,0),a=rf(n.boxes),l=a.vertical,c=a.horizontal;B(n.boxes,m=>{typeof m.beforeLayout=="function"&&m.beforeLayout()});const h=l.reduce((m,p)=>p.box.options&&p.box.options.display===!1?m:m+1,0)||1,u=Object.freeze({outerWidth:t,outerHeight:e,padding:s,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/h,hBoxMaxHeight:r/2}),d=Object.assign({},s);zl(d,tt(i));const f=Object.assign({maxPadding:d,w:o,h:r,x:s.left,y:s.top},s),g=of(l.concat(c),u);gn(a.fullSize,f,u,g),gn(l,f,u,g),gn(c,f,u,g)&&gn(l,f,u,g),lf(f),vr(a.leftAndTop,f,u,g),f.x+=f.w,f.y+=f.h,vr(a.rightAndBottom,f,u,g),n.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h,height:f.h,width:f.w},B(a.chartArea,m=>{const p=m.box;Object.assign(p,n.chartArea),p.update(f.w,f.h,{left:0,top:0,right:0,bottom:0})})}};class Wl{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,i){}removeEventListener(t,e,i){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,i,s){return e=Math.max(0,e||t.width),i=i||t.height,{width:e,height:Math.max(0,s?Math.floor(e/s):i)}}isAttached(t){return!0}updateConfig(t){}}class hf extends Wl{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const Si="$chartjs",uf={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},kr=n=>n===null||n==="";function df(n,t){const e=n.style,i=n.getAttribute("height"),s=n.getAttribute("width");if(n[Si]={initial:{height:i,width:s,style:{display:e.display,height:e.height,width:e.width}}},e.display=e.display||"block",e.boxSizing=e.boxSizing||"border-box",kr(s)){const o=rr(n,"width");o!==void 0&&(n.width=o)}if(kr(i))if(n.style.height==="")n.height=n.width/(t||2);else{const o=rr(n,"height");o!==void 0&&(n.height=o)}return n}const Vl=fd?{passive:!0}:!1;function ff(n,t,e){n&&n.addEventListener(t,e,Vl)}function gf(n,t,e){n&&n.canvas&&n.canvas.removeEventListener(t,e,Vl)}function mf(n,t){const e=uf[n.type]||n.type,{x:i,y:s}=be(n,t);return{type:e,chart:t,native:n,x:i!==void 0?i:null,y:s!==void 0?s:null}}function Ri(n,t){for(const e of n)if(e===t||e.contains(t))return!0}function pf(n,t,e){const i=n.canvas,s=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||Ri(a.addedNodes,i),r=r&&!Ri(a.removedNodes,i);r&&e()});return s.observe(document,{childList:!0,subtree:!0}),s}function yf(n,t,e){const i=n.canvas,s=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||Ri(a.removedNodes,i),r=r&&!Ri(a.addedNodes,i);r&&e()});return s.observe(document,{childList:!0,subtree:!0}),s}const In=new Map;let Sr=0;function Bl(){const n=window.devicePixelRatio;n!==Sr&&(Sr=n,In.forEach((t,e)=>{e.currentDevicePixelRatio!==n&&t()}))}function bf(n,t){In.size||window.addEventListener("resize",Bl),In.set(n,t)}function xf(n){In.delete(n),In.size||window.removeEventListener("resize",Bl)}function _f(n,t,e){const i=n.canvas,s=i&&ko(i);if(!s)return;const o=pl((a,l)=>{const c=s.clientWidth;e(a,l),c<s.clientWidth&&e()},window),r=new ResizeObserver(a=>{const l=a[0],c=l.contentRect.width,h=l.contentRect.height;c===0&&h===0||o(c,h)});return r.observe(s),bf(n,o),r}function gs(n,t,e){e&&e.disconnect(),t==="resize"&&xf(n)}function wf(n,t,e){const i=n.canvas,s=pl(o=>{n.ctx!==null&&e(mf(o,n))},n);return ff(i,t,s),s}class vf extends Wl{acquireContext(t,e){const i=t&&t.getContext&&t.getContext("2d");return i&&i.canvas===t?(df(t,e),i):null}releaseContext(t){const e=t.canvas;if(!e[Si])return!1;const i=e[Si].initial;["height","width"].forEach(o=>{const r=i[o];I(r)?e.removeAttribute(o):e.setAttribute(o,r)});const s=i.style||{};return Object.keys(s).forEach(o=>{e.style[o]=s[o]}),e.width=e.width,delete e[Si],!0}addEventListener(t,e,i){this.removeEventListener(t,e);const s=t.$proxies||(t.$proxies={}),r={attach:pf,detach:yf,resize:_f}[e]||wf;s[e]=r(t,e,i)}removeEventListener(t,e){const i=t.$proxies||(t.$proxies={}),s=i[e];if(!s)return;({attach:gs,detach:gs,resize:gs}[e]||gf)(t,e,s),i[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,i,s){return dd(t,e,i,s)}isAttached(t){const e=t&&ko(t);return!!(e&&e.isConnected)}}function kf(n){return!vo()||typeof OffscreenCanvas<"u"&&n instanceof OffscreenCanvas?hf:vf}class st{constructor(){v(this,"x");v(this,"y");v(this,"active",!1);v(this,"options");v(this,"$animations")}tooltipPosition(t){const{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}hasValue(){return le(this.x)&&le(this.y)}getProps(t,e){const i=this.$animations;if(!e||!i)return this;const s={};return t.forEach(o=>{s[o]=i[o]&&i[o].active()?i[o]._to:this[o]}),s}}v(st,"defaults",{}),v(st,"defaultRoutes");function Sf(n,t){const e=n.options.ticks,i=Mf(n),s=Math.min(e.maxTicksLimit||i,i),o=e.major.enabled?Of(t):[],r=o.length,a=o[0],l=o[r-1],c=[];if(r>s)return Df(t,c,o,r/s),c;const h=Tf(o,t,s);if(r>0){let u,d;const f=r>1?Math.round((l-a)/(r-1)):null;for(si(t,c,h,I(f)?0:a-f,a),u=0,d=r-1;u<d;u++)si(t,c,h,o[u],o[u+1]);return si(t,c,h,l,I(f)?t.length:l+f),c}return si(t,c,h),c}function Mf(n){const t=n.options.offset,e=n._tickSize(),i=n._length/e+(t?0:1),s=n._maxLength/e;return Math.floor(Math.min(i,s))}function Tf(n,t,e){const i=Cf(n),s=t.length/e;if(!i)return Math.max(s,1);const o=mu(i);for(let r=0,a=o.length-1;r<a;r++){const l=o[r];if(l>s)return l}return Math.max(s,1)}function Of(n){const t=[];let e,i;for(e=0,i=n.length;e<i;e++)n[e].major&&t.push(e);return t}function Df(n,t,e,i){let s=0,o=e[0],r;for(i=Math.ceil(i),r=0;r<n.length;r++)r===o&&(t.push(n[r]),s++,o=e[s*i])}function si(n,t,e,i,s){const o=C(i,0),r=Math.min(C(s,n.length),n.length);let a=0,l,c,h;for(e=Math.ceil(e),s&&(l=s-i,e=l/Math.floor(l/e)),h=o;h<0;)a++,h=Math.round(o+a*e);for(c=Math.max(o,0);c<r;c++)c===h&&(t.push(n[c]),a++,h=Math.round(o+a*e))}function Cf(n){const t=n.length;let e,i;if(t<2)return!1;for(i=n[0],e=1;e<t;++e)if(n[e]-n[e-1]!==i)return!1;return i}const Pf=n=>n==="left"?"right":n==="right"?"left":n,Mr=(n,t,e)=>t==="top"||t==="left"?n[t]+e:n[t]-e,Tr=(n,t)=>Math.min(t||n,n);function Or(n,t){const e=[],i=n.length/t,s=n.length;let o=0;for(;o<s;o+=i)e.push(n[Math.floor(o)]);return e}function Af(n,t,e){const i=n.ticks.length,s=Math.min(t,i-1),o=n._startPixel,r=n._endPixel,a=1e-6;let l=n.getPixelForTick(s),c;if(!(e&&(i===1?c=Math.max(l-o,r-l):t===0?c=(n.getPixelForTick(1)-l)/2:c=(l-n.getPixelForTick(s-1))/2,l+=s<t?c:-c,l<o-a||l>r+a)))return l}function Ef(n,t){B(n,e=>{const i=e.gc,s=i.length/2;let o;if(s>t){for(o=0;o<s;++o)delete e.data[i[o]];i.splice(0,s)}})}function an(n){return n.drawTicks?n.tickLength:0}function Dr(n,t){if(!n.display)return 0;const e=G(n.font,t),i=tt(n.padding);return(V(n.text)?n.text.length:1)*e.lineHeight+i.height}function If(n,t){return he(n,{scale:t,type:"scale"})}function Lf(n,t,e){return he(n,{tick:e,index:t,type:"tick"})}function Rf(n,t,e){let i=po(n);return(e&&t!=="right"||!e&&t==="right")&&(i=Pf(i)),i}function Ff(n,t,e,i){const{top:s,left:o,bottom:r,right:a,chart:l}=n,{chartArea:c,scales:h}=l;let u=0,d,f,g;const m=r-s,p=a-o;if(n.isHorizontal()){if(f=at(i,o,a),E(e)){const y=Object.keys(e)[0],b=e[y];g=h[y].getPixelForValue(b)+m-t}else e==="center"?g=(c.bottom+c.top)/2+m-t:g=Mr(n,e,t);d=a-o}else{if(E(e)){const y=Object.keys(e)[0],b=e[y];f=h[y].getPixelForValue(b)-p+t}else e==="center"?f=(c.left+c.right)/2-p+t:f=Mr(n,e,t);g=at(i,r,s),u=e==="left"?-X:X}return{titleX:f,titleY:g,maxWidth:d,rotation:u}}class Te extends st{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:i,_suggestedMax:s}=this;return t=bt(t,Number.POSITIVE_INFINITY),e=bt(e,Number.NEGATIVE_INFINITY),i=bt(i,Number.POSITIVE_INFINITY),s=bt(s,Number.NEGATIVE_INFINITY),{min:bt(t,i),max:bt(e,s),minDefined:q(t),maxDefined:q(e)}}getMinMax(t){let{min:e,max:i,minDefined:s,maxDefined:o}=this.getUserBounds(),r;if(s&&o)return{min:e,max:i};const a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)r=a[l].controller.getMinMax(this,t),s||(e=Math.min(e,r.min)),o||(i=Math.max(i,r.max));return e=o&&e>i?i:e,i=s&&e>i?e:i,{min:bt(e,bt(i,e)),max:bt(i,bt(e,i))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){W(this.options.beforeUpdate,[this])}update(t,e,i){const{beginAtZero:s,grace:o,ticks:r}=this.options,a=r.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=ju(this,o,s),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?Or(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||r.source==="auto")&&(this.ticks=Sf(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,e,i;this.isHorizontal()?(e=this.left,i=this.right):(e=this.top,i=this.bottom,t=!t),this._startPixel=e,this._endPixel=i,this._reversePixels=t,this._length=i-e,this._alignToPixels=this.options.alignToPixels}afterUpdate(){W(this.options.afterUpdate,[this])}beforeSetDimensions(){W(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){W(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),W(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){W(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let i,s,o;for(i=0,s=t.length;i<s;i++)o=t[i],o.label=W(e.callback,[o.value,i,t],this)}afterTickToLabelConversion(){W(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){W(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,e=t.ticks,i=Tr(this.ticks.length,t.ticks.maxTicksLimit),s=e.minRotation||0,o=e.maxRotation;let r=s,a,l,c;if(!this._isVisible()||!e.display||s>=o||i<=1||!this.isHorizontal()){this.labelRotation=s;return}const h=this._getLabelSizes(),u=h.widest.width,d=h.highest.height,f=it(this.chart.width-u,0,this.maxWidth);a=t.offset?this.maxWidth/i:f/(i-1),u+6>a&&(a=f/(i-(t.offset?.5:1)),l=this.maxHeight-an(t.grid)-e.padding-Dr(t.title,this.chart.options.font),c=Math.sqrt(u*u+d*d),r=$i(Math.min(Math.asin(it((h.highest.height+6)/a,-1,1)),Math.asin(it(l/c,-1,1))-Math.asin(it(d/c,-1,1)))),r=Math.max(s,Math.min(o,r))),this.labelRotation=r}afterCalculateLabelRotation(){W(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){W(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:e,options:{ticks:i,title:s,grid:o}}=this,r=this._isVisible(),a=this.isHorizontal();if(r){const l=Dr(s,e.options.font);if(a?(t.width=this.maxWidth,t.height=an(o)+l):(t.height=this.maxHeight,t.width=an(o)+l),i.display&&this.ticks.length){const{first:c,last:h,widest:u,highest:d}=this._getLabelSizes(),f=i.padding*2,g=nt(this.labelRotation),m=Math.cos(g),p=Math.sin(g);if(a){const y=i.mirror?0:p*u.width+m*d.height;t.height=Math.min(this.maxHeight,t.height+y+f)}else{const y=i.mirror?0:m*u.width+p*d.height;t.width=Math.min(this.maxWidth,t.width+y+f)}this._calculatePadding(c,h,p,m)}}this._handleMargins(),a?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,i,s){const{ticks:{align:o,padding:r},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const h=this.getPixelForTick(0)-this.left,u=this.right-this.getPixelForTick(this.ticks.length-1);let d=0,f=0;l?c?(d=s*t.width,f=i*e.height):(d=i*t.height,f=s*e.width):o==="start"?f=e.width:o==="end"?d=t.width:o!=="inner"&&(d=t.width/2,f=e.width/2),this.paddingLeft=Math.max((d-h+r)*this.width/(this.width-h),0),this.paddingRight=Math.max((f-u+r)*this.width/(this.width-u),0)}else{let h=e.height/2,u=t.height/2;o==="start"?(h=0,u=t.height):o==="end"&&(h=e.height,u=0),this.paddingTop=h+r,this.paddingBottom=u+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){W(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return e==="top"||e==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let e,i;for(e=0,i=t.length;e<i;e++)I(t[e].label)&&(t.splice(e,1),i--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let i=this.ticks;e<i.length&&(i=Or(i,e)),this._labelSizes=t=this._computeLabelSizes(i,i.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,i){const{ctx:s,_longestTextCache:o}=this,r=[],a=[],l=Math.floor(e/Tr(e,i));let c=0,h=0,u,d,f,g,m,p,y,b,_,w,x;for(u=0;u<e;u+=l){if(g=t[u].label,m=this._resolveTickFontOptions(u),s.font=p=m.string,y=o[p]=o[p]||{data:{},gc:[]},b=m.lineHeight,_=w=0,!I(g)&&!V(g))_=Ii(s,y.data,y.gc,_,g),w=b;else if(V(g))for(d=0,f=g.length;d<f;++d)x=g[d],!I(x)&&!V(x)&&(_=Ii(s,y.data,y.gc,_,x),w+=b);r.push(_),a.push(w),c=Math.max(_,c),h=Math.max(w,h)}Ef(o,e);const k=r.indexOf(c),S=a.indexOf(h),M=O=>({width:r[O]||0,height:a[O]||0});return{first:M(0),last:M(e-1),widest:M(k),highest:M(S),widths:r,heights:a}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return xu(this._alignToPixels?fe(this.chart,e,0):e)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const i=e[t];return i.$context||(i.$context=Lf(this.getContext(),t,i))}return this.$context||(this.$context=If(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,e=nt(this.labelRotation),i=Math.abs(Math.cos(e)),s=Math.abs(Math.sin(e)),o=this._getLabelSizes(),r=t.autoSkipPadding||0,a=o?o.widest.width+r:0,l=o?o.highest.height+r:0;return this.isHorizontal()?l*i>a*s?a/i:l/s:l*s<a*i?l/i:a/s}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis,i=this.chart,s=this.options,{grid:o,position:r,border:a}=s,l=o.offset,c=this.isHorizontal(),u=this.ticks.length+(l?1:0),d=an(o),f=[],g=a.setContext(this.getContext()),m=g.display?g.width:0,p=m/2,y=function(Z){return fe(i,Z,m)};let b,_,w,x,k,S,M,O,A,L,F,ot;if(r==="top")b=y(this.bottom),S=this.bottom-d,O=b-p,L=y(t.top)+p,ot=t.bottom;else if(r==="bottom")b=y(this.top),L=t.top,ot=y(t.bottom)-p,S=b+p,O=this.top+d;else if(r==="left")b=y(this.right),k=this.right-d,M=b-p,A=y(t.left)+p,F=t.right;else if(r==="right")b=y(this.left),A=t.left,F=y(t.right)-p,k=b+p,M=this.left+d;else if(e==="x"){if(r==="center")b=y((t.top+t.bottom)/2+.5);else if(E(r)){const Z=Object.keys(r)[0],Q=r[Z];b=y(this.chart.scales[Z].getPixelForValue(Q))}L=t.top,ot=t.bottom,S=b+p,O=S+d}else if(e==="y"){if(r==="center")b=y((t.left+t.right)/2);else if(E(r)){const Z=Object.keys(r)[0],Q=r[Z];b=y(this.chart.scales[Z].getPixelForValue(Q))}k=b-p,M=k-d,A=t.left,F=t.right}const yt=C(s.ticks.maxTicksLimit,u),$=Math.max(1,Math.ceil(u/yt));for(_=0;_<u;_+=$){const Z=this.getContext(_),Q=o.setContext(Z),Ot=a.setContext(Z),rt=Q.lineWidth,Pe=Q.color,qn=Ot.dash||[],Ae=Ot.dashOffset,en=Q.tickWidth,ue=Q.tickColor,nn=Q.tickBorderDash||[],de=Q.tickBorderDashOffset;w=Af(this,_,l),w!==void 0&&(x=fe(i,w,rt),c?k=M=A=F=x:S=O=L=ot=x,f.push({tx1:k,ty1:S,tx2:M,ty2:O,x1:A,y1:L,x2:F,y2:ot,width:rt,color:Pe,borderDash:qn,borderDashOffset:Ae,tickWidth:en,tickColor:ue,tickBorderDash:nn,tickBorderDashOffset:de}))}return this._ticksLength=u,this._borderValue=b,f}_computeLabelItems(t){const e=this.axis,i=this.options,{position:s,ticks:o}=i,r=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:h,mirror:u}=o,d=an(i.grid),f=d+h,g=u?-h:f,m=-nt(this.labelRotation),p=[];let y,b,_,w,x,k,S,M,O,A,L,F,ot="middle";if(s==="top")k=this.bottom-g,S=this._getXAxisLabelAlignment();else if(s==="bottom")k=this.top+g,S=this._getXAxisLabelAlignment();else if(s==="left"){const $=this._getYAxisLabelAlignment(d);S=$.textAlign,x=$.x}else if(s==="right"){const $=this._getYAxisLabelAlignment(d);S=$.textAlign,x=$.x}else if(e==="x"){if(s==="center")k=(t.top+t.bottom)/2+f;else if(E(s)){const $=Object.keys(s)[0],Z=s[$];k=this.chart.scales[$].getPixelForValue(Z)+f}S=this._getXAxisLabelAlignment()}else if(e==="y"){if(s==="center")x=(t.left+t.right)/2-f;else if(E(s)){const $=Object.keys(s)[0],Z=s[$];x=this.chart.scales[$].getPixelForValue(Z)}S=this._getYAxisLabelAlignment(d).textAlign}e==="y"&&(l==="start"?ot="top":l==="end"&&(ot="bottom"));const yt=this._getLabelSizes();for(y=0,b=a.length;y<b;++y){_=a[y],w=_.label;const $=o.setContext(this.getContext(y));M=this.getPixelForTick(y)+o.labelOffset,O=this._resolveTickFontOptions(y),A=O.lineHeight,L=V(w)?w.length:1;const Z=L/2,Q=$.color,Ot=$.textStrokeColor,rt=$.textStrokeWidth;let Pe=S;r?(x=M,S==="inner"&&(y===b-1?Pe=this.options.reverse?"left":"right":y===0?Pe=this.options.reverse?"right":"left":Pe="center"),s==="top"?c==="near"||m!==0?F=-L*A+A/2:c==="center"?F=-yt.highest.height/2-Z*A+A:F=-yt.highest.height+A/2:c==="near"||m!==0?F=A/2:c==="center"?F=yt.highest.height/2-Z*A:F=yt.highest.height-L*A,u&&(F*=-1),m!==0&&!$.showLabelBackdrop&&(x+=A/2*Math.sin(m))):(k=M,F=(1-L)*A/2);let qn;if($.showLabelBackdrop){const Ae=tt($.backdropPadding),en=yt.heights[y],ue=yt.widths[y];let nn=F-Ae.top,de=0-Ae.left;switch(ot){case"middle":nn-=en/2;break;case"bottom":nn-=en;break}switch(S){case"center":de-=ue/2;break;case"right":de-=ue;break;case"inner":y===b-1?de-=ue:y>0&&(de-=ue/2);break}qn={left:de,top:nn,width:ue+Ae.width,height:en+Ae.height,color:$.backdropColor}}p.push({label:w,font:O,textOffset:F,options:{rotation:m,color:Q,strokeColor:Ot,strokeWidth:rt,textAlign:Pe,textBaseline:ot,translation:[x,k],backdrop:qn}})}return p}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;if(-nt(this.labelRotation))return t==="top"?"left":"right";let s="center";return e.align==="start"?s="left":e.align==="end"?s="right":e.align==="inner"&&(s="inner"),s}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:i,mirror:s,padding:o}}=this.options,r=this._getLabelSizes(),a=t+o,l=r.widest.width;let c,h;return e==="left"?s?(h=this.right+o,i==="near"?c="left":i==="center"?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-a,i==="near"?c="right":i==="center"?(c="center",h-=l/2):(c="left",h=this.left)):e==="right"?s?(h=this.left+o,i==="near"?c="right":i==="center"?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+a,i==="near"?c="left":i==="center"?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,e=this.options.position;if(e==="left"||e==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(e==="top"||e==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:i,top:s,width:o,height:r}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(i,s,o,r),t.restore())}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const s=this.ticks.findIndex(o=>o.value===t);return s>=0?e.setContext(this.getContext(s)).lineWidth:0}drawGrid(t){const e=this.options.grid,i=this.ctx,s=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let o,r;const a=(l,c,h)=>{!h.width||!h.color||(i.save(),i.lineWidth=h.width,i.strokeStyle=h.color,i.setLineDash(h.borderDash||[]),i.lineDashOffset=h.borderDashOffset,i.beginPath(),i.moveTo(l.x,l.y),i.lineTo(c.x,c.y),i.stroke(),i.restore())};if(e.display)for(o=0,r=s.length;o<r;++o){const l=s[o];e.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),e.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{border:i,grid:s}}=this,o=i.setContext(this.getContext()),r=i.display?o.width:0;if(!r)return;const a=s.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,h,u,d;this.isHorizontal()?(c=fe(t,this.left,r)-r/2,h=fe(t,this.right,a)+a/2,u=d=l):(u=fe(t,this.top,r)-r/2,d=fe(t,this.bottom,a)+a/2,c=h=l),e.save(),e.lineWidth=o.width,e.strokeStyle=o.color,e.beginPath(),e.moveTo(c,u),e.lineTo(h,d),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;const i=this.ctx,s=this._computeLabelArea();s&&Wn(i,s);const o=this.getLabelItems(t);for(const r of o){const a=r.options,l=r.font,c=r.label,h=r.textOffset;Se(i,c,0,h,l,a)}s&&Vn(i)}drawTitle(){const{ctx:t,options:{position:e,title:i,reverse:s}}=this;if(!i.display)return;const o=G(i.font),r=tt(i.padding),a=i.align;let l=o.lineHeight/2;e==="bottom"||e==="center"||E(e)?(l+=r.bottom,V(i.text)&&(l+=o.lineHeight*(i.text.length-1))):l+=r.top;const{titleX:c,titleY:h,maxWidth:u,rotation:d}=Ff(this,l,e,a);Se(t,i.text,0,0,o,{color:i.color,maxWidth:u,rotation:d,textAlign:Rf(a,e,s),textBaseline:"middle",translation:[c,h]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,e=t.ticks&&t.ticks.z||0,i=C(t.grid&&t.grid.z,-1),s=C(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==Te.prototype.draw?[{z:e,draw:o=>{this.draw(o)}}]:[{z:i,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:s,draw:()=>{this.drawBorder()}},{z:e,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas(),i=this.axis+"AxisID",s=[];let o,r;for(o=0,r=e.length;o<r;++o){const a=e[o];a[i]===this.id&&(!t||a.type===t)&&s.push(a)}return s}_resolveTickFontOptions(t){const e=this.options.ticks.setContext(this.getContext(t));return G(e.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class oi{constructor(t,e,i){this.type=t,this.scope=e,this.override=i,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let i;Wf(e)&&(i=this.register(e));const s=this.items,o=t.id,r=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in s||(s[o]=t,Nf(t,r,i),this.override&&Y.override(t.id,t.overrides)),r}get(t){return this.items[t]}unregister(t){const e=this.items,i=t.id,s=this.scope;i in e&&delete e[i],s&&i in Y[s]&&(delete Y[s][i],this.override&&delete ke[i])}}function Nf(n,t,e){const i=Ft(Object.create(null),[e?Y.get(e):{},Y.get(t),n.defaults]);Y.set(t,i),n.defaultRoutes&&zf(t,n.defaultRoutes),n.descriptors&&Y.describe(t,n.descriptors)}function zf(n,t){Object.keys(t).forEach(e=>{const i=e.split("."),s=i.pop(),o=[n].concat(i).join("."),r=t[e].split("."),a=r.pop(),l=r.join(".");Y.route(o,s,l,a)})}function Wf(n){return"id"in n&&"defaults"in n}class Vf{constructor(){this.controllers=new oi(At,"datasets",!0),this.elements=new oi(st,"elements"),this.plugins=new oi(Object,"plugins"),this.scales=new oi(Te,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,i){[...e].forEach(s=>{const o=i||this._getRegistryForType(s);i||o.isForType(s)||o===this.plugins&&s.id?this._exec(t,o,s):B(s,r=>{const a=i||this._getRegistryForType(r);this._exec(t,a,r)})})}_exec(t,e,i){const s=fo(t);W(i["before"+s],[],i),e[t](i),W(i["after"+s],[],i)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const i=this._typedRegistries[e];if(i.isForType(t))return i}return this.plugins}_get(t,e,i){const s=e.get(t);if(s===void 0)throw new Error('"'+t+'" is not a registered '+i+".");return s}}var Lt=new Vf;class Bf{constructor(){this._init=[]}notify(t,e,i,s){e==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const o=s?this._descriptors(t).filter(s):this._descriptors(t),r=this._notify(o,t,e,i);return e==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),r}_notify(t,e,i,s){s=s||{};for(const o of t){const r=o.plugin,a=r[i],l=[e,s,o.options];if(W(a,l,r)===!1&&s.cancelable)return!1}return!0}invalidate(){I(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){const i=t&&t.config,s=C(i.options&&i.options.plugins,{}),o=Hf(i);return s===!1&&!e?[]:jf(t,o,s,e)}_notifyStateChanges(t){const e=this._oldCache||[],i=this._cache,s=(o,r)=>o.filter(a=>!r.some(l=>a.plugin.id===l.plugin.id));this._notify(s(e,i),t,"stop"),this._notify(s(i,e),t,"start")}}function Hf(n){const t={},e=[],i=Object.keys(Lt.plugins.items);for(let o=0;o<i.length;o++)e.push(Lt.getPlugin(i[o]));const s=n.plugins||[];for(let o=0;o<s.length;o++){const r=s[o];e.indexOf(r)===-1&&(e.push(r),t[r.id]=!0)}return{plugins:e,localIds:t}}function $f(n,t){return!t&&n===!1?null:n===!0?{}:n}function jf(n,{plugins:t,localIds:e},i,s){const o=[],r=n.getContext();for(const a of t){const l=a.id,c=$f(i[l],s);c!==null&&o.push({plugin:a,options:Yf(n.config,{plugin:a,local:e[l]},c,r)})}return o}function Yf(n,{plugin:t,local:e},i,s){const o=n.pluginScopeKeys(t),r=n.getOptionScopes(i,o);return e&&t.defaults&&r.push(t.defaults),n.createResolver(r,s,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Is(n,t){const e=Y.datasets[n]||{};return((t.datasets||{})[n]||{}).indexAxis||t.indexAxis||e.indexAxis||"x"}function Uf(n,t){let e=n;return n==="_index_"?e=t:n==="_value_"&&(e=t==="x"?"y":"x"),e}function Xf(n,t){return n===t?"_index_":"_value_"}function Cr(n){if(n==="x"||n==="y"||n==="r")return n}function Zf(n){if(n==="top"||n==="bottom")return"x";if(n==="left"||n==="right")return"y"}function Ls(n,...t){if(Cr(n))return n;for(const e of t){const i=e.axis||Zf(e.position)||n.length>1&&Cr(n[0].toLowerCase());if(i)return i}throw new Error(`Cannot determine type of '${n}' axis. Please provide 'axis' or 'position' option.`)}function Pr(n,t,e){if(e[t+"AxisID"]===n)return{axis:t}}function qf(n,t){if(t.data&&t.data.datasets){const e=t.data.datasets.filter(i=>i.xAxisID===n||i.yAxisID===n);if(e.length)return Pr(n,"x",e[0])||Pr(n,"y",e[0])}return{}}function Gf(n,t){const e=ke[n.type]||{scales:{}},i=t.scales||{},s=Is(n.type,t),o=Object.create(null);return Object.keys(i).forEach(r=>{const a=i[r];if(!E(a))return console.error(`Invalid scale configuration for scale: ${r}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${r}`);const l=Ls(r,a,qf(r,n),Y.scales[a.type]),c=Xf(l,s),h=e.scales||{};o[r]=wn(Object.create(null),[{axis:l},a,h[l],h[c]])}),n.data.datasets.forEach(r=>{const a=r.type||n.type,l=r.indexAxis||Is(a,t),h=(ke[a]||{}).scales||{};Object.keys(h).forEach(u=>{const d=Uf(u,l),f=r[d+"AxisID"]||d;o[f]=o[f]||Object.create(null),wn(o[f],[{axis:d},i[f],h[u]])})}),Object.keys(o).forEach(r=>{const a=o[r];wn(a,[Y.scales[a.type],Y.scale])}),o}function Hl(n){const t=n.options||(n.options={});t.plugins=C(t.plugins,{}),t.scales=Gf(n,t)}function $l(n){return n=n||{},n.datasets=n.datasets||[],n.labels=n.labels||[],n}function Jf(n){return n=n||{},n.data=$l(n.data),Hl(n),n}const Ar=new Map,jl=new Set;function ri(n,t){let e=Ar.get(n);return e||(e=t(),Ar.set(n,e),jl.add(e)),e}const ln=(n,t,e)=>{const i=ae(t,e);i!==void 0&&n.add(i)};class Kf{constructor(t){this._config=Jf(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=$l(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),Hl(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return ri(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return ri(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return ri(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){const e=t.id,i=this.type;return ri(`${i}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){const i=this._scopeCache;let s=i.get(t);return(!s||e)&&(s=new Map,i.set(t,s)),s}getOptionScopes(t,e,i){const{options:s,type:o}=this,r=this._cachedScopes(t,i),a=r.get(e);if(a)return a;const l=new Set;e.forEach(h=>{t&&(l.add(t),h.forEach(u=>ln(l,t,u))),h.forEach(u=>ln(l,s,u)),h.forEach(u=>ln(l,ke[o]||{},u)),h.forEach(u=>ln(l,Y,u)),h.forEach(u=>ln(l,Ps,u))});const c=Array.from(l);return c.length===0&&c.push(Object.create(null)),jl.has(e)&&r.set(e,c),c}chartOptionScopes(){const{options:t,type:e}=this;return[t,ke[e]||{},Y.datasets[e]||{},{type:e},Y,Ps]}resolveNamedOptions(t,e,i,s=[""]){const o={$shared:!0},{resolver:r,subPrefixes:a}=Er(this._resolverCache,t,s);let l=r;if(tg(r,e)){o.$shared=!1,i=mt(i)?i():i;const c=this.createResolver(t,i,a);l=je(r,i,c)}for(const c of e)o[c]=l[c];return o}createResolver(t,e,i=[""],s){const{resolver:o}=Er(this._resolverCache,t,i);return E(e)?je(o,e,void 0,s):o}}function Er(n,t,e){let i=n.get(t);i||(i=new Map,n.set(t,i));const s=e.join();let o=i.get(s);return o||(o={resolver:xo(t,e),subPrefixes:e.filter(a=>!a.toLowerCase().includes("hover"))},i.set(s,o)),o}const Qf=n=>E(n)&&Object.getOwnPropertyNames(n).some(t=>mt(n[t]));function tg(n,t){const{isScriptable:e,isIndexable:i}=vl(n);for(const s of t){const o=e(s),r=i(s),a=(r||o)&&n[s];if(o&&(mt(a)||Qf(a))||r&&V(a))return!0}return!1}var eg="4.4.9";const ng=["top","bottom","left","right","chartArea"];function Ir(n,t){return n==="top"||n==="bottom"||ng.indexOf(n)===-1&&t==="x"}function Lr(n,t){return function(e,i){return e[n]===i[n]?e[t]-i[t]:e[n]-i[n]}}function Rr(n){const t=n.chart,e=t.options.animation;t.notifyPlugins("afterRender"),W(e&&e.onComplete,[n],t)}function ig(n){const t=n.chart,e=t.options.animation;W(e&&e.onProgress,[n],t)}function Yl(n){return vo()&&typeof n=="string"?n=document.getElementById(n):n&&n.length&&(n=n[0]),n&&n.canvas&&(n=n.canvas),n}const Mi={},Fr=n=>{const t=Yl(n);return Object.values(Mi).filter(e=>e.canvas===t).pop()};function sg(n,t,e){const i=Object.keys(n);for(const s of i){const o=+s;if(o>=t){const r=n[s];delete n[s],(e>0||o>t)&&(n[o+e]=r)}}}function og(n,t,e,i){return!e||n.type==="mouseout"?null:i?t:n}class St{static register(...t){Lt.add(...t),Nr()}static unregister(...t){Lt.remove(...t),Nr()}constructor(t,e){const i=this.config=new Kf(e),s=Yl(t),o=Fr(s);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");const r=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||kf(s)),this.platform.updateConfig(i);const a=this.platform.acquireContext(s,r.aspectRatio),l=a&&a.canvas,c=l&&l.height,h=l&&l.width;if(this.id=au(),this.ctx=a,this.canvas=l,this.width=h,this.height=c,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new Bf,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=ku(u=>this.update(u),r.resizeDelay||0),this._dataChanges=[],Mi[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}zt.listen(this,"complete",Rr),zt.listen(this,"progress",ig),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:i,height:s,_aspectRatio:o}=this;return I(t)?e&&o?o:s?i/s:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return Lt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():or(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return nr(this.canvas,this.ctx),this}stop(){return zt.stop(this),this}resize(t,e){zt.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const i=this.options,s=this.canvas,o=i.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(s,t,e,o),a=i.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,or(this,a,!0)&&(this.notifyPlugins("resize",{size:r}),W(i.onResize,[this,r],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const e=this.options.scales||{};B(e,(i,s)=>{i.id=s})}buildOrUpdateScales(){const t=this.options,e=t.scales,i=this.scales,s=Object.keys(i).reduce((r,a)=>(r[a]=!1,r),{});let o=[];e&&(o=o.concat(Object.keys(e).map(r=>{const a=e[r],l=Ls(r,a),c=l==="r",h=l==="x";return{options:a,dposition:c?"chartArea":h?"bottom":"left",dtype:c?"radialLinear":h?"category":"linear"}}))),B(o,r=>{const a=r.options,l=a.id,c=Ls(l,a),h=C(a.type,r.dtype);(a.position===void 0||Ir(a.position,c)!==Ir(r.dposition))&&(a.position=r.dposition),s[l]=!0;let u=null;if(l in i&&i[l].type===h)u=i[l];else{const d=Lt.getScale(h);u=new d({id:l,type:h,ctx:this.ctx,chart:this}),i[u.id]=u}u.init(a,t)}),B(s,(r,a)=>{r||delete i[a]}),B(i,r=>{ht.configure(this,r,r.options),ht.addBox(this,r)})}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,i=t.length;if(t.sort((s,o)=>s.index-o.index),i>e){for(let s=e;s<i;++s)this._destroyDatasetMeta(s);t.splice(e,i-e)}this._sortedMetasets=t.slice(0).sort(Lr("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((i,s)=>{e.filter(o=>o===i._dataset).length===0&&this._destroyDatasetMeta(s)})}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let i,s;for(this._removeUnreferencedMetasets(),i=0,s=e.length;i<s;i++){const o=e[i];let r=this.getDatasetMeta(i);const a=o.type||this.config.type;if(r.type&&r.type!==a&&(this._destroyDatasetMeta(i),r=this.getDatasetMeta(i)),r.type=a,r.indexAxis=o.indexAxis||Is(a,this.options),r.order=o.order||0,r.index=i,r.label=""+o.label,r.visible=this.isDatasetVisible(i),r.controller)r.controller.updateIndex(i),r.controller.linkScales();else{const l=Lt.getController(a),{datasetElementType:c,dataElementType:h}=Y.datasets[a];Object.assign(l,{dataElementType:Lt.getElement(h),datasetElementType:c&&Lt.getElement(c)}),r.controller=new l(this,i),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){B(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const i=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),s=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let c=0,h=this.data.datasets.length;c<h;c++){const{controller:u}=this.getDatasetMeta(c),d=!s&&o.indexOf(u)===-1;u.buildOrUpdateElements(d),r=Math.max(+u.getMaxOverflow(),r)}r=this._minPadding=i.layout.autoPadding?r:0,this._updateLayout(r),s||B(o,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(Lr("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){B(this.scales,t=>{ht.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),i=new Set(t.events);(!Zo(e,i)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:i,start:s,count:o}of e){const r=i==="_removeElements"?-o:o;sg(t,s,r)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,i=o=>new Set(t.filter(r=>r[0]===o).map((r,a)=>a+","+r.splice(1).join(","))),s=i(0);for(let o=1;o<e;o++)if(!Zo(s,i(o)))return;return Array.from(s).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;ht.update(this,this.width,this.height,t);const e=this.chartArea,i=e.width<=0||e.height<=0;this._layers=[],B(this.boxes,s=>{i&&s.position==="chartArea"||(s.configure&&s.configure(),this._layers.push(...s._layers()))},this),this._layers.forEach((s,o)=>{s._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let e=0,i=this.data.datasets.length;e<i;++e)this.getDatasetMeta(e).controller.configure();for(let e=0,i=this.data.datasets.length;e<i;++e)this._updateDataset(e,mt(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const i=this.getDatasetMeta(t),s={meta:i,index:t,mode:e,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",s)!==!1&&(i.controller._update(e),s.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",s))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(zt.has(this)?this.attached&&!zt.running(this)&&zt.start(this):(this.draw(),Rr({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:i,height:s}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(i,s)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,i=[];let s,o;for(s=0,o=e.length;s<o;++s){const r=e[s];(!t||r.visible)&&i.push(r)}return i}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,i={meta:t,index:t.index,cancelable:!0},s=Il(this,t);this.notifyPlugins("beforeDatasetDraw",i)!==!1&&(s&&Wn(e,s),t.controller.draw(),s&&Vn(e),i.cancelable=!1,this.notifyPlugins("afterDatasetDraw",i))}isPointInArea(t){return $t(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,i,s){const o=ef.modes[e];return typeof o=="function"?o(this,t,i,s):[]}getDatasetMeta(t){const e=this.data.datasets[t],i=this._metasets;let s=i.filter(o=>o&&o._dataset===e).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},i.push(s)),s}getContext(){return this.$context||(this.$context=he(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const i=this.getDatasetMeta(t);return typeof i.hidden=="boolean"?!i.hidden:!e.hidden}setDatasetVisibility(t,e){const i=this.getDatasetMeta(t);i.hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,i){const s=i?"show":"hide",o=this.getDatasetMeta(t),r=o.controller._resolveAnimations(void 0,s);pt(e)?(o.data[e].hidden=!i,this.update()):(this.setDatasetVisibility(t,i),r.update(o,{visible:i}),this.update(a=>a.datasetIndex===t?s:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),zt.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),nr(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete Mi[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,i=(o,r)=>{e.addEventListener(this,o,r),t[o]=r},s=(o,r,a)=>{o.offsetX=r,o.offsetY=a,this._eventHandler(o)};B(this.options.events,o=>i(o,s))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,i=(l,c)=>{e.addEventListener(this,l,c),t[l]=c},s=(l,c)=>{t[l]&&(e.removeEventListener(this,l,c),delete t[l])},o=(l,c)=>{this.canvas&&this.resize(l,c)};let r;const a=()=>{s("attach",a),this.attached=!0,this.resize(),i("resize",o),i("detach",r)};r=()=>{this.attached=!1,s("resize",o),this._stop(),this._resize(0,0),i("attach",a)},e.isAttached(this.canvas)?a():r()}unbindEvents(){B(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},B(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,i){const s=i?"set":"remove";let o,r,a,l;for(e==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+s+"DatasetHoverStyle"]()),a=0,l=t.length;a<l;++a){r=t[a];const c=r&&this.getDatasetMeta(r.datasetIndex).controller;c&&c[s+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],i=t.map(({datasetIndex:o,index:r})=>{const a=this.getDatasetMeta(o);if(!a)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:a.data[r],index:r}});!Di(i,e)&&(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,e))}notifyPlugins(t,e,i){return this._plugins.notify(this,t,e,i)}isPluginEnabled(t){return this._plugins._cache.filter(e=>e.plugin.id===t).length===1}_updateHoverStyles(t,e,i){const s=this.options.hover,o=(l,c)=>l.filter(h=>!c.some(u=>h.datasetIndex===u.datasetIndex&&h.index===u.index)),r=o(e,t),a=i?t:o(t,e);r.length&&this.updateHoverStyle(r,s.mode,!1),a.length&&s.mode&&this.updateHoverStyle(a,s.mode,!0)}_eventHandler(t,e){const i={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},s=r=>(r.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",i,s)===!1)return;const o=this._handleEvent(t,e,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,s),(o||i.changed)&&this.render(),this}_handleEvent(t,e,i){const{_active:s=[],options:o}=this,r=e,a=this._getActiveElements(t,s,i,r),l=fu(t),c=og(t,this._lastEvent,i,l);i&&(this._lastEvent=null,W(o.onHover,[t,a,this],this),l&&W(o.onClick,[t,a,this],this));const h=!Di(a,s);return(h||e)&&(this._active=a,this._updateHoverStyles(a,s,e)),this._lastEvent=c,h}_getActiveElements(t,e,i,s){if(t.type==="mouseout")return[];if(!i)return e;const o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,s)}}v(St,"defaults",Y),v(St,"instances",Mi),v(St,"overrides",ke),v(St,"registry",Lt),v(St,"version",eg),v(St,"getChart",Fr);function Nr(){return B(St.instances,n=>n._plugins.invalidate())}function rg(n,t,e){const{startAngle:i,pixelMargin:s,x:o,y:r,outerRadius:a,innerRadius:l}=t;let c=s/a;n.beginPath(),n.arc(o,r,a,i-c,e+c),l>s?(c=s/l,n.arc(o,r,l,e+c,i-c,!0)):n.arc(o,r,s,e+X,i-X),n.closePath(),n.clip()}function ag(n){return bo(n,["outerStart","outerEnd","innerStart","innerEnd"])}function lg(n,t,e,i){const s=ag(n.options.borderRadius),o=(e-t)/2,r=Math.min(o,i*t/2),a=l=>{const c=(e-Math.min(o,l))*i/2;return it(l,0,Math.min(o,c))};return{outerStart:a(s.outerStart),outerEnd:a(s.outerEnd),innerStart:it(s.innerStart,0,r),innerEnd:it(s.innerEnd,0,r)}}function Ie(n,t,e,i){return{x:e+n*Math.cos(t),y:i+n*Math.sin(t)}}function Fi(n,t,e,i,s,o){const{x:r,y:a,startAngle:l,pixelMargin:c,innerRadius:h}=t,u=Math.max(t.outerRadius+i+e-c,0),d=h>0?h+i+e+c:0;let f=0;const g=s-l;if(i){const $=h>0?h-i:0,Z=u>0?u-i:0,Q=($+Z)/2,Ot=Q!==0?g*Q/(Q+i):g;f=(g-Ot)/2}const m=Math.max(.001,g*u-e/R)/u,p=(g-m)/2,y=l+p+f,b=s-p-f,{outerStart:_,outerEnd:w,innerStart:x,innerEnd:k}=lg(t,d,u,b-y),S=u-_,M=u-w,O=y+_/S,A=b-w/M,L=d+x,F=d+k,ot=y+x/L,yt=b-k/F;if(n.beginPath(),o){const $=(O+A)/2;if(n.arc(r,a,u,O,$),n.arc(r,a,u,$,A),w>0){const rt=Ie(M,A,r,a);n.arc(rt.x,rt.y,w,A,b+X)}const Z=Ie(F,b,r,a);if(n.lineTo(Z.x,Z.y),k>0){const rt=Ie(F,yt,r,a);n.arc(rt.x,rt.y,k,b+X,yt+Math.PI)}const Q=(b-k/d+(y+x/d))/2;if(n.arc(r,a,d,b-k/d,Q,!0),n.arc(r,a,d,Q,y+x/d,!0),x>0){const rt=Ie(L,ot,r,a);n.arc(rt.x,rt.y,x,ot+Math.PI,y-X)}const Ot=Ie(S,y,r,a);if(n.lineTo(Ot.x,Ot.y),_>0){const rt=Ie(S,O,r,a);n.arc(rt.x,rt.y,_,y-X,O)}}else{n.moveTo(r,a);const $=Math.cos(O)*u+r,Z=Math.sin(O)*u+a;n.lineTo($,Z);const Q=Math.cos(A)*u+r,Ot=Math.sin(A)*u+a;n.lineTo(Q,Ot)}n.closePath()}function cg(n,t,e,i,s){const{fullCircles:o,startAngle:r,circumference:a}=t;let l=t.endAngle;if(o){Fi(n,t,e,i,l,s);for(let c=0;c<o;++c)n.fill();isNaN(a)||(l=r+(a%j||j))}return Fi(n,t,e,i,l,s),n.fill(),l}function hg(n,t,e,i,s){const{fullCircles:o,startAngle:r,circumference:a,options:l}=t,{borderWidth:c,borderJoinStyle:h,borderDash:u,borderDashOffset:d}=l,f=l.borderAlign==="inner";if(!c)return;n.setLineDash(u||[]),n.lineDashOffset=d,f?(n.lineWidth=c*2,n.lineJoin=h||"round"):(n.lineWidth=c,n.lineJoin=h||"bevel");let g=t.endAngle;if(o){Fi(n,t,e,i,g,s);for(let m=0;m<o;++m)n.stroke();isNaN(a)||(g=r+(a%j||j))}f&&rg(n,t,g),o||(Fi(n,t,e,i,g,s),n.stroke())}class Ne extends st{constructor(e){super();v(this,"circumference");v(this,"endAngle");v(this,"fullCircles");v(this,"innerRadius");v(this,"outerRadius");v(this,"pixelMargin");v(this,"startAngle");this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,e&&Object.assign(this,e)}inRange(e,i,s){const o=this.getProps(["x","y"],s),{angle:r,distance:a}=Ei(o,{x:e,y:i}),{startAngle:l,endAngle:c,innerRadius:h,outerRadius:u,circumference:d}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],s),f=(this.options.spacing+this.options.borderWidth)/2,g=C(d,c-l),m=En(r,l,c)&&l!==c,p=g>=j||m,y=Bt(a,h+f,u+f);return p&&y}getCenterPoint(e){const{x:i,y:s,startAngle:o,endAngle:r,innerRadius:a,outerRadius:l}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],e),{offset:c,spacing:h}=this.options,u=(o+r)/2,d=(a+l+h+c)/2;return{x:i+Math.cos(u)*d,y:s+Math.sin(u)*d}}tooltipPosition(e){return this.getCenterPoint(e)}draw(e){const{options:i,circumference:s}=this,o=(i.offset||0)/4,r=(i.spacing||0)/2,a=i.circular;if(this.pixelMargin=i.borderAlign==="inner"?.33:0,this.fullCircles=s>j?Math.floor(s/j):0,s===0||this.innerRadius<0||this.outerRadius<0)return;e.save();const l=(this.startAngle+this.endAngle)/2;e.translate(Math.cos(l)*o,Math.sin(l)*o);const c=1-Math.sin(Math.min(R,s||0)),h=o*c;e.fillStyle=i.backgroundColor,e.strokeStyle=i.borderColor,cg(e,this,h,r,a),hg(e,this,h,r,a),e.restore()}}v(Ne,"id","arc"),v(Ne,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0}),v(Ne,"defaultRoutes",{backgroundColor:"backgroundColor"}),v(Ne,"descriptors",{_scriptable:!0,_indexable:e=>e!=="borderDash"});function Ul(n,t,e=t){n.lineCap=C(e.borderCapStyle,t.borderCapStyle),n.setLineDash(C(e.borderDash,t.borderDash)),n.lineDashOffset=C(e.borderDashOffset,t.borderDashOffset),n.lineJoin=C(e.borderJoinStyle,t.borderJoinStyle),n.lineWidth=C(e.borderWidth,t.borderWidth),n.strokeStyle=C(e.borderColor,t.borderColor)}function ug(n,t,e){n.lineTo(e.x,e.y)}function dg(n){return n.stepped?Ru:n.tension||n.cubicInterpolationMode==="monotone"?Fu:ug}function Xl(n,t,e={}){const i=n.length,{start:s=0,end:o=i-1}=e,{start:r,end:a}=t,l=Math.max(s,r),c=Math.min(o,a),h=s<r&&o<r||s>a&&o>a;return{count:i,start:l,loop:t.loop,ilen:c<l&&!h?i+c-l:c-l}}function fg(n,t,e,i){const{points:s,options:o}=t,{count:r,start:a,loop:l,ilen:c}=Xl(s,e,i),h=dg(o);let{move:u=!0,reverse:d}=i||{},f,g,m;for(f=0;f<=c;++f)g=s[(a+(d?c-f:f))%r],!g.skip&&(u?(n.moveTo(g.x,g.y),u=!1):h(n,m,g,d,o.stepped),m=g);return l&&(g=s[(a+(d?c:0))%r],h(n,m,g,d,o.stepped)),!!l}function gg(n,t,e,i){const s=t.points,{count:o,start:r,ilen:a}=Xl(s,e,i),{move:l=!0,reverse:c}=i||{};let h=0,u=0,d,f,g,m,p,y;const b=w=>(r+(c?a-w:w))%o,_=()=>{m!==p&&(n.lineTo(h,p),n.lineTo(h,m),n.lineTo(h,y))};for(l&&(f=s[b(0)],n.moveTo(f.x,f.y)),d=0;d<=a;++d){if(f=s[b(d)],f.skip)continue;const w=f.x,x=f.y,k=w|0;k===g?(x<m?m=x:x>p&&(p=x),h=(u*h+w)/++u):(_(),n.lineTo(w,x),g=k,u=0,m=p=x),y=x}_()}function Rs(n){const t=n.options,e=t.borderDash&&t.borderDash.length;return!n._decimated&&!n._loop&&!t.tension&&t.cubicInterpolationMode!=="monotone"&&!t.stepped&&!e?gg:fg}function mg(n){return n.stepped?gd:n.tension||n.cubicInterpolationMode==="monotone"?md:xe}function pg(n,t,e,i){let s=t._path;s||(s=t._path=new Path2D,t.path(s,e,i)&&s.closePath()),Ul(n,t.options),n.stroke(s)}function yg(n,t,e,i){const{segments:s,options:o}=t,r=Rs(t);for(const a of s)Ul(n,o,a.style),n.beginPath(),r(n,t,a,{start:e,end:e+i-1})&&n.closePath(),n.stroke()}const bg=typeof Path2D=="function";function xg(n,t,e,i){bg&&!t.options.segment?pg(n,t,e,i):yg(n,t,e,i)}class ee extends st{constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){const i=this.options;if((i.tension||i.cubicInterpolationMode==="monotone")&&!i.stepped&&!this._pointsUpdated){const s=i.spanGaps?this._loop:this._fullLoop;rd(this._points,i,t,s,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=wd(this,this.options.segment))}first(){const t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){const t=this.segments,e=this.points,i=t.length;return i&&e[t[i-1].end]}interpolate(t,e){const i=this.options,s=t[e],o=this.points,r=El(this,{property:e,start:s,end:s});if(!r.length)return;const a=[],l=mg(i);let c,h;for(c=0,h=r.length;c<h;++c){const{start:u,end:d}=r[c],f=o[u],g=o[d];if(f===g){a.push(f);continue}const m=Math.abs((s-f[e])/(g[e]-f[e])),p=l(f,g,m,i.stepped);p[e]=t[e],a.push(p)}return a.length===1?a[0]:a}pathSegment(t,e,i){return Rs(this)(t,this,e,i)}path(t,e,i){const s=this.segments,o=Rs(this);let r=this._loop;e=e||0,i=i||this.points.length-e;for(const a of s)r&=o(t,this,a,{start:e,end:e+i-1});return!!r}draw(t,e,i,s){const o=this.options||{};(this.points||[]).length&&o.borderWidth&&(t.save(),xg(t,this,i,s),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}v(ee,"id","line"),v(ee,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),v(ee,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),v(ee,"descriptors",{_scriptable:!0,_indexable:t=>t!=="borderDash"&&t!=="fill"});function zr(n,t,e,i){const s=n.options,{[e]:o}=n.getProps([e],i);return Math.abs(t-o)<s.radius+s.hitRadius}class Tn extends st{constructor(e){super();v(this,"parsed");v(this,"skip");v(this,"stop");this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,e&&Object.assign(this,e)}inRange(e,i,s){const o=this.options,{x:r,y:a}=this.getProps(["x","y"],s);return Math.pow(e-r,2)+Math.pow(i-a,2)<Math.pow(o.hitRadius+o.radius,2)}inXRange(e,i){return zr(this,e,"x",i)}inYRange(e,i){return zr(this,e,"y",i)}getCenterPoint(e){const{x:i,y:s}=this.getProps(["x","y"],e);return{x:i,y:s}}size(e){e=e||this.options||{};let i=e.radius||0;i=Math.max(i,i&&e.hoverRadius||0);const s=i&&e.borderWidth||0;return(i+s)*2}draw(e,i){const s=this.options;this.skip||s.radius<.1||!$t(this,i,this.size(s)/2)||(e.strokeStyle=s.borderColor,e.lineWidth=s.borderWidth,e.fillStyle=s.backgroundColor,As(e,s,this.x,this.y))}getRange(){const e=this.options||{};return e.radius+e.hitRadius}}v(Tn,"id","point"),v(Tn,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),v(Tn,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function Zl(n,t){const{x:e,y:i,base:s,width:o,height:r}=n.getProps(["x","y","base","width","height"],t);let a,l,c,h,u;return n.horizontal?(u=r/2,a=Math.min(e,s),l=Math.max(e,s),c=i-u,h=i+u):(u=o/2,a=e-u,l=e+u,c=Math.min(i,s),h=Math.max(i,s)),{left:a,top:c,right:l,bottom:h}}function ne(n,t,e,i){return n?0:it(t,e,i)}function _g(n,t,e){const i=n.options.borderWidth,s=n.borderSkipped,o=wl(i);return{t:ne(s.top,o.top,0,e),r:ne(s.right,o.right,0,t),b:ne(s.bottom,o.bottom,0,e),l:ne(s.left,o.left,0,t)}}function wg(n,t,e){const{enableBorderRadius:i}=n.getProps(["enableBorderRadius"]),s=n.options.borderRadius,o=se(s),r=Math.min(t,e),a=n.borderSkipped,l=i||E(s);return{topLeft:ne(!l||a.top||a.left,o.topLeft,0,r),topRight:ne(!l||a.top||a.right,o.topRight,0,r),bottomLeft:ne(!l||a.bottom||a.left,o.bottomLeft,0,r),bottomRight:ne(!l||a.bottom||a.right,o.bottomRight,0,r)}}function vg(n){const t=Zl(n),e=t.right-t.left,i=t.bottom-t.top,s=_g(n,e/2,i/2),o=wg(n,e/2,i/2);return{outer:{x:t.left,y:t.top,w:e,h:i,radius:o},inner:{x:t.left+s.l,y:t.top+s.t,w:e-s.l-s.r,h:i-s.t-s.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(s.t,s.l)),topRight:Math.max(0,o.topRight-Math.max(s.t,s.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(s.b,s.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(s.b,s.r))}}}}function ms(n,t,e,i){const s=t===null,o=e===null,a=n&&!(s&&o)&&Zl(n,i);return a&&(s||Bt(t,a.left,a.right))&&(o||Bt(e,a.top,a.bottom))}function kg(n){return n.topLeft||n.topRight||n.bottomLeft||n.bottomRight}function Sg(n,t){n.rect(t.x,t.y,t.w,t.h)}function ps(n,t,e={}){const i=n.x!==e.x?-t:0,s=n.y!==e.y?-t:0,o=(n.x+n.w!==e.x+e.w?t:0)-i,r=(n.y+n.h!==e.y+e.h?t:0)-s;return{x:n.x+i,y:n.y+s,w:n.w+o,h:n.h+r,radius:n.radius}}class On extends st{constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){const{inflateAmount:e,options:{borderColor:i,backgroundColor:s}}=this,{inner:o,outer:r}=vg(this),a=kg(r.radius)?$e:Sg;t.save(),(r.w!==o.w||r.h!==o.h)&&(t.beginPath(),a(t,ps(r,e,o)),t.clip(),a(t,ps(o,-e,r)),t.fillStyle=i,t.fill("evenodd")),t.beginPath(),a(t,ps(o,e)),t.fillStyle=s,t.fill(),t.restore()}inRange(t,e,i){return ms(this,t,e,i)}inXRange(t,e){return ms(this,t,null,e)}inYRange(t,e){return ms(this,null,t,e)}getCenterPoint(t){const{x:e,y:i,base:s,horizontal:o}=this.getProps(["x","y","base","horizontal"],t);return{x:o?(e+s)/2:e,y:o?i:(i+s)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}}v(On,"id","bar"),v(On,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),v(On,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});var Mg=Object.freeze({__proto__:null,ArcElement:Ne,BarElement:On,LineElement:ee,PointElement:Tn});const Fs=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],Wr=Fs.map(n=>n.replace("rgb(","rgba(").replace(")",", 0.5)"));function ql(n){return Fs[n%Fs.length]}function Gl(n){return Wr[n%Wr.length]}function Tg(n,t){return n.borderColor=ql(t),n.backgroundColor=Gl(t),++t}function Og(n,t){return n.backgroundColor=n.data.map(()=>ql(t++)),t}function Dg(n,t){return n.backgroundColor=n.data.map(()=>Gl(t++)),t}function Cg(n){let t=0;return(e,i)=>{const s=n.getDatasetMeta(i).controller;s instanceof te?t=Og(e,t):s instanceof Mn?t=Dg(e,t):s&&(t=Tg(e,t))}}function Vr(n){let t;for(t in n)if(n[t].borderColor||n[t].backgroundColor)return!0;return!1}function Pg(n){return n&&(n.borderColor||n.backgroundColor)}function Ag(){return Y.borderColor!=="rgba(0,0,0,0.1)"||Y.backgroundColor!=="rgba(0,0,0,0.1)"}var Eg={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(n,t,e){if(!e.enabled)return;const{data:{datasets:i},options:s}=n.config,{elements:o}=s,r=Vr(i)||Pg(s)||o&&Vr(o)||Ag();if(!e.forceOverride&&r)return;const a=Cg(n);i.forEach(a)}};function Ig(n,t,e,i,s){const o=s.samples||i;if(o>=e)return n.slice(t,t+e);const r=[],a=(e-2)/(o-2);let l=0;const c=t+e-1;let h=t,u,d,f,g,m;for(r[l++]=n[h],u=0;u<o-2;u++){let p=0,y=0,b;const _=Math.floor((u+1)*a)+1+t,w=Math.min(Math.floor((u+2)*a)+1,e)+t,x=w-_;for(b=_;b<w;b++)p+=n[b].x,y+=n[b].y;p/=x,y/=x;const k=Math.floor(u*a)+1+t,S=Math.min(Math.floor((u+1)*a)+1,e)+t,{x:M,y:O}=n[h];for(f=g=-1,b=k;b<S;b++)g=.5*Math.abs((M-p)*(n[b].y-O)-(M-n[b].x)*(y-O)),g>f&&(f=g,d=n[b],m=b);r[l++]=d,h=m}return r[l++]=n[c],r}function Lg(n,t,e,i){let s=0,o=0,r,a,l,c,h,u,d,f,g,m;const p=[],y=t+e-1,b=n[t].x,w=n[y].x-b;for(r=t;r<t+e;++r){a=n[r],l=(a.x-b)/w*i,c=a.y;const x=l|0;if(x===h)c<g?(g=c,u=r):c>m&&(m=c,d=r),s=(o*s+a.x)/++o;else{const k=r-1;if(!I(u)&&!I(d)){const S=Math.min(u,d),M=Math.max(u,d);S!==f&&S!==k&&p.push({...n[S],x:s}),M!==f&&M!==k&&p.push({...n[M],x:s})}r>0&&k!==f&&p.push(n[k]),p.push(a),h=x,o=0,g=m=c,u=d=f=r}}return p}function Jl(n){if(n._decimated){const t=n._data;delete n._decimated,delete n._data,Object.defineProperty(n,"data",{configurable:!0,enumerable:!0,writable:!0,value:t})}}function Br(n){n.data.datasets.forEach(t=>{Jl(t)})}function Rg(n,t){const e=t.length;let i=0,s;const{iScale:o}=n,{min:r,max:a,minDefined:l,maxDefined:c}=o.getUserBounds();return l&&(i=it(Ht(t,o.axis,r).lo,0,e-1)),c?s=it(Ht(t,o.axis,a).hi+1,i,e)-i:s=e-i,{start:i,count:s}}var Fg={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(n,t,e)=>{if(!e.enabled){Br(n);return}const i=n.width;n.data.datasets.forEach((s,o)=>{const{_data:r,indexAxis:a}=s,l=n.getDatasetMeta(o),c=r||s.data;if(U([a,n.options.indexAxis])==="y"||!l.controller.supportsDecimation)return;const h=n.scales[l.xAxisID];if(h.type!=="linear"&&h.type!=="time"||n.options.parsing)return;let{start:u,count:d}=Rg(l,c);const f=e.threshold||4*i;if(d<=f){Jl(s);return}I(r)&&(s._data=c,delete s.data,Object.defineProperty(s,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(m){this._data=m}}));let g;switch(e.algorithm){case"lttb":g=Ig(c,u,d,i,e);break;case"min-max":g=Lg(c,u,d,i);break;default:throw new Error(`Unsupported decimation algorithm '${e.algorithm}'`)}s._decimated=g})},destroy(n){Br(n)}};function Ng(n,t,e){const i=n.segments,s=n.points,o=t.points,r=[];for(const a of i){let{start:l,end:c}=a;c=To(l,c,s);const h=Ns(e,s[l],s[c],a.loop);if(!t.segments){r.push({source:a,target:h,start:s[l],end:s[c]});continue}const u=El(t,h);for(const d of u){const f=Ns(e,o[d.start],o[d.end],d.loop),g=Al(a,s,f);for(const m of g)r.push({source:m,target:d,start:{[e]:Hr(h,f,"start",Math.max)},end:{[e]:Hr(h,f,"end",Math.min)}})}}return r}function Ns(n,t,e,i){if(i)return;let s=t[n],o=e[n];return n==="angle"&&(s=xt(s),o=xt(o)),{property:n,start:s,end:o}}function zg(n,t){const{x:e=null,y:i=null}=n||{},s=t.points,o=[];return t.segments.forEach(({start:r,end:a})=>{a=To(r,a,s);const l=s[r],c=s[a];i!==null?(o.push({x:l.x,y:i}),o.push({x:c.x,y:i})):e!==null&&(o.push({x:e,y:l.y}),o.push({x:e,y:c.y}))}),o}function To(n,t,e){for(;t>n;t--){const i=e[t];if(!isNaN(i.x)&&!isNaN(i.y))break}return t}function Hr(n,t,e,i){return n&&t?i(n[e],t[e]):n?n[e]:t?t[e]:0}function Kl(n,t){let e=[],i=!1;return V(n)?(i=!0,e=n):e=zg(n,t),e.length?new ee({points:e,options:{tension:0},_loop:i,_fullLoop:i}):null}function $r(n){return n&&n.fill!==!1}function Wg(n,t,e){let s=n[t].fill;const o=[t];let r;if(!e)return s;for(;s!==!1&&o.indexOf(s)===-1;){if(!q(s))return s;if(r=n[s],!r)return!1;if(r.visible)return s;o.push(s),s=r.fill}return!1}function Vg(n,t,e){const i=jg(n);if(E(i))return isNaN(i.value)?!1:i;let s=parseFloat(i);return q(s)&&Math.floor(s)===s?Bg(i[0],t,s,e):["origin","start","end","stack","shape"].indexOf(i)>=0&&i}function Bg(n,t,e,i){return(n==="-"||n==="+")&&(e=t+e),e===t||e<0||e>=i?!1:e}function Hg(n,t){let e=null;return n==="start"?e=t.bottom:n==="end"?e=t.top:E(n)?e=t.getPixelForValue(n.value):t.getBasePixel&&(e=t.getBasePixel()),e}function $g(n,t,e){let i;return n==="start"?i=e:n==="end"?i=t.options.reverse?t.min:t.max:E(n)?i=n.value:i=t.getBaseValue(),i}function jg(n){const t=n.options,e=t.fill;let i=C(e&&e.target,e);return i===void 0&&(i=!!t.backgroundColor),i===!1||i===null?!1:i===!0?"origin":i}function Yg(n){const{scale:t,index:e,line:i}=n,s=[],o=i.segments,r=i.points,a=Ug(t,e);a.push(Kl({x:null,y:t.bottom},i));for(let l=0;l<o.length;l++){const c=o[l];for(let h=c.start;h<=c.end;h++)Xg(s,r[h],a)}return new ee({points:s,options:{}})}function Ug(n,t){const e=[],i=n.getMatchingVisibleMetas("line");for(let s=0;s<i.length;s++){const o=i[s];if(o.index===t)break;o.hidden||e.unshift(o.dataset)}return e}function Xg(n,t,e){const i=[];for(let s=0;s<e.length;s++){const o=e[s],{first:r,last:a,point:l}=Zg(o,t,"x");if(!(!l||r&&a)){if(r)i.unshift(l);else if(n.push(l),!a)break}}n.push(...i)}function Zg(n,t,e){const i=n.interpolate(t,e);if(!i)return{};const s=i[e],o=n.segments,r=n.points;let a=!1,l=!1;for(let c=0;c<o.length;c++){const h=o[c],u=r[h.start][e],d=r[h.end][e];if(Bt(s,u,d)){a=s===u,l=s===d;break}}return{first:a,last:l,point:i}}class Ql{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,i){const{x:s,y:o,radius:r}=this;return e=e||{start:0,end:j},t.arc(s,o,r,e.end,e.start,!0),!i.bounds}interpolate(t){const{x:e,y:i,radius:s}=this,o=t.angle;return{x:e+Math.cos(o)*s,y:i+Math.sin(o)*s,angle:o}}}function qg(n){const{chart:t,fill:e,line:i}=n;if(q(e))return Gg(t,e);if(e==="stack")return Yg(n);if(e==="shape")return!0;const s=Jg(n);return s instanceof Ql?s:Kl(s,i)}function Gg(n,t){const e=n.getDatasetMeta(t);return e&&n.isDatasetVisible(t)?e.dataset:null}function Jg(n){return(n.scale||{}).getPointPositionForValue?Qg(n):Kg(n)}function Kg(n){const{scale:t={},fill:e}=n,i=Hg(e,t);if(q(i)){const s=t.isHorizontal();return{x:s?i:null,y:s?null:i}}return null}function Qg(n){const{scale:t,fill:e}=n,i=t.options,s=t.getLabels().length,o=i.reverse?t.max:t.min,r=$g(e,t,o),a=[];if(i.grid.circular){const l=t.getPointPositionForValue(0,o);return new Ql({x:l.x,y:l.y,radius:t.getDistanceFromCenterForValue(r)})}for(let l=0;l<s;++l)a.push(t.getPointPositionForValue(l,r));return a}function ys(n,t,e){const i=qg(t),{chart:s,index:o,line:r,scale:a,axis:l}=t,c=r.options,h=c.fill,u=c.backgroundColor,{above:d=u,below:f=u}=h||{},g=s.getDatasetMeta(o),m=Il(s,g);i&&r.points.length&&(Wn(n,e),tm(n,{line:r,target:i,above:d,below:f,area:e,scale:a,axis:l,clip:m}),Vn(n))}function tm(n,t){const{line:e,target:i,above:s,below:o,area:r,scale:a,clip:l}=t,c=e._loop?"angle":t.axis;n.save(),c==="x"&&o!==s&&(jr(n,i,r.top),Yr(n,{line:e,target:i,color:s,scale:a,property:c,clip:l}),n.restore(),n.save(),jr(n,i,r.bottom)),Yr(n,{line:e,target:i,color:o,scale:a,property:c,clip:l}),n.restore()}function jr(n,t,e){const{segments:i,points:s}=t;let o=!0,r=!1;n.beginPath();for(const a of i){const{start:l,end:c}=a,h=s[l],u=s[To(l,c,s)];o?(n.moveTo(h.x,h.y),o=!1):(n.lineTo(h.x,e),n.lineTo(h.x,h.y)),r=!!t.pathSegment(n,a,{move:r}),r?n.closePath():n.lineTo(u.x,e)}n.lineTo(t.first().x,e),n.closePath(),n.clip()}function Yr(n,t){const{line:e,target:i,property:s,color:o,scale:r,clip:a}=t,l=Ng(e,i,s);for(const{source:c,target:h,start:u,end:d}of l){const{style:{backgroundColor:f=o}={}}=c,g=i!==!0;n.save(),n.fillStyle=f,em(n,r,a,g&&Ns(s,u,d)),n.beginPath();const m=!!e.pathSegment(n,c);let p;if(g){m?n.closePath():Ur(n,i,d,s);const y=!!i.pathSegment(n,h,{move:m,reverse:!0});p=m&&y,p||Ur(n,i,u,s)}n.closePath(),n.fill(p?"evenodd":"nonzero"),n.restore()}}function em(n,t,e,i){const s=t.chart.chartArea,{property:o,start:r,end:a}=i||{};if(o==="x"||o==="y"){let l,c,h,u;o==="x"?(l=r,c=s.top,h=a,u=s.bottom):(l=s.left,c=r,h=s.right,u=a),n.beginPath(),e&&(l=Math.max(l,e.left),h=Math.min(h,e.right),c=Math.max(c,e.top),u=Math.min(u,e.bottom)),n.rect(l,c,h-l,u-c),n.clip()}}function Ur(n,t,e,i){const s=t.interpolate(e,i);s&&n.lineTo(s.x,s.y)}var nm={id:"filler",afterDatasetsUpdate(n,t,e){const i=(n.data.datasets||[]).length,s=[];let o,r,a,l;for(r=0;r<i;++r)o=n.getDatasetMeta(r),a=o.dataset,l=null,a&&a.options&&a instanceof ee&&(l={visible:n.isDatasetVisible(r),index:r,fill:Vg(a,r,i),chart:n,axis:o.controller.options.indexAxis,scale:o.vScale,line:a}),o.$filler=l,s.push(l);for(r=0;r<i;++r)l=s[r],!(!l||l.fill===!1)&&(l.fill=Wg(s,r,e.propagate))},beforeDraw(n,t,e){const i=e.drawTime==="beforeDraw",s=n.getSortedVisibleDatasetMetas(),o=n.chartArea;for(let r=s.length-1;r>=0;--r){const a=s[r].$filler;a&&(a.line.updateControlPoints(o,a.axis),i&&a.fill&&ys(n.ctx,a,o))}},beforeDatasetsDraw(n,t,e){if(e.drawTime!=="beforeDatasetsDraw")return;const i=n.getSortedVisibleDatasetMetas();for(let s=i.length-1;s>=0;--s){const o=i[s].$filler;$r(o)&&ys(n.ctx,o,n.chartArea)}},beforeDatasetDraw(n,t,e){const i=t.meta.$filler;!$r(i)||e.drawTime!=="beforeDatasetDraw"||ys(n.ctx,i,n.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const Xr=(n,t)=>{let{boxHeight:e=t,boxWidth:i=t}=n;return n.usePointStyle&&(e=Math.min(e,t),i=n.pointStyleWidth||Math.min(i,t)),{boxWidth:i,boxHeight:e,itemHeight:Math.max(t,e)}},im=(n,t)=>n!==null&&t!==null&&n.datasetIndex===t.datasetIndex&&n.index===t.index;class Zr extends st{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,i){this.maxWidth=t,this.maxHeight=e,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let e=W(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(i=>t.filter(i,this.chart.data))),t.sort&&(e=e.sort((i,s)=>t.sort(i,s,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){const{options:t,ctx:e}=this;if(!t.display){this.width=this.height=0;return}const i=t.labels,s=G(i.font),o=s.size,r=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=Xr(i,o);let c,h;e.font=s.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(r,o,a,l)+10):(h=this.maxHeight,c=this._fitCols(r,s,a,l)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(h,t.maxHeight||this.maxHeight)}_fitRows(t,e,i,s){const{ctx:o,maxWidth:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=s+a;let u=t;o.textAlign="left",o.textBaseline="middle";let d=-1,f=-h;return this.legendItems.forEach((g,m)=>{const p=i+e/2+o.measureText(g.text).width;(m===0||c[c.length-1]+p+2*a>r)&&(u+=h,c[c.length-(m>0?0:1)]=0,f+=h,d++),l[m]={left:0,top:f,row:d,width:p,height:s},c[c.length-1]+=p+a}),u}_fitCols(t,e,i,s){const{ctx:o,maxHeight:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=r-t;let u=a,d=0,f=0,g=0,m=0;return this.legendItems.forEach((p,y)=>{const{itemWidth:b,itemHeight:_}=sm(i,e,o,p,s);y>0&&f+_+2*a>h&&(u+=d+a,c.push({width:d,height:f}),g+=d+a,m++,d=f=0),l[y]={left:g,top:f,col:m,width:b,height:_},d=Math.max(d,b),f+=_+a}),u+=d,c.push({width:d,height:f}),u}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:i,labels:{padding:s},rtl:o}}=this,r=We(o,this.left,this.width);if(this.isHorizontal()){let a=0,l=at(i,this.left+s,this.right-this.lineWidths[a]);for(const c of e)a!==c.row&&(a=c.row,l=at(i,this.left+s,this.right-this.lineWidths[a])),c.top+=this.top+t+s,c.left=r.leftForLtr(r.x(l),c.width),l+=c.width+s}else{let a=0,l=at(i,this.top+t+s,this.bottom-this.columnSizes[a].height);for(const c of e)c.col!==a&&(a=c.col,l=at(i,this.top+t+s,this.bottom-this.columnSizes[a].height)),c.top=l,c.left+=this.left+s,c.left=r.leftForLtr(r.x(c.left),c.width),l+=c.height+s}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const t=this.ctx;Wn(t,this),this._draw(),Vn(t)}}_draw(){const{options:t,columnSizes:e,lineWidths:i,ctx:s}=this,{align:o,labels:r}=t,a=Y.color,l=We(t.rtl,this.left,this.width),c=G(r.font),{padding:h}=r,u=c.size,d=u/2;let f;this.drawTitle(),s.textAlign=l.textAlign("left"),s.textBaseline="middle",s.lineWidth=.5,s.font=c.string;const{boxWidth:g,boxHeight:m,itemHeight:p}=Xr(r,u),y=function(k,S,M){if(isNaN(g)||g<=0||isNaN(m)||m<0)return;s.save();const O=C(M.lineWidth,1);if(s.fillStyle=C(M.fillStyle,a),s.lineCap=C(M.lineCap,"butt"),s.lineDashOffset=C(M.lineDashOffset,0),s.lineJoin=C(M.lineJoin,"miter"),s.lineWidth=O,s.strokeStyle=C(M.strokeStyle,a),s.setLineDash(C(M.lineDash,[])),r.usePointStyle){const A={radius:m*Math.SQRT2/2,pointStyle:M.pointStyle,rotation:M.rotation,borderWidth:O},L=l.xPlus(k,g/2),F=S+d;_l(s,A,L,F,r.pointStyleWidth&&g)}else{const A=S+Math.max((u-m)/2,0),L=l.leftForLtr(k,g),F=se(M.borderRadius);s.beginPath(),Object.values(F).some(ot=>ot!==0)?$e(s,{x:L,y:A,w:g,h:m,radius:F}):s.rect(L,A,g,m),s.fill(),O!==0&&s.stroke()}s.restore()},b=function(k,S,M){Se(s,M.text,k,S+p/2,c,{strikethrough:M.hidden,textAlign:l.textAlign(M.textAlign)})},_=this.isHorizontal(),w=this._computeTitleHeight();_?f={x:at(o,this.left+h,this.right-i[0]),y:this.top+h+w,line:0}:f={x:this.left+h,y:at(o,this.top+w+h,this.bottom-e[0].height),line:0},Dl(this.ctx,t.textDirection);const x=p+h;this.legendItems.forEach((k,S)=>{s.strokeStyle=k.fontColor,s.fillStyle=k.fontColor;const M=s.measureText(k.text).width,O=l.textAlign(k.textAlign||(k.textAlign=r.textAlign)),A=g+d+M;let L=f.x,F=f.y;l.setWidth(this.width),_?S>0&&L+A+h>this.right&&(F=f.y+=x,f.line++,L=f.x=at(o,this.left+h,this.right-i[f.line])):S>0&&F+x>this.bottom&&(L=f.x=L+e[f.line].width+h,f.line++,F=f.y=at(o,this.top+w+h,this.bottom-e[f.line].height));const ot=l.x(L);if(y(ot,F,k),L=Su(O,L+g+d,_?L+A:this.right,t.rtl),b(l.x(L),F,k),_)f.x+=A+h;else if(typeof k.text!="string"){const yt=c.lineHeight;f.y+=tc(k,yt)+h}else f.y+=x}),Cl(this.ctx,t.textDirection)}drawTitle(){const t=this.options,e=t.title,i=G(e.font),s=tt(e.padding);if(!e.display)return;const o=We(t.rtl,this.left,this.width),r=this.ctx,a=e.position,l=i.size/2,c=s.top+l;let h,u=this.left,d=this.width;if(this.isHorizontal())d=Math.max(...this.lineWidths),h=this.top+c,u=at(t.align,u,this.right-d);else{const g=this.columnSizes.reduce((m,p)=>Math.max(m,p.height),0);h=c+at(t.align,this.top,this.bottom-g-t.labels.padding-this._computeTitleHeight())}const f=at(a,u,u+d);r.textAlign=o.textAlign(po(a)),r.textBaseline="middle",r.strokeStyle=e.color,r.fillStyle=e.color,r.font=i.string,Se(r,e.text,f,h,i)}_computeTitleHeight(){const t=this.options.title,e=G(t.font),i=tt(t.padding);return t.display?e.lineHeight+i.height:0}_getLegendItemAt(t,e){let i,s,o;if(Bt(t,this.left,this.right)&&Bt(e,this.top,this.bottom)){for(o=this.legendHitBoxes,i=0;i<o.length;++i)if(s=o[i],Bt(t,s.left,s.left+s.width)&&Bt(e,s.top,s.top+s.height))return this.legendItems[i]}return null}handleEvent(t){const e=this.options;if(!am(t.type,e))return;const i=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){const s=this._hoveredItem,o=im(s,i);s&&!o&&W(e.onLeave,[t,s,this],this),this._hoveredItem=i,i&&!o&&W(e.onHover,[t,i,this],this)}else i&&W(e.onClick,[t,i,this],this)}}function sm(n,t,e,i,s){const o=om(i,n,t,e),r=rm(s,i,t.lineHeight);return{itemWidth:o,itemHeight:r}}function om(n,t,e,i){let s=n.text;return s&&typeof s!="string"&&(s=s.reduce((o,r)=>o.length>r.length?o:r)),t+e.size/2+i.measureText(s).width}function rm(n,t,e){let i=n;return typeof t.text!="string"&&(i=tc(t,e)),i}function tc(n,t){const e=n.text?n.text.length:0;return t*e}function am(n,t){return!!((n==="mousemove"||n==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(n==="click"||n==="mouseup"))}var lm={id:"legend",_element:Zr,start(n,t,e){const i=n.legend=new Zr({ctx:n.ctx,options:e,chart:n});ht.configure(n,i,e),ht.addBox(n,i)},stop(n){ht.removeBox(n,n.legend),delete n.legend},beforeUpdate(n,t,e){const i=n.legend;ht.configure(n,i,e),i.options=e},afterUpdate(n){const t=n.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(n,t){t.replay||n.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(n,t,e){const i=t.datasetIndex,s=e.chart;s.isDatasetVisible(i)?(s.hide(i),t.hidden=!0):(s.show(i),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:n=>n.chart.options.color,boxWidth:40,padding:10,generateLabels(n){const t=n.data.datasets,{labels:{usePointStyle:e,pointStyle:i,textAlign:s,color:o,useBorderRadius:r,borderRadius:a}}=n.legend.options;return n._getSortedDatasetMetas().map(l=>{const c=l.controller.getStyle(e?0:void 0),h=tt(c.borderWidth);return{text:t[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:i||c.pointStyle,rotation:c.rotation,textAlign:s||c.textAlign,borderRadius:r&&(a||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:n=>n.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:n=>!n.startsWith("on"),labels:{_scriptable:n=>!["generateLabels","filter","sort"].includes(n)}}};class Oo extends st{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){const i=this.options;if(this.left=0,this.top=0,!i.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;const s=V(i.text)?i.text.length:1;this._padding=tt(i.padding);const o=s*G(i.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){const t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){const{top:e,left:i,bottom:s,right:o,options:r}=this,a=r.align;let l=0,c,h,u;return this.isHorizontal()?(h=at(a,i,o),u=e+t,c=o-i):(r.position==="left"?(h=i+t,u=at(a,s,e),l=R*-.5):(h=o-t,u=at(a,e,s),l=R*.5),c=s-e),{titleX:h,titleY:u,maxWidth:c,rotation:l}}draw(){const t=this.ctx,e=this.options;if(!e.display)return;const i=G(e.font),o=i.lineHeight/2+this._padding.top,{titleX:r,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(o);Se(t,e.text,0,0,i,{color:e.color,maxWidth:l,rotation:c,textAlign:po(e.align),textBaseline:"middle",translation:[r,a]})}}function cm(n,t){const e=new Oo({ctx:n.ctx,options:t,chart:n});ht.configure(n,e,t),ht.addBox(n,e),n.titleBlock=e}var hm={id:"title",_element:Oo,start(n,t,e){cm(n,e)},stop(n){const t=n.titleBlock;ht.removeBox(n,t),delete n.titleBlock},beforeUpdate(n,t,e){const i=n.titleBlock;ht.configure(n,i,e),i.options=e},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const ai=new WeakMap;var um={id:"subtitle",start(n,t,e){const i=new Oo({ctx:n.ctx,options:e,chart:n});ht.configure(n,i,e),ht.addBox(n,i),ai.set(n,i)},stop(n){ht.removeBox(n,ai.get(n)),ai.delete(n)},beforeUpdate(n,t,e){const i=ai.get(n);ht.configure(n,i,e),i.options=e},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const mn={average(n){if(!n.length)return!1;let t,e,i=new Set,s=0,o=0;for(t=0,e=n.length;t<e;++t){const a=n[t].element;if(a&&a.hasValue()){const l=a.tooltipPosition();i.add(l.x),s+=l.y,++o}}return o===0||i.size===0?!1:{x:[...i].reduce((a,l)=>a+l)/i.size,y:s/o}},nearest(n,t){if(!n.length)return!1;let e=t.x,i=t.y,s=Number.POSITIVE_INFINITY,o,r,a;for(o=0,r=n.length;o<r;++o){const l=n[o].element;if(l&&l.hasValue()){const c=l.getCenterPoint(),h=He(t,c);h<s&&(s=h,a=l)}}if(a){const l=a.tooltipPosition();e=l.x,i=l.y}return{x:e,y:i}}};function It(n,t){return t&&(V(t)?Array.prototype.push.apply(n,t):n.push(t)),n}function Wt(n){return(typeof n=="string"||n instanceof String)&&n.indexOf(`
`)>-1?n.split(`
`):n}function dm(n,t){const{element:e,datasetIndex:i,index:s}=t,o=n.getDatasetMeta(i).controller,{label:r,value:a}=o.getLabelAndValue(s);return{chart:n,label:r,parsed:o.getParsed(s),raw:n.data.datasets[i].data[s],formattedValue:a,dataset:o.getDataset(),dataIndex:s,datasetIndex:i,element:e}}function qr(n,t){const e=n.chart.ctx,{body:i,footer:s,title:o}=n,{boxWidth:r,boxHeight:a}=t,l=G(t.bodyFont),c=G(t.titleFont),h=G(t.footerFont),u=o.length,d=s.length,f=i.length,g=tt(t.padding);let m=g.height,p=0,y=i.reduce((w,x)=>w+x.before.length+x.lines.length+x.after.length,0);if(y+=n.beforeBody.length+n.afterBody.length,u&&(m+=u*c.lineHeight+(u-1)*t.titleSpacing+t.titleMarginBottom),y){const w=t.displayColors?Math.max(a,l.lineHeight):l.lineHeight;m+=f*w+(y-f)*l.lineHeight+(y-1)*t.bodySpacing}d&&(m+=t.footerMarginTop+d*h.lineHeight+(d-1)*t.footerSpacing);let b=0;const _=function(w){p=Math.max(p,e.measureText(w).width+b)};return e.save(),e.font=c.string,B(n.title,_),e.font=l.string,B(n.beforeBody.concat(n.afterBody),_),b=t.displayColors?r+2+t.boxPadding:0,B(i,w=>{B(w.before,_),B(w.lines,_),B(w.after,_)}),b=0,e.font=h.string,B(n.footer,_),e.restore(),p+=g.width,{width:p,height:m}}function fm(n,t){const{y:e,height:i}=t;return e<i/2?"top":e>n.height-i/2?"bottom":"center"}function gm(n,t,e,i){const{x:s,width:o}=i,r=e.caretSize+e.caretPadding;if(n==="left"&&s+o+r>t.width||n==="right"&&s-o-r<0)return!0}function mm(n,t,e,i){const{x:s,width:o}=e,{width:r,chartArea:{left:a,right:l}}=n;let c="center";return i==="center"?c=s<=(a+l)/2?"left":"right":s<=o/2?c="left":s>=r-o/2&&(c="right"),gm(c,n,t,e)&&(c="center"),c}function Gr(n,t,e){const i=e.yAlign||t.yAlign||fm(n,e);return{xAlign:e.xAlign||t.xAlign||mm(n,t,e,i),yAlign:i}}function pm(n,t){let{x:e,width:i}=n;return t==="right"?e-=i:t==="center"&&(e-=i/2),e}function ym(n,t,e){let{y:i,height:s}=n;return t==="top"?i+=e:t==="bottom"?i-=s+e:i-=s/2,i}function Jr(n,t,e,i){const{caretSize:s,caretPadding:o,cornerRadius:r}=n,{xAlign:a,yAlign:l}=e,c=s+o,{topLeft:h,topRight:u,bottomLeft:d,bottomRight:f}=se(r);let g=pm(t,a);const m=ym(t,l,c);return l==="center"?a==="left"?g+=c:a==="right"&&(g-=c):a==="left"?g-=Math.max(h,d)+s:a==="right"&&(g+=Math.max(u,f)+s),{x:it(g,0,i.width-t.width),y:it(m,0,i.height-t.height)}}function li(n,t,e){const i=tt(e.padding);return t==="center"?n.x+n.width/2:t==="right"?n.x+n.width-i.right:n.x+i.left}function Kr(n){return It([],Wt(n))}function bm(n,t,e){return he(n,{tooltip:t,tooltipItems:e,type:"tooltip"})}function Qr(n,t){const e=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return e?n.override(e):n}const ec={beforeTitle:Nt,title(n){if(n.length>0){const t=n[0],e=t.chart.data.labels,i=e?e.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(i>0&&t.dataIndex<i)return e[t.dataIndex]}return""},afterTitle:Nt,beforeBody:Nt,beforeLabel:Nt,label(n){if(this&&this.options&&this.options.mode==="dataset")return n.label+": "+n.formattedValue||n.formattedValue;let t=n.dataset.label||"";t&&(t+=": ");const e=n.formattedValue;return I(e)||(t+=e),t},labelColor(n){const e=n.chart.getDatasetMeta(n.datasetIndex).controller.getStyle(n.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(n){const e=n.chart.getDatasetMeta(n.datasetIndex).controller.getStyle(n.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:Nt,afterBody:Nt,beforeFooter:Nt,footer:Nt,afterFooter:Nt};function dt(n,t,e,i){const s=n[t].call(e,i);return typeof s>"u"?ec[t].call(e,i):s}class zs extends st{constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const e=this.chart,i=this.options.setContext(this.getContext()),s=i.enabled&&e.options.animation&&i.animations,o=new So(this.chart,s);return s._cacheable&&(this._cachedAnimations=Object.freeze(o)),o}getContext(){return this.$context||(this.$context=bm(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,e){const{callbacks:i}=e,s=dt(i,"beforeTitle",this,t),o=dt(i,"title",this,t),r=dt(i,"afterTitle",this,t);let a=[];return a=It(a,Wt(s)),a=It(a,Wt(o)),a=It(a,Wt(r)),a}getBeforeBody(t,e){return Kr(dt(e.callbacks,"beforeBody",this,t))}getBody(t,e){const{callbacks:i}=e,s=[];return B(t,o=>{const r={before:[],lines:[],after:[]},a=Qr(i,o);It(r.before,Wt(dt(a,"beforeLabel",this,o))),It(r.lines,dt(a,"label",this,o)),It(r.after,Wt(dt(a,"afterLabel",this,o))),s.push(r)}),s}getAfterBody(t,e){return Kr(dt(e.callbacks,"afterBody",this,t))}getFooter(t,e){const{callbacks:i}=e,s=dt(i,"beforeFooter",this,t),o=dt(i,"footer",this,t),r=dt(i,"afterFooter",this,t);let a=[];return a=It(a,Wt(s)),a=It(a,Wt(o)),a=It(a,Wt(r)),a}_createItems(t){const e=this._active,i=this.chart.data,s=[],o=[],r=[];let a=[],l,c;for(l=0,c=e.length;l<c;++l)a.push(dm(this.chart,e[l]));return t.filter&&(a=a.filter((h,u,d)=>t.filter(h,u,d,i))),t.itemSort&&(a=a.sort((h,u)=>t.itemSort(h,u,i))),B(a,h=>{const u=Qr(t.callbacks,h);s.push(dt(u,"labelColor",this,h)),o.push(dt(u,"labelPointStyle",this,h)),r.push(dt(u,"labelTextColor",this,h))}),this.labelColors=s,this.labelPointStyles=o,this.labelTextColors=r,this.dataPoints=a,a}update(t,e){const i=this.options.setContext(this.getContext()),s=this._active;let o,r=[];if(!s.length)this.opacity!==0&&(o={opacity:0});else{const a=mn[i.position].call(this,s,this._eventPosition);r=this._createItems(i),this.title=this.getTitle(r,i),this.beforeBody=this.getBeforeBody(r,i),this.body=this.getBody(r,i),this.afterBody=this.getAfterBody(r,i),this.footer=this.getFooter(r,i);const l=this._size=qr(this,i),c=Object.assign({},a,l),h=Gr(this.chart,i,c),u=Jr(i,c,h,this.chart);this.xAlign=h.xAlign,this.yAlign=h.yAlign,o={opacity:1,x:u.x,y:u.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=r,this.$context=void 0,o&&this._resolveAnimations().update(this,o),t&&i.external&&i.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,i,s){const o=this.getCaretPosition(t,i,s);e.lineTo(o.x1,o.y1),e.lineTo(o.x2,o.y2),e.lineTo(o.x3,o.y3)}getCaretPosition(t,e,i){const{xAlign:s,yAlign:o}=this,{caretSize:r,cornerRadius:a}=i,{topLeft:l,topRight:c,bottomLeft:h,bottomRight:u}=se(a),{x:d,y:f}=t,{width:g,height:m}=e;let p,y,b,_,w,x;return o==="center"?(w=f+m/2,s==="left"?(p=d,y=p-r,_=w+r,x=w-r):(p=d+g,y=p+r,_=w-r,x=w+r),b=p):(s==="left"?y=d+Math.max(l,h)+r:s==="right"?y=d+g-Math.max(c,u)-r:y=this.caretX,o==="top"?(_=f,w=_-r,p=y-r,b=y+r):(_=f+m,w=_+r,p=y+r,b=y-r),x=_),{x1:p,x2:y,x3:b,y1:_,y2:w,y3:x}}drawTitle(t,e,i){const s=this.title,o=s.length;let r,a,l;if(o){const c=We(i.rtl,this.x,this.width);for(t.x=li(this,i.titleAlign,i),e.textAlign=c.textAlign(i.titleAlign),e.textBaseline="middle",r=G(i.titleFont),a=i.titleSpacing,e.fillStyle=i.titleColor,e.font=r.string,l=0;l<o;++l)e.fillText(s[l],c.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+a,l+1===o&&(t.y+=i.titleMarginBottom-a)}}_drawColorBox(t,e,i,s,o){const r=this.labelColors[i],a=this.labelPointStyles[i],{boxHeight:l,boxWidth:c}=o,h=G(o.bodyFont),u=li(this,"left",o),d=s.x(u),f=l<h.lineHeight?(h.lineHeight-l)/2:0,g=e.y+f;if(o.usePointStyle){const m={radius:Math.min(c,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},p=s.leftForLtr(d,c)+c/2,y=g+l/2;t.strokeStyle=o.multiKeyBackground,t.fillStyle=o.multiKeyBackground,As(t,m,p,y),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,As(t,m,p,y)}else{t.lineWidth=E(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;const m=s.leftForLtr(d,c),p=s.leftForLtr(s.xPlus(d,1),c-2),y=se(r.borderRadius);Object.values(y).some(b=>b!==0)?(t.beginPath(),t.fillStyle=o.multiKeyBackground,$e(t,{x:m,y:g,w:c,h:l,radius:y}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),$e(t,{x:p,y:g+1,w:c-2,h:l-2,radius:y}),t.fill()):(t.fillStyle=o.multiKeyBackground,t.fillRect(m,g,c,l),t.strokeRect(m,g,c,l),t.fillStyle=r.backgroundColor,t.fillRect(p,g+1,c-2,l-2))}t.fillStyle=this.labelTextColors[i]}drawBody(t,e,i){const{body:s}=this,{bodySpacing:o,bodyAlign:r,displayColors:a,boxHeight:l,boxWidth:c,boxPadding:h}=i,u=G(i.bodyFont);let d=u.lineHeight,f=0;const g=We(i.rtl,this.x,this.width),m=function(M){e.fillText(M,g.x(t.x+f),t.y+d/2),t.y+=d+o},p=g.textAlign(r);let y,b,_,w,x,k,S;for(e.textAlign=r,e.textBaseline="middle",e.font=u.string,t.x=li(this,p,i),e.fillStyle=i.bodyColor,B(this.beforeBody,m),f=a&&p!=="right"?r==="center"?c/2+h:c+2+h:0,w=0,k=s.length;w<k;++w){for(y=s[w],b=this.labelTextColors[w],e.fillStyle=b,B(y.before,m),_=y.lines,a&&_.length&&(this._drawColorBox(e,t,w,g,i),d=Math.max(u.lineHeight,l)),x=0,S=_.length;x<S;++x)m(_[x]),d=u.lineHeight;B(y.after,m)}f=0,d=u.lineHeight,B(this.afterBody,m),t.y-=o}drawFooter(t,e,i){const s=this.footer,o=s.length;let r,a;if(o){const l=We(i.rtl,this.x,this.width);for(t.x=li(this,i.footerAlign,i),t.y+=i.footerMarginTop,e.textAlign=l.textAlign(i.footerAlign),e.textBaseline="middle",r=G(i.footerFont),e.fillStyle=i.footerColor,e.font=r.string,a=0;a<o;++a)e.fillText(s[a],l.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+i.footerSpacing}}drawBackground(t,e,i,s){const{xAlign:o,yAlign:r}=this,{x:a,y:l}=t,{width:c,height:h}=i,{topLeft:u,topRight:d,bottomLeft:f,bottomRight:g}=se(s.cornerRadius);e.fillStyle=s.backgroundColor,e.strokeStyle=s.borderColor,e.lineWidth=s.borderWidth,e.beginPath(),e.moveTo(a+u,l),r==="top"&&this.drawCaret(t,e,i,s),e.lineTo(a+c-d,l),e.quadraticCurveTo(a+c,l,a+c,l+d),r==="center"&&o==="right"&&this.drawCaret(t,e,i,s),e.lineTo(a+c,l+h-g),e.quadraticCurveTo(a+c,l+h,a+c-g,l+h),r==="bottom"&&this.drawCaret(t,e,i,s),e.lineTo(a+f,l+h),e.quadraticCurveTo(a,l+h,a,l+h-f),r==="center"&&o==="left"&&this.drawCaret(t,e,i,s),e.lineTo(a,l+u),e.quadraticCurveTo(a,l,a+u,l),e.closePath(),e.fill(),s.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){const e=this.chart,i=this.$animations,s=i&&i.x,o=i&&i.y;if(s||o){const r=mn[t.position].call(this,this._active,this._eventPosition);if(!r)return;const a=this._size=qr(this,t),l=Object.assign({},r,this._size),c=Gr(e,t,l),h=Jr(t,l,c,e);(s._to!==h.x||o._to!==h.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=a.width,this.height=a.height,this.caretX=r.x,this.caretY=r.y,this._resolveAnimations().update(this,h))}}_willRender(){return!!this.opacity}draw(t){const e=this.options.setContext(this.getContext());let i=this.opacity;if(!i)return;this._updateAnimationTarget(e);const s={width:this.width,height:this.height},o={x:this.x,y:this.y};i=Math.abs(i)<.001?0:i;const r=tt(e.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&a&&(t.save(),t.globalAlpha=i,this.drawBackground(o,t,s,e),Dl(t,e.textDirection),o.y+=r.top,this.drawTitle(o,t,e),this.drawBody(o,t,e),this.drawFooter(o,t,e),Cl(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){const i=this._active,s=t.map(({datasetIndex:a,index:l})=>{const c=this.chart.getDatasetMeta(a);if(!c)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:c.data[l],index:l}}),o=!Di(i,s),r=this._positionChanged(s,e);(o||r)&&(this._active=s,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,i=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const s=this.options,o=this._active||[],r=this._getActiveElements(t,o,e,i),a=this._positionChanged(r,t),l=e||!Di(r,o)||a;return l&&(this._active=r,(s.enabled||s.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),l}_getActiveElements(t,e,i,s){const o=this.options;if(t.type==="mouseout")return[];if(!s)return e.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);const r=this.chart.getElementsAtEventForMode(t,o.mode,o,i);return o.reverse&&r.reverse(),r}_positionChanged(t,e){const{caretX:i,caretY:s,options:o}=this,r=mn[o.position].call(this,t,e);return r!==!1&&(i!==r.x||s!==r.y)}}v(zs,"positioners",mn);var xm={id:"tooltip",_element:zs,positioners:mn,afterInit(n,t,e){e&&(n.tooltip=new zs({chart:n,options:e}))},beforeUpdate(n,t,e){n.tooltip&&n.tooltip.initialize(e)},reset(n,t,e){n.tooltip&&n.tooltip.initialize(e)},afterDraw(n){const t=n.tooltip;if(t&&t._willRender()){const e={tooltip:t};if(n.notifyPlugins("beforeTooltipDraw",{...e,cancelable:!0})===!1)return;t.draw(n.ctx),n.notifyPlugins("afterTooltipDraw",e)}},afterEvent(n,t){if(n.tooltip){const e=t.replay;n.tooltip.handleEvent(t.event,e,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(n,t)=>t.bodyFont.size,boxWidth:(n,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:ec},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:n=>n!=="filter"&&n!=="itemSort"&&n!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},_m=Object.freeze({__proto__:null,Colors:Eg,Decimation:Fg,Filler:nm,Legend:lm,SubTitle:um,Title:hm,Tooltip:xm});const wm=(n,t,e,i)=>(typeof t=="string"?(e=n.push(t)-1,i.unshift({index:e,label:t})):isNaN(t)&&(e=null),e);function vm(n,t,e,i){const s=n.indexOf(t);if(s===-1)return wm(n,t,e,i);const o=n.lastIndexOf(t);return s!==o?e:s}const km=(n,t)=>n===null?null:it(Math.round(n),0,t);function ta(n){const t=this.getLabels();return n>=0&&n<t.length?t[n]:n}class Ws extends Te{constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const i=this.getLabels();for(const{index:s,label:o}of e)i[s]===o&&i.splice(s,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(I(t))return null;const i=this.getLabels();return e=isFinite(e)&&i[e]===t?e:vm(i,t,C(e,t),this._addedLabels),km(e,i.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:i,max:s}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(i=0),e||(s=this.getLabels().length-1)),this.min=i,this.max=s}buildTicks(){const t=this.min,e=this.max,i=this.options.offset,s=[];let o=this.getLabels();o=t===0&&e===o.length-1?o:o.slice(t,e+1),this._valueRange=Math.max(o.length-(i?0:1),1),this._startValue=this.min-(i?.5:0);for(let r=t;r<=e;r++)s.push({value:r});return s}getLabelForValue(t){return ta.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}v(Ws,"id","category"),v(Ws,"defaults",{ticks:{callback:ta}});function Sm(n,t){const e=[],{bounds:s,step:o,min:r,max:a,precision:l,count:c,maxTicks:h,maxDigits:u,includeBounds:d}=n,f=o||1,g=h-1,{min:m,max:p}=t,y=!I(r),b=!I(a),_=!I(c),w=(p-m)/(u+1);let x=qo((p-m)/g/f)*f,k,S,M,O;if(x<1e-14&&!y&&!b)return[{value:m},{value:p}];O=Math.ceil(p/x)-Math.floor(m/x),O>g&&(x=qo(O*x/g/f)*f),I(l)||(k=Math.pow(10,l),x=Math.ceil(x*k)/k),s==="ticks"?(S=Math.floor(m/x)*x,M=Math.ceil(p/x)*x):(S=m,M=p),y&&b&&o&&yu((a-r)/o,x/1e3)?(O=Math.round(Math.min((a-r)/x,h)),x=(a-r)/O,S=r,M=a):_?(S=y?r:S,M=b?a:M,O=c-1,x=(M-S)/O):(O=(M-S)/x,vn(O,Math.round(O),x/1e3)?O=Math.round(O):O=Math.ceil(O));const A=Math.max(Go(x),Go(S));k=Math.pow(10,I(l)?A:l),S=Math.round(S*k)/k,M=Math.round(M*k)/k;let L=0;for(y&&(d&&S!==r?(e.push({value:r}),S<r&&L++,vn(Math.round((S+L*x)*k)/k,r,ea(r,w,n))&&L++):S<r&&L++);L<O;++L){const F=Math.round((S+L*x)*k)/k;if(b&&F>a)break;e.push({value:F})}return b&&d&&M!==a?e.length&&vn(e[e.length-1].value,a,ea(a,w,n))?e[e.length-1].value=a:e.push({value:a}):(!b||M===a)&&e.push({value:M}),e}function ea(n,t,{horizontal:e,minRotation:i}){const s=nt(i),o=(e?Math.sin(s):Math.cos(s))||.001,r=.75*t*(""+n).length;return Math.min(t/o,r)}class Ni extends Te{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return I(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:e,maxDefined:i}=this.getUserBounds();let{min:s,max:o}=this;const r=l=>s=e?s:l,a=l=>o=i?o:l;if(t){const l=Rt(s),c=Rt(o);l<0&&c<0?a(0):l>0&&c>0&&r(0)}if(s===o){let l=o===0?1:Math.abs(o*.05);a(o+l),t||r(s-l)}this.min=s,this.max=o}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:e,stepSize:i}=t,s;return i?(s=Math.ceil(this.max/i)-Math.floor(this.min/i)+1,s>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${i} would result generating up to ${s} ticks. Limiting to 1000.`),s=1e3)):(s=this.computeTickLimit(),e=e||11),e&&(s=Math.min(e,s)),s}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,e=t.ticks;let i=this.getTickLimit();i=Math.max(2,i);const s={maxTicks:i,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:e.includeBounds!==!1},o=this._range||this,r=Sm(s,o);return t.bounds==="ticks"&&dl(r,this,"value"),t.reverse?(r.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),r}configure(){const t=this.ticks;let e=this.min,i=this.max;if(super.configure(),this.options.offset&&t.length){const s=(i-e)/Math.max(t.length-1,1)/2;e-=s,i+=s}this._startValue=e,this._endValue=i,this._valueRange=i-e}getLabelForValue(t){return zn(t,this.chart.options.locale,this.options.ticks.format)}}class Vs extends Ni{determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=q(t)?t:0,this.max=q(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),e=t?this.width:this.height,i=nt(this.options.ticks.minRotation),s=(t?Math.sin(i):Math.cos(i))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,o.lineHeight/s))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}v(Vs,"id","linear"),v(Vs,"defaults",{ticks:{callback:ji.formatters.numeric}});const Ln=n=>Math.floor(Qt(n)),me=(n,t)=>Math.pow(10,Ln(n)+t);function na(n){return n/Math.pow(10,Ln(n))===1}function ia(n,t,e){const i=Math.pow(10,e),s=Math.floor(n/i);return Math.ceil(t/i)-s}function Mm(n,t){const e=t-n;let i=Ln(e);for(;ia(n,t,i)>10;)i++;for(;ia(n,t,i)<10;)i--;return Math.min(i,Ln(n))}function Tm(n,{min:t,max:e}){t=bt(n.min,t);const i=[],s=Ln(t);let o=Mm(t,e),r=o<0?Math.pow(10,Math.abs(o)):1;const a=Math.pow(10,o),l=s>o?Math.pow(10,s):0,c=Math.round((t-l)*r)/r,h=Math.floor((t-l)/a/10)*a*10;let u=Math.floor((c-h)/Math.pow(10,o)),d=bt(n.min,Math.round((l+h+u*Math.pow(10,o))*r)/r);for(;d<e;)i.push({value:d,major:na(d),significand:u}),u>=10?u=u<15?15:20:u++,u>=20&&(o++,u=2,r=o>=0?1:r),d=Math.round((l+h+u*Math.pow(10,o))*r)/r;const f=bt(n.max,d);return i.push({value:f,major:na(f),significand:u}),i}class Bs extends Te{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){const i=Ni.prototype.parse.apply(this,[t,e]);if(i===0){this._zero=!0;return}return q(i)&&i>0?i:null}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=q(t)?Math.max(0,t):null,this.max=q(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!q(this._userMin)&&(this.min=t===me(this.min,0)?me(this.min,-1):me(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let i=this.min,s=this.max;const o=a=>i=t?i:a,r=a=>s=e?s:a;i===s&&(i<=0?(o(1),r(10)):(o(me(i,-1)),r(me(s,1)))),i<=0&&o(me(s,-1)),s<=0&&r(me(i,1)),this.min=i,this.max=s}buildTicks(){const t=this.options,e={min:this._userMin,max:this._userMax},i=Tm(e,this);return t.bounds==="ticks"&&dl(i,this,"value"),t.reverse?(i.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),i}getLabelForValue(t){return t===void 0?"0":zn(t,this.chart.options.locale,this.options.ticks.format)}configure(){const t=this.min;super.configure(),this._startValue=Qt(t),this._valueRange=Qt(this.max)-Qt(t)}getPixelForValue(t){return(t===void 0||t===0)&&(t=this.min),t===null||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(Qt(t)-this._startValue)/this._valueRange)}getValueForPixel(t){const e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}v(Bs,"id","logarithmic"),v(Bs,"defaults",{ticks:{callback:ji.formatters.logarithmic,major:{enabled:!0}}});function Hs(n){const t=n.ticks;if(t.display&&n.display){const e=tt(t.backdropPadding);return C(t.font&&t.font.size,Y.font.size)+e.height}return 0}function Om(n,t,e){return e=V(e)?e:[e],{w:Lu(n,t.string,e),h:e.length*t.lineHeight}}function sa(n,t,e,i,s){return n===i||n===s?{start:t-e/2,end:t+e/2}:n<i||n>s?{start:t-e,end:t}:{start:t,end:t+e}}function Dm(n){const t={l:n.left+n._padding.left,r:n.right-n._padding.right,t:n.top+n._padding.top,b:n.bottom-n._padding.bottom},e=Object.assign({},t),i=[],s=[],o=n._pointLabels.length,r=n.options.pointLabels,a=r.centerPointLabels?R/o:0;for(let l=0;l<o;l++){const c=r.setContext(n.getPointLabelContext(l));s[l]=c.padding;const h=n.getPointPosition(l,n.drawingArea+s[l],a),u=G(c.font),d=Om(n.ctx,u,n._pointLabels[l]);i[l]=d;const f=xt(n.getIndexAngle(l)+a),g=Math.round($i(f)),m=sa(g,h.x,d.w,0,180),p=sa(g,h.y,d.h,90,270);Cm(e,t,f,m,p)}n.setCenterPoint(t.l-e.l,e.r-t.r,t.t-e.t,e.b-t.b),n._pointLabelItems=Em(n,i,s)}function Cm(n,t,e,i,s){const o=Math.abs(Math.sin(e)),r=Math.abs(Math.cos(e));let a=0,l=0;i.start<t.l?(a=(t.l-i.start)/o,n.l=Math.min(n.l,t.l-a)):i.end>t.r&&(a=(i.end-t.r)/o,n.r=Math.max(n.r,t.r+a)),s.start<t.t?(l=(t.t-s.start)/r,n.t=Math.min(n.t,t.t-l)):s.end>t.b&&(l=(s.end-t.b)/r,n.b=Math.max(n.b,t.b+l))}function Pm(n,t,e){const i=n.drawingArea,{extra:s,additionalAngle:o,padding:r,size:a}=e,l=n.getPointPosition(t,i+s+r,o),c=Math.round($i(xt(l.angle+X))),h=Rm(l.y,a.h,c),u=Im(c),d=Lm(l.x,a.w,u);return{visible:!0,x:l.x,y:h,textAlign:u,left:d,top:h,right:d+a.w,bottom:h+a.h}}function Am(n,t){if(!t)return!0;const{left:e,top:i,right:s,bottom:o}=n;return!($t({x:e,y:i},t)||$t({x:e,y:o},t)||$t({x:s,y:i},t)||$t({x:s,y:o},t))}function Em(n,t,e){const i=[],s=n._pointLabels.length,o=n.options,{centerPointLabels:r,display:a}=o.pointLabels,l={extra:Hs(o)/2,additionalAngle:r?R/s:0};let c;for(let h=0;h<s;h++){l.padding=e[h],l.size=t[h];const u=Pm(n,h,l);i.push(u),a==="auto"&&(u.visible=Am(u,c),u.visible&&(c=u))}return i}function Im(n){return n===0||n===180?"center":n<180?"left":"right"}function Lm(n,t,e){return e==="right"?n-=t:e==="center"&&(n-=t/2),n}function Rm(n,t,e){return e===90||e===270?n-=t/2:(e>270||e<90)&&(n-=t),n}function Fm(n,t,e){const{left:i,top:s,right:o,bottom:r}=e,{backdropColor:a}=t;if(!I(a)){const l=se(t.borderRadius),c=tt(t.backdropPadding);n.fillStyle=a;const h=i-c.left,u=s-c.top,d=o-i+c.width,f=r-s+c.height;Object.values(l).some(g=>g!==0)?(n.beginPath(),$e(n,{x:h,y:u,w:d,h:f,radius:l}),n.fill()):n.fillRect(h,u,d,f)}}function Nm(n,t){const{ctx:e,options:{pointLabels:i}}=n;for(let s=t-1;s>=0;s--){const o=n._pointLabelItems[s];if(!o.visible)continue;const r=i.setContext(n.getPointLabelContext(s));Fm(e,r,o);const a=G(r.font),{x:l,y:c,textAlign:h}=o;Se(e,n._pointLabels[s],l,c+a.lineHeight/2,a,{color:r.color,textAlign:h,textBaseline:"middle"})}}function nc(n,t,e,i){const{ctx:s}=n;if(e)s.arc(n.xCenter,n.yCenter,t,0,j);else{let o=n.getPointPosition(0,t);s.moveTo(o.x,o.y);for(let r=1;r<i;r++)o=n.getPointPosition(r,t),s.lineTo(o.x,o.y)}}function zm(n,t,e,i,s){const o=n.ctx,r=t.circular,{color:a,lineWidth:l}=t;!r&&!i||!a||!l||e<0||(o.save(),o.strokeStyle=a,o.lineWidth=l,o.setLineDash(s.dash||[]),o.lineDashOffset=s.dashOffset,o.beginPath(),nc(n,e,r,i),o.closePath(),o.stroke(),o.restore())}function Wm(n,t,e){return he(n,{label:e,index:t,type:"pointLabel"})}class pn extends Ni{constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const t=this._padding=tt(Hs(this.options)/2),e=this.width=this.maxWidth-t.width,i=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+i/2+t.top),this.drawingArea=Math.floor(Math.min(e,i)/2)}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!1);this.min=q(t)&&!isNaN(t)?t:0,this.max=q(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/Hs(this.options))}generateTickLabels(t){Ni.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((e,i)=>{const s=W(this.options.pointLabels.callback,[e,i],this);return s||s===0?s:""}).filter((e,i)=>this.chart.getDataVisibility(i))}fit(){const t=this.options;t.display&&t.pointLabels.display?Dm(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,i,s){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((i-s)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,i,s))}getIndexAngle(t){const e=j/(this._pointLabels.length||1),i=this.options.startAngle||0;return xt(t*e+nt(i))}getDistanceFromCenterForValue(t){if(I(t))return NaN;const e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(I(t))return NaN;const e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){const e=this._pointLabels||[];if(t>=0&&t<e.length){const i=e[t];return Wm(this.getContext(),t,i)}}getPointPosition(t,e,i=0){const s=this.getIndexAngle(t)-X+i;return{x:Math.cos(s)*e+this.xCenter,y:Math.sin(s)*e+this.yCenter,angle:s}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){const{left:e,top:i,right:s,bottom:o}=this._pointLabelItems[t];return{left:e,top:i,right:s,bottom:o}}drawBackground(){const{backgroundColor:t,grid:{circular:e}}=this.options;if(t){const i=this.ctx;i.save(),i.beginPath(),nc(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),i.closePath(),i.fillStyle=t,i.fill(),i.restore()}}drawGrid(){const t=this.ctx,e=this.options,{angleLines:i,grid:s,border:o}=e,r=this._pointLabels.length;let a,l,c;if(e.pointLabels.display&&Nm(this,r),s.display&&this.ticks.forEach((h,u)=>{if(u!==0||u===0&&this.min<0){l=this.getDistanceFromCenterForValue(h.value);const d=this.getContext(u),f=s.setContext(d),g=o.setContext(d);zm(this,f,l,r,g)}}),i.display){for(t.save(),a=r-1;a>=0;a--){const h=i.setContext(this.getPointLabelContext(a)),{color:u,lineWidth:d}=h;!d||!u||(t.lineWidth=d,t.strokeStyle=u,t.setLineDash(h.borderDash),t.lineDashOffset=h.borderDashOffset,l=this.getDistanceFromCenterForValue(e.reverse?this.min:this.max),c=this.getPointPosition(a,l),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(c.x,c.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){const t=this.ctx,e=this.options,i=e.ticks;if(!i.display)return;const s=this.getIndexAngle(0);let o,r;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(s),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach((a,l)=>{if(l===0&&this.min>=0&&!e.reverse)return;const c=i.setContext(this.getContext(l)),h=G(c.font);if(o=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){t.font=h.string,r=t.measureText(a.label).width,t.fillStyle=c.backdropColor;const u=tt(c.backdropPadding);t.fillRect(-r/2-u.left,-o-h.size/2-u.top,r+u.width,h.size+u.height)}Se(t,a.label,0,-o,h,{color:c.color,strokeColor:c.textStrokeColor,strokeWidth:c.textStrokeWidth})}),t.restore()}drawTitle(){}}v(pn,"id","radialLinear"),v(pn,"defaults",{display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:ji.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(t){return t},padding:5,centerPointLabels:!1}}),v(pn,"defaultRoutes",{"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"}),v(pn,"descriptors",{angleLines:{_fallback:"grid"}});const Ui={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},gt=Object.keys(Ui);function oa(n,t){return n-t}function ra(n,t){if(I(t))return null;const e=n._adapter,{parser:i,round:s,isoWeekday:o}=n._parseOpts;let r=t;return typeof i=="function"&&(r=i(r)),q(r)||(r=typeof i=="string"?e.parse(r,i):e.parse(r)),r===null?null:(s&&(r=s==="week"&&(le(o)||o===!0)?e.startOf(r,"isoWeek",o):e.startOf(r,s)),+r)}function aa(n,t,e,i){const s=gt.length;for(let o=gt.indexOf(n);o<s-1;++o){const r=Ui[gt[o]],a=r.steps?r.steps:Number.MAX_SAFE_INTEGER;if(r.common&&Math.ceil((e-t)/(a*r.size))<=i)return gt[o]}return gt[s-1]}function Vm(n,t,e,i,s){for(let o=gt.length-1;o>=gt.indexOf(e);o--){const r=gt[o];if(Ui[r].common&&n._adapter.diff(s,i,r)>=t-1)return r}return gt[e?gt.indexOf(e):0]}function Bm(n){for(let t=gt.indexOf(n)+1,e=gt.length;t<e;++t)if(Ui[gt[t]].common)return gt[t]}function la(n,t,e){if(!e)n[t]=!0;else if(e.length){const{lo:i,hi:s}=mo(e,t),o=e[i]>=t?e[i]:e[s];n[o]=!0}}function Hm(n,t,e,i){const s=n._adapter,o=+s.startOf(t[0].value,i),r=t[t.length-1].value;let a,l;for(a=o;a<=r;a=+s.add(a,1,i))l=e[a],l>=0&&(t[l].major=!0);return t}function ca(n,t,e){const i=[],s={},o=t.length;let r,a;for(r=0;r<o;++r)a=t[r],s[a]=r,i.push({value:a,major:!1});return o===0||!e?i:Hm(n,i,s,e)}class Rn extends Te{constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){const i=t.time||(t.time={}),s=this._adapter=new Fl._date(t.adapters.date);s.init(e),wn(i.displayFormats,s.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return t===void 0?null:ra(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,i=t.time.unit||"day";let{min:s,max:o,minDefined:r,maxDefined:a}=this.getUserBounds();function l(c){!r&&!isNaN(c.min)&&(s=Math.min(s,c.min)),!a&&!isNaN(c.max)&&(o=Math.max(o,c.max))}(!r||!a)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),s=q(s)&&!isNaN(s)?s:+e.startOf(Date.now(),i),o=q(o)&&!isNaN(o)?o:+e.endOf(Date.now(),i)+1,this.min=Math.min(s,o-1),this.max=Math.max(s+1,o)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],i=t[t.length-1]),{min:e,max:i}}buildTicks(){const t=this.options,e=t.time,i=t.ticks,s=i.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&s.length&&(this.min=this._userMin||s[0],this.max=this._userMax||s[s.length-1]);const o=this.min,r=this.max,a=wu(s,o,r);return this._unit=e.unit||(i.autoSkip?aa(e.minUnit,this.min,this.max,this._getLabelCapacity(o)):Vm(this,a.length,e.minUnit,this.min,this.max)),this._majorUnit=!i.major.enabled||this._unit==="year"?void 0:Bm(this._unit),this.initOffsets(s),t.reverse&&a.reverse(),ca(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e=0,i=0,s,o;this.options.offset&&t.length&&(s=this.getDecimalForValue(t[0]),t.length===1?e=1-s:e=(this.getDecimalForValue(t[1])-s)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?i=o:i=(o-this.getDecimalForValue(t[t.length-2]))/2);const r=t.length<3?.5:.25;e=it(e,0,r),i=it(i,0,r),this._offsets={start:e,end:i,factor:1/(e+1+i)}}_generate(){const t=this._adapter,e=this.min,i=this.max,s=this.options,o=s.time,r=o.unit||aa(o.minUnit,e,i,this._getLabelCapacity(e)),a=C(s.ticks.stepSize,1),l=r==="week"?o.isoWeekday:!1,c=le(l)||l===!0,h={};let u=e,d,f;if(c&&(u=+t.startOf(u,"isoWeek",l)),u=+t.startOf(u,c?"day":r),t.diff(i,e,r)>1e5*a)throw new Error(e+" and "+i+" are too far apart with stepSize of "+a+" "+r);const g=s.ticks.source==="data"&&this.getDataTimestamps();for(d=u,f=0;d<i;d=+t.add(d,a,r),f++)la(h,d,g);return(d===i||s.bounds==="ticks"||f===1)&&la(h,d,g),Object.keys(h).sort(oa).map(m=>+m)}getLabelForValue(t){const e=this._adapter,i=this.options.time;return i.tooltipFormat?e.format(t,i.tooltipFormat):e.format(t,i.displayFormats.datetime)}format(t,e){const s=this.options.time.displayFormats,o=this._unit,r=e||s[o];return this._adapter.format(t,r)}_tickFormatFunction(t,e,i,s){const o=this.options,r=o.ticks.callback;if(r)return W(r,[t,e,i],this);const a=o.time.displayFormats,l=this._unit,c=this._majorUnit,h=l&&a[l],u=c&&a[c],d=i[e],f=c&&u&&d&&d.major;return this._adapter.format(t,s||(f?u:h))}generateTickLabels(t){let e,i,s;for(e=0,i=t.length;e<i;++e)s=t[e],s.label=this._tickFormatFunction(s.value,e,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,i=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+i)*e.factor)}getValueForPixel(t){const e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+i*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,i=this.ctx.measureText(t).width,s=nt(this.isHorizontal()?e.maxRotation:e.minRotation),o=Math.cos(s),r=Math.sin(s),a=this._resolveTickFontOptions(0).size;return{w:i*o+a*r,h:i*r+a*o}}_getLabelCapacity(t){const e=this.options.time,i=e.displayFormats,s=i[e.unit]||i.millisecond,o=this._tickFormatFunction(t,0,ca(this,[t],this._majorUnit),s),r=this._getLabelSize(o),a=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[],e,i;if(t.length)return t;const s=this.getMatchingVisibleMetas();if(this._normalized&&s.length)return this._cache.data=s[0].controller.getAllParsedValues(this);for(e=0,i=s.length;e<i;++e)t=t.concat(s[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let e,i;if(t.length)return t;const s=this.getLabels();for(e=0,i=s.length;e<i;++e)t.push(ra(this,s[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return gl(t.sort(oa))}}v(Rn,"id","time"),v(Rn,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function ci(n,t,e){let i=0,s=n.length-1,o,r,a,l;e?(t>=n[i].pos&&t<=n[s].pos&&({lo:i,hi:s}=Ht(n,"pos",t)),{pos:o,time:a}=n[i],{pos:r,time:l}=n[s]):(t>=n[i].time&&t<=n[s].time&&({lo:i,hi:s}=Ht(n,"time",t)),{time:o,pos:a}=n[i],{time:r,pos:l}=n[s]);const c=r-o;return c?a+(l-a)*(t-o)/c:a}class $s extends Rn{constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=ci(e,this.min),this._tableRange=ci(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:e,max:i}=this,s=[],o=[];let r,a,l,c,h;for(r=0,a=t.length;r<a;++r)c=t[r],c>=e&&c<=i&&s.push(c);if(s.length<2)return[{time:e,pos:0},{time:i,pos:1}];for(r=0,a=s.length;r<a;++r)h=s[r+1],l=s[r-1],c=s[r],Math.round((h+l)/2)!==c&&o.push({time:c,pos:r/(a-1)});return o}_generate(){const t=this.min,e=this.max;let i=super.getDataTimestamps();return(!i.includes(t)||!i.length)&&i.splice(0,0,t),(!i.includes(e)||i.length===1)&&i.push(e),i.sort((s,o)=>s-o)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const e=this.getDataTimestamps(),i=this.getLabelTimestamps();return e.length&&i.length?t=this.normalize(e.concat(i)):t=e.length?e:i,t=this._cache.all=t,t}getDecimalForValue(t){return(ci(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return ci(this._table,i*this._tableRange+this._minPos,!0)}}v($s,"id","timeseries"),v($s,"defaults",Rn.defaults);var $m=Object.freeze({__proto__:null,CategoryScale:Ws,LinearScale:Vs,LogarithmicScale:Bs,RadialLinearScale:pn,TimeScale:Rn,TimeSeriesScale:$s});const jm=[Gd,Mg,_m,$m];St.register(...jm);class Oe extends Error{}class Ym extends Oe{constructor(t){super(`Invalid DateTime: ${t.toMessage()}`)}}class Um extends Oe{constructor(t){super(`Invalid Interval: ${t.toMessage()}`)}}class Xm extends Oe{constructor(t){super(`Invalid Duration: ${t.toMessage()}`)}}class ze extends Oe{}class ic extends Oe{constructor(t){super(`Invalid unit ${t}`)}}class lt extends Oe{}class Xt extends Oe{constructor(){super("Zone is an abstract class")}}const T="numeric",Et="short",_t="long",zi={year:T,month:T,day:T},sc={year:T,month:Et,day:T},Zm={year:T,month:Et,day:T,weekday:Et},oc={year:T,month:_t,day:T},rc={year:T,month:_t,day:T,weekday:_t},ac={hour:T,minute:T},lc={hour:T,minute:T,second:T},cc={hour:T,minute:T,second:T,timeZoneName:Et},hc={hour:T,minute:T,second:T,timeZoneName:_t},uc={hour:T,minute:T,hourCycle:"h23"},dc={hour:T,minute:T,second:T,hourCycle:"h23"},fc={hour:T,minute:T,second:T,hourCycle:"h23",timeZoneName:Et},gc={hour:T,minute:T,second:T,hourCycle:"h23",timeZoneName:_t},mc={year:T,month:T,day:T,hour:T,minute:T},pc={year:T,month:T,day:T,hour:T,minute:T,second:T},yc={year:T,month:Et,day:T,hour:T,minute:T},bc={year:T,month:Et,day:T,hour:T,minute:T,second:T},qm={year:T,month:Et,day:T,weekday:Et,hour:T,minute:T},xc={year:T,month:_t,day:T,hour:T,minute:T,timeZoneName:Et},_c={year:T,month:_t,day:T,hour:T,minute:T,second:T,timeZoneName:Et},wc={year:T,month:_t,day:T,weekday:_t,hour:T,minute:T,timeZoneName:_t},vc={year:T,month:_t,day:T,weekday:_t,hour:T,minute:T,second:T,timeZoneName:_t};class Hn{get type(){throw new Xt}get name(){throw new Xt}get ianaName(){return this.name}get isUniversal(){throw new Xt}offsetName(t,e){throw new Xt}formatOffset(t,e){throw new Xt}offset(t){throw new Xt}equals(t){throw new Xt}get isValid(){throw new Xt}}let bs=null;class Xi extends Hn{static get instance(){return bs===null&&(bs=new Xi),bs}get type(){return"system"}get name(){return new Intl.DateTimeFormat().resolvedOptions().timeZone}get isUniversal(){return!1}offsetName(t,{format:e,locale:i}){return Ic(t,e,i)}formatOffset(t,e){return Dn(this.offset(t),e)}offset(t){return-new Date(t).getTimezoneOffset()}equals(t){return t.type==="system"}get isValid(){return!0}}const js=new Map;function Gm(n){let t=js.get(n);return t===void 0&&(t=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:n,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",era:"short"}),js.set(n,t)),t}const Jm={year:0,month:1,day:2,era:3,hour:4,minute:5,second:6};function Km(n,t){const e=n.format(t).replace(/\u200E/g,""),i=/(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(e),[,s,o,r,a,l,c,h]=i;return[r,s,o,a,l,c,h]}function Qm(n,t){const e=n.formatToParts(t),i=[];for(let s=0;s<e.length;s++){const{type:o,value:r}=e[s],a=Jm[o];o==="era"?i[a]=r:P(a)||(i[a]=parseInt(r,10))}return i}const xs=new Map;class Yt extends Hn{static create(t){let e=xs.get(t);return e===void 0&&xs.set(t,e=new Yt(t)),e}static resetCache(){xs.clear(),js.clear()}static isValidSpecifier(t){return this.isValidZone(t)}static isValidZone(t){if(!t)return!1;try{return new Intl.DateTimeFormat("en-US",{timeZone:t}).format(),!0}catch{return!1}}constructor(t){super(),this.zoneName=t,this.valid=Yt.isValidZone(t)}get type(){return"iana"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(t,{format:e,locale:i}){return Ic(t,e,i,this.name)}formatOffset(t,e){return Dn(this.offset(t),e)}offset(t){if(!this.valid)return NaN;const e=new Date(t);if(isNaN(e))return NaN;const i=Gm(this.name);let[s,o,r,a,l,c,h]=i.formatToParts?Qm(i,e):Km(i,e);a==="BC"&&(s=-Math.abs(s)+1);const d=qi({year:s,month:o,day:r,hour:l===24?0:l,minute:c,second:h,millisecond:0});let f=+e;const g=f%1e3;return f-=g>=0?g:1e3+g,(d-f)/(60*1e3)}equals(t){return t.type==="iana"&&t.name===this.name}get isValid(){return this.valid}}let ha={};function tp(n,t={}){const e=JSON.stringify([n,t]);let i=ha[e];return i||(i=new Intl.ListFormat(n,t),ha[e]=i),i}const Ys=new Map;function Us(n,t={}){const e=JSON.stringify([n,t]);let i=Ys.get(e);return i===void 0&&(i=new Intl.DateTimeFormat(n,t),Ys.set(e,i)),i}const Xs=new Map;function ep(n,t={}){const e=JSON.stringify([n,t]);let i=Xs.get(e);return i===void 0&&(i=new Intl.NumberFormat(n,t),Xs.set(e,i)),i}const Zs=new Map;function np(n,t={}){const{base:e,...i}=t,s=JSON.stringify([n,i]);let o=Zs.get(s);return o===void 0&&(o=new Intl.RelativeTimeFormat(n,t),Zs.set(s,o)),o}let yn=null;function ip(){return yn||(yn=new Intl.DateTimeFormat().resolvedOptions().locale,yn)}const qs=new Map;function kc(n){let t=qs.get(n);return t===void 0&&(t=new Intl.DateTimeFormat(n).resolvedOptions(),qs.set(n,t)),t}const Gs=new Map;function sp(n){let t=Gs.get(n);if(!t){const e=new Intl.Locale(n);t="getWeekInfo"in e?e.getWeekInfo():e.weekInfo,"minimalDays"in t||(t={...Sc,...t}),Gs.set(n,t)}return t}function op(n){const t=n.indexOf("-x-");t!==-1&&(n=n.substring(0,t));const e=n.indexOf("-u-");if(e===-1)return[n];{let i,s;try{i=Us(n).resolvedOptions(),s=n}catch{const l=n.substring(0,e);i=Us(l).resolvedOptions(),s=l}const{numberingSystem:o,calendar:r}=i;return[s,o,r]}}function rp(n,t,e){return(e||t)&&(n.includes("-u-")||(n+="-u"),e&&(n+=`-ca-${e}`),t&&(n+=`-nu-${t}`)),n}function ap(n){const t=[];for(let e=1;e<=12;e++){const i=D.utc(2009,e,1);t.push(n(i))}return t}function lp(n){const t=[];for(let e=1;e<=7;e++){const i=D.utc(2016,11,13+e);t.push(n(i))}return t}function hi(n,t,e,i){const s=n.listingMode();return s==="error"?null:s==="en"?e(t):i(t)}function cp(n){return n.numberingSystem&&n.numberingSystem!=="latn"?!1:n.numberingSystem==="latn"||!n.locale||n.locale.startsWith("en")||kc(n.locale).numberingSystem==="latn"}class hp{constructor(t,e,i){this.padTo=i.padTo||0,this.floor=i.floor||!1;const{padTo:s,floor:o,...r}=i;if(!e||Object.keys(r).length>0){const a={useGrouping:!1,...i};i.padTo>0&&(a.minimumIntegerDigits=i.padTo),this.inf=ep(t,a)}}format(t){if(this.inf){const e=this.floor?Math.floor(t):t;return this.inf.format(e)}else{const e=this.floor?Math.floor(t):Eo(t,3);return et(e,this.padTo)}}}class up{constructor(t,e,i){this.opts=i,this.originalZone=void 0;let s;if(this.opts.timeZone)this.dt=t;else if(t.zone.type==="fixed"){const r=-1*(t.offset/60),a=r>=0?`Etc/GMT+${r}`:`Etc/GMT${r}`;t.offset!==0&&Yt.create(a).valid?(s=a,this.dt=t):(s="UTC",this.dt=t.offset===0?t:t.setZone("UTC").plus({minutes:t.offset}),this.originalZone=t.zone)}else t.zone.type==="system"?this.dt=t:t.zone.type==="iana"?(this.dt=t,s=t.zone.name):(s="UTC",this.dt=t.setZone("UTC").plus({minutes:t.offset}),this.originalZone=t.zone);const o={...this.opts};o.timeZone=o.timeZone||s,this.dtf=Us(e,o)}format(){return this.originalZone?this.formatToParts().map(({value:t})=>t).join(""):this.dtf.format(this.dt.toJSDate())}formatToParts(){const t=this.dtf.formatToParts(this.dt.toJSDate());return this.originalZone?t.map(e=>{if(e.type==="timeZoneName"){const i=this.originalZone.offsetName(this.dt.ts,{locale:this.dt.locale,format:this.opts.timeZoneName});return{...e,value:i}}else return e}):t}resolvedOptions(){return this.dtf.resolvedOptions()}}class dp{constructor(t,e,i){this.opts={style:"long",...i},!e&&Ac()&&(this.rtf=np(t,i))}format(t,e){return this.rtf?this.rtf.format(t,e):Lp(e,t,this.opts.numeric,this.opts.style!=="long")}formatToParts(t,e){return this.rtf?this.rtf.formatToParts(t,e):[]}}const Sc={firstDay:1,minimalDays:4,weekend:[6,7]};class H{static fromOpts(t){return H.create(t.locale,t.numberingSystem,t.outputCalendar,t.weekSettings,t.defaultToEN)}static create(t,e,i,s,o=!1){const r=t||K.defaultLocale,a=r||(o?"en-US":ip()),l=e||K.defaultNumberingSystem,c=i||K.defaultOutputCalendar,h=Ks(s)||K.defaultWeekSettings;return new H(a,l,c,h,r)}static resetCache(){yn=null,Ys.clear(),Xs.clear(),Zs.clear(),qs.clear(),Gs.clear()}static fromObject({locale:t,numberingSystem:e,outputCalendar:i,weekSettings:s}={}){return H.create(t,e,i,s)}constructor(t,e,i,s,o){const[r,a,l]=op(t);this.locale=r,this.numberingSystem=e||a||null,this.outputCalendar=i||l||null,this.weekSettings=s,this.intl=rp(this.locale,this.numberingSystem,this.outputCalendar),this.weekdaysCache={format:{},standalone:{}},this.monthsCache={format:{},standalone:{}},this.meridiemCache=null,this.eraCache={},this.specifiedLocale=o,this.fastNumbersCached=null}get fastNumbers(){return this.fastNumbersCached==null&&(this.fastNumbersCached=cp(this)),this.fastNumbersCached}listingMode(){const t=this.isEnglish(),e=(this.numberingSystem===null||this.numberingSystem==="latn")&&(this.outputCalendar===null||this.outputCalendar==="gregory");return t&&e?"en":"intl"}clone(t){return!t||Object.getOwnPropertyNames(t).length===0?this:H.create(t.locale||this.specifiedLocale,t.numberingSystem||this.numberingSystem,t.outputCalendar||this.outputCalendar,Ks(t.weekSettings)||this.weekSettings,t.defaultToEN||!1)}redefaultToEN(t={}){return this.clone({...t,defaultToEN:!0})}redefaultToSystem(t={}){return this.clone({...t,defaultToEN:!1})}months(t,e=!1){return hi(this,t,Fc,()=>{const i=e?{month:t,day:"numeric"}:{month:t},s=e?"format":"standalone";return this.monthsCache[s][t]||(this.monthsCache[s][t]=ap(o=>this.extract(o,i,"month"))),this.monthsCache[s][t]})}weekdays(t,e=!1){return hi(this,t,Wc,()=>{const i=e?{weekday:t,year:"numeric",month:"long",day:"numeric"}:{weekday:t},s=e?"format":"standalone";return this.weekdaysCache[s][t]||(this.weekdaysCache[s][t]=lp(o=>this.extract(o,i,"weekday"))),this.weekdaysCache[s][t]})}meridiems(){return hi(this,void 0,()=>Vc,()=>{if(!this.meridiemCache){const t={hour:"numeric",hourCycle:"h12"};this.meridiemCache=[D.utc(2016,11,13,9),D.utc(2016,11,13,19)].map(e=>this.extract(e,t,"dayperiod"))}return this.meridiemCache})}eras(t){return hi(this,t,Bc,()=>{const e={era:t};return this.eraCache[t]||(this.eraCache[t]=[D.utc(-40,1,1),D.utc(2017,1,1)].map(i=>this.extract(i,e,"era"))),this.eraCache[t]})}extract(t,e,i){const s=this.dtFormatter(t,e),o=s.formatToParts(),r=o.find(a=>a.type.toLowerCase()===i);return r?r.value:null}numberFormatter(t={}){return new hp(this.intl,t.forceSimple||this.fastNumbers,t)}dtFormatter(t,e={}){return new up(t,this.intl,e)}relFormatter(t={}){return new dp(this.intl,this.isEnglish(),t)}listFormatter(t={}){return tp(this.intl,t)}isEnglish(){return this.locale==="en"||this.locale.toLowerCase()==="en-us"||kc(this.intl).locale.startsWith("en-us")}getWeekSettings(){return this.weekSettings?this.weekSettings:Ec()?sp(this.locale):Sc}getStartOfWeek(){return this.getWeekSettings().firstDay}getMinDaysInFirstWeek(){return this.getWeekSettings().minimalDays}getWeekendDays(){return this.getWeekSettings().weekend}equals(t){return this.locale===t.locale&&this.numberingSystem===t.numberingSystem&&this.outputCalendar===t.outputCalendar}toString(){return`Locale(${this.locale}, ${this.numberingSystem}, ${this.outputCalendar})`}}let _s=null;class ut extends Hn{static get utcInstance(){return _s===null&&(_s=new ut(0)),_s}static instance(t){return t===0?ut.utcInstance:new ut(t)}static parseSpecifier(t){if(t){const e=t.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);if(e)return new ut(Gi(e[1],e[2]))}return null}constructor(t){super(),this.fixed=t}get type(){return"fixed"}get name(){return this.fixed===0?"UTC":`UTC${Dn(this.fixed,"narrow")}`}get ianaName(){return this.fixed===0?"Etc/UTC":`Etc/GMT${Dn(-this.fixed,"narrow")}`}offsetName(){return this.name}formatOffset(t,e){return Dn(this.fixed,e)}get isUniversal(){return!0}offset(){return this.fixed}equals(t){return t.type==="fixed"&&t.fixed===this.fixed}get isValid(){return!0}}class fp extends Hn{constructor(t){super(),this.zoneName=t}get type(){return"invalid"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(){return null}formatOffset(){return""}offset(){return NaN}equals(){return!1}get isValid(){return!1}}function Jt(n,t){if(P(n)||n===null)return t;if(n instanceof Hn)return n;if(xp(n)){const e=n.toLowerCase();return e==="default"?t:e==="local"||e==="system"?Xi.instance:e==="utc"||e==="gmt"?ut.utcInstance:ut.parseSpecifier(e)||Yt.create(n)}else return oe(n)?ut.instance(n):typeof n=="object"&&"offset"in n&&typeof n.offset=="function"?n:new fp(n)}const Do={arab:"[٠-٩]",arabext:"[۰-۹]",bali:"[᭐-᭙]",beng:"[০-৯]",deva:"[०-९]",fullwide:"[０-９]",gujr:"[૦-૯]",hanidec:"[〇|一|二|三|四|五|六|七|八|九]",khmr:"[០-៩]",knda:"[೦-೯]",laoo:"[໐-໙]",limb:"[᥆-᥏]",mlym:"[൦-൯]",mong:"[᠐-᠙]",mymr:"[၀-၉]",orya:"[୦-୯]",tamldec:"[௦-௯]",telu:"[౦-౯]",thai:"[๐-๙]",tibt:"[༠-༩]",latn:"\\d"},ua={arab:[1632,1641],arabext:[1776,1785],bali:[6992,7001],beng:[2534,2543],deva:[2406,2415],fullwide:[65296,65303],gujr:[2790,2799],khmr:[6112,6121],knda:[3302,3311],laoo:[3792,3801],limb:[6470,6479],mlym:[3430,3439],mong:[6160,6169],mymr:[4160,4169],orya:[2918,2927],tamldec:[3046,3055],telu:[3174,3183],thai:[3664,3673],tibt:[3872,3881]},gp=Do.hanidec.replace(/[\[|\]]/g,"").split("");function mp(n){let t=parseInt(n,10);if(isNaN(t)){t="";for(let e=0;e<n.length;e++){const i=n.charCodeAt(e);if(n[e].search(Do.hanidec)!==-1)t+=gp.indexOf(n[e]);else for(const s in ua){const[o,r]=ua[s];i>=o&&i<=r&&(t+=i-o)}}return parseInt(t,10)}else return t}const Js=new Map;function pp(){Js.clear()}function Dt({numberingSystem:n},t=""){const e=n||"latn";let i=Js.get(e);i===void 0&&(i=new Map,Js.set(e,i));let s=i.get(t);return s===void 0&&(s=new RegExp(`${Do[e]}${t}`),i.set(t,s)),s}let da=()=>Date.now(),fa="system",ga=null,ma=null,pa=null,ya=60,ba,xa=null;class K{static get now(){return da}static set now(t){da=t}static set defaultZone(t){fa=t}static get defaultZone(){return Jt(fa,Xi.instance)}static get defaultLocale(){return ga}static set defaultLocale(t){ga=t}static get defaultNumberingSystem(){return ma}static set defaultNumberingSystem(t){ma=t}static get defaultOutputCalendar(){return pa}static set defaultOutputCalendar(t){pa=t}static get defaultWeekSettings(){return xa}static set defaultWeekSettings(t){xa=Ks(t)}static get twoDigitCutoffYear(){return ya}static set twoDigitCutoffYear(t){ya=t%100}static get throwOnInvalid(){return ba}static set throwOnInvalid(t){ba=t}static resetCaches(){H.resetCache(),Yt.resetCache(),D.resetCache(),pp()}}class Pt{constructor(t,e){this.reason=t,this.explanation=e}toMessage(){return this.explanation?`${this.reason}: ${this.explanation}`:this.reason}}const Mc=[0,31,59,90,120,151,181,212,243,273,304,334],Tc=[0,31,60,91,121,152,182,213,244,274,305,335];function Mt(n,t){return new Pt("unit out of range",`you specified ${t} (of type ${typeof t}) as a ${n}, which is invalid`)}function Co(n,t,e){const i=new Date(Date.UTC(n,t-1,e));n<100&&n>=0&&i.setUTCFullYear(i.getUTCFullYear()-1900);const s=i.getUTCDay();return s===0?7:s}function Oc(n,t,e){return e+($n(n)?Tc:Mc)[t-1]}function Dc(n,t){const e=$n(n)?Tc:Mc,i=e.findIndex(o=>o<t),s=t-e[i];return{month:i+1,day:s}}function Po(n,t){return(n-t+7)%7+1}function Wi(n,t=4,e=1){const{year:i,month:s,day:o}=n,r=Oc(i,s,o),a=Po(Co(i,s,o),e);let l=Math.floor((r-a+14-t)/7),c;return l<1?(c=i-1,l=Fn(c,t,e)):l>Fn(i,t,e)?(c=i+1,l=1):c=i,{weekYear:c,weekNumber:l,weekday:a,...Ji(n)}}function _a(n,t=4,e=1){const{weekYear:i,weekNumber:s,weekday:o}=n,r=Po(Co(i,1,t),e),a=Ve(i);let l=s*7+o-r-7+t,c;l<1?(c=i-1,l+=Ve(c)):l>a?(c=i+1,l-=Ve(i)):c=i;const{month:h,day:u}=Dc(c,l);return{year:c,month:h,day:u,...Ji(n)}}function ws(n){const{year:t,month:e,day:i}=n,s=Oc(t,e,i);return{year:t,ordinal:s,...Ji(n)}}function wa(n){const{year:t,ordinal:e}=n,{month:i,day:s}=Dc(t,e);return{year:t,month:i,day:s,...Ji(n)}}function va(n,t){if(!P(n.localWeekday)||!P(n.localWeekNumber)||!P(n.localWeekYear)){if(!P(n.weekday)||!P(n.weekNumber)||!P(n.weekYear))throw new ze("Cannot mix locale-based week fields with ISO-based week fields");return P(n.localWeekday)||(n.weekday=n.localWeekday),P(n.localWeekNumber)||(n.weekNumber=n.localWeekNumber),P(n.localWeekYear)||(n.weekYear=n.localWeekYear),delete n.localWeekday,delete n.localWeekNumber,delete n.localWeekYear,{minDaysInFirstWeek:t.getMinDaysInFirstWeek(),startOfWeek:t.getStartOfWeek()}}else return{minDaysInFirstWeek:4,startOfWeek:1}}function yp(n,t=4,e=1){const i=Zi(n.weekYear),s=Tt(n.weekNumber,1,Fn(n.weekYear,t,e)),o=Tt(n.weekday,1,7);return i?s?o?!1:Mt("weekday",n.weekday):Mt("week",n.weekNumber):Mt("weekYear",n.weekYear)}function bp(n){const t=Zi(n.year),e=Tt(n.ordinal,1,Ve(n.year));return t?e?!1:Mt("ordinal",n.ordinal):Mt("year",n.year)}function Cc(n){const t=Zi(n.year),e=Tt(n.month,1,12),i=Tt(n.day,1,Vi(n.year,n.month));return t?e?i?!1:Mt("day",n.day):Mt("month",n.month):Mt("year",n.year)}function Pc(n){const{hour:t,minute:e,second:i,millisecond:s}=n,o=Tt(t,0,23)||t===24&&e===0&&i===0&&s===0,r=Tt(e,0,59),a=Tt(i,0,59),l=Tt(s,0,999);return o?r?a?l?!1:Mt("millisecond",s):Mt("second",i):Mt("minute",e):Mt("hour",t)}function P(n){return typeof n>"u"}function oe(n){return typeof n=="number"}function Zi(n){return typeof n=="number"&&n%1===0}function xp(n){return typeof n=="string"}function _p(n){return Object.prototype.toString.call(n)==="[object Date]"}function Ac(){try{return typeof Intl<"u"&&!!Intl.RelativeTimeFormat}catch{return!1}}function Ec(){try{return typeof Intl<"u"&&!!Intl.Locale&&("weekInfo"in Intl.Locale.prototype||"getWeekInfo"in Intl.Locale.prototype)}catch{return!1}}function wp(n){return Array.isArray(n)?n:[n]}function ka(n,t,e){if(n.length!==0)return n.reduce((i,s)=>{const o=[t(s),s];return i&&e(i[0],o[0])===i[0]?i:o},null)[1]}function vp(n,t){return t.reduce((e,i)=>(e[i]=n[i],e),{})}function Ue(n,t){return Object.prototype.hasOwnProperty.call(n,t)}function Ks(n){if(n==null)return null;if(typeof n!="object")throw new lt("Week settings must be an object");if(!Tt(n.firstDay,1,7)||!Tt(n.minimalDays,1,7)||!Array.isArray(n.weekend)||n.weekend.some(t=>!Tt(t,1,7)))throw new lt("Invalid week settings");return{firstDay:n.firstDay,minimalDays:n.minimalDays,weekend:Array.from(n.weekend)}}function Tt(n,t,e){return Zi(n)&&n>=t&&n<=e}function kp(n,t){return n-t*Math.floor(n/t)}function et(n,t=2){const e=n<0;let i;return e?i="-"+(""+-n).padStart(t,"0"):i=(""+n).padStart(t,"0"),i}function Gt(n){if(!(P(n)||n===null||n===""))return parseInt(n,10)}function pe(n){if(!(P(n)||n===null||n===""))return parseFloat(n)}function Ao(n){if(!(P(n)||n===null||n==="")){const t=parseFloat("0."+n)*1e3;return Math.floor(t)}}function Eo(n,t,e=!1){const i=10**t;return(e?Math.trunc:Math.round)(n*i)/i}function $n(n){return n%4===0&&(n%100!==0||n%400===0)}function Ve(n){return $n(n)?366:365}function Vi(n,t){const e=kp(t-1,12)+1,i=n+(t-e)/12;return e===2?$n(i)?29:28:[31,null,31,30,31,30,31,31,30,31,30,31][e-1]}function qi(n){let t=Date.UTC(n.year,n.month-1,n.day,n.hour,n.minute,n.second,n.millisecond);return n.year<100&&n.year>=0&&(t=new Date(t),t.setUTCFullYear(n.year,n.month-1,n.day)),+t}function Sa(n,t,e){return-Po(Co(n,1,t),e)+t-1}function Fn(n,t=4,e=1){const i=Sa(n,t,e),s=Sa(n+1,t,e);return(Ve(n)-i+s)/7}function Qs(n){return n>99?n:n>K.twoDigitCutoffYear?1900+n:2e3+n}function Ic(n,t,e,i=null){const s=new Date(n),o={hourCycle:"h23",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"};i&&(o.timeZone=i);const r={timeZoneName:t,...o},a=new Intl.DateTimeFormat(e,r).formatToParts(s).find(l=>l.type.toLowerCase()==="timezonename");return a?a.value:null}function Gi(n,t){let e=parseInt(n,10);Number.isNaN(e)&&(e=0);const i=parseInt(t,10)||0,s=e<0||Object.is(e,-0)?-i:i;return e*60+s}function Lc(n){const t=Number(n);if(typeof n=="boolean"||n===""||Number.isNaN(t))throw new lt(`Invalid unit value ${n}`);return t}function Bi(n,t){const e={};for(const i in n)if(Ue(n,i)){const s=n[i];if(s==null)continue;e[t(i)]=Lc(s)}return e}function Dn(n,t){const e=Math.trunc(Math.abs(n/60)),i=Math.trunc(Math.abs(n%60)),s=n>=0?"+":"-";switch(t){case"short":return`${s}${et(e,2)}:${et(i,2)}`;case"narrow":return`${s}${e}${i>0?`:${i}`:""}`;case"techie":return`${s}${et(e,2)}${et(i,2)}`;default:throw new RangeError(`Value format ${t} is out of range for property format`)}}function Ji(n){return vp(n,["hour","minute","second","millisecond"])}const Sp=["January","February","March","April","May","June","July","August","September","October","November","December"],Rc=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],Mp=["J","F","M","A","M","J","J","A","S","O","N","D"];function Fc(n){switch(n){case"narrow":return[...Mp];case"short":return[...Rc];case"long":return[...Sp];case"numeric":return["1","2","3","4","5","6","7","8","9","10","11","12"];case"2-digit":return["01","02","03","04","05","06","07","08","09","10","11","12"];default:return null}}const Nc=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],zc=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],Tp=["M","T","W","T","F","S","S"];function Wc(n){switch(n){case"narrow":return[...Tp];case"short":return[...zc];case"long":return[...Nc];case"numeric":return["1","2","3","4","5","6","7"];default:return null}}const Vc=["AM","PM"],Op=["Before Christ","Anno Domini"],Dp=["BC","AD"],Cp=["B","A"];function Bc(n){switch(n){case"narrow":return[...Cp];case"short":return[...Dp];case"long":return[...Op];default:return null}}function Pp(n){return Vc[n.hour<12?0:1]}function Ap(n,t){return Wc(t)[n.weekday-1]}function Ep(n,t){return Fc(t)[n.month-1]}function Ip(n,t){return Bc(t)[n.year<0?0:1]}function Lp(n,t,e="always",i=!1){const s={years:["year","yr."],quarters:["quarter","qtr."],months:["month","mo."],weeks:["week","wk."],days:["day","day","days"],hours:["hour","hr."],minutes:["minute","min."],seconds:["second","sec."]},o=["hours","minutes","seconds"].indexOf(n)===-1;if(e==="auto"&&o){const u=n==="days";switch(t){case 1:return u?"tomorrow":`next ${s[n][0]}`;case-1:return u?"yesterday":`last ${s[n][0]}`;case 0:return u?"today":`this ${s[n][0]}`}}const r=Object.is(t,-0)||t<0,a=Math.abs(t),l=a===1,c=s[n],h=i?l?c[1]:c[2]||c[1]:l?s[n][0]:n;return r?`${a} ${h} ago`:`in ${a} ${h}`}function Ma(n,t){let e="";for(const i of n)i.literal?e+=i.val:e+=t(i.val);return e}const Rp={D:zi,DD:sc,DDD:oc,DDDD:rc,t:ac,tt:lc,ttt:cc,tttt:hc,T:uc,TT:dc,TTT:fc,TTTT:gc,f:mc,ff:yc,fff:xc,ffff:wc,F:pc,FF:bc,FFF:_c,FFFF:vc};class ct{static create(t,e={}){return new ct(t,e)}static parseFormat(t){let e=null,i="",s=!1;const o=[];for(let r=0;r<t.length;r++){const a=t.charAt(r);a==="'"?(i.length>0&&o.push({literal:s||/^\s+$/.test(i),val:i}),e=null,i="",s=!s):s||a===e?i+=a:(i.length>0&&o.push({literal:/^\s+$/.test(i),val:i}),i=a,e=a)}return i.length>0&&o.push({literal:s||/^\s+$/.test(i),val:i}),o}static macroTokenToFormatOpts(t){return Rp[t]}constructor(t,e){this.opts=e,this.loc=t,this.systemLoc=null}formatWithSystemDefault(t,e){return this.systemLoc===null&&(this.systemLoc=this.loc.redefaultToSystem()),this.systemLoc.dtFormatter(t,{...this.opts,...e}).format()}dtFormatter(t,e={}){return this.loc.dtFormatter(t,{...this.opts,...e})}formatDateTime(t,e){return this.dtFormatter(t,e).format()}formatDateTimeParts(t,e){return this.dtFormatter(t,e).formatToParts()}formatInterval(t,e){return this.dtFormatter(t.start,e).dtf.formatRange(t.start.toJSDate(),t.end.toJSDate())}resolvedOptions(t,e){return this.dtFormatter(t,e).resolvedOptions()}num(t,e=0){if(this.opts.forceSimple)return et(t,e);const i={...this.opts};return e>0&&(i.padTo=e),this.loc.numberFormatter(i).format(t)}formatDateTimeFromString(t,e){const i=this.loc.listingMode()==="en",s=this.loc.outputCalendar&&this.loc.outputCalendar!=="gregory",o=(f,g)=>this.loc.extract(t,f,g),r=f=>t.isOffsetFixed&&t.offset===0&&f.allowZ?"Z":t.isValid?t.zone.formatOffset(t.ts,f.format):"",a=()=>i?Pp(t):o({hour:"numeric",hourCycle:"h12"},"dayperiod"),l=(f,g)=>i?Ep(t,f):o(g?{month:f}:{month:f,day:"numeric"},"month"),c=(f,g)=>i?Ap(t,f):o(g?{weekday:f}:{weekday:f,month:"long",day:"numeric"},"weekday"),h=f=>{const g=ct.macroTokenToFormatOpts(f);return g?this.formatWithSystemDefault(t,g):f},u=f=>i?Ip(t,f):o({era:f},"era"),d=f=>{switch(f){case"S":return this.num(t.millisecond);case"u":case"SSS":return this.num(t.millisecond,3);case"s":return this.num(t.second);case"ss":return this.num(t.second,2);case"uu":return this.num(Math.floor(t.millisecond/10),2);case"uuu":return this.num(Math.floor(t.millisecond/100));case"m":return this.num(t.minute);case"mm":return this.num(t.minute,2);case"h":return this.num(t.hour%12===0?12:t.hour%12);case"hh":return this.num(t.hour%12===0?12:t.hour%12,2);case"H":return this.num(t.hour);case"HH":return this.num(t.hour,2);case"Z":return r({format:"narrow",allowZ:this.opts.allowZ});case"ZZ":return r({format:"short",allowZ:this.opts.allowZ});case"ZZZ":return r({format:"techie",allowZ:this.opts.allowZ});case"ZZZZ":return t.zone.offsetName(t.ts,{format:"short",locale:this.loc.locale});case"ZZZZZ":return t.zone.offsetName(t.ts,{format:"long",locale:this.loc.locale});case"z":return t.zoneName;case"a":return a();case"d":return s?o({day:"numeric"},"day"):this.num(t.day);case"dd":return s?o({day:"2-digit"},"day"):this.num(t.day,2);case"c":return this.num(t.weekday);case"ccc":return c("short",!0);case"cccc":return c("long",!0);case"ccccc":return c("narrow",!0);case"E":return this.num(t.weekday);case"EEE":return c("short",!1);case"EEEE":return c("long",!1);case"EEEEE":return c("narrow",!1);case"L":return s?o({month:"numeric",day:"numeric"},"month"):this.num(t.month);case"LL":return s?o({month:"2-digit",day:"numeric"},"month"):this.num(t.month,2);case"LLL":return l("short",!0);case"LLLL":return l("long",!0);case"LLLLL":return l("narrow",!0);case"M":return s?o({month:"numeric"},"month"):this.num(t.month);case"MM":return s?o({month:"2-digit"},"month"):this.num(t.month,2);case"MMM":return l("short",!1);case"MMMM":return l("long",!1);case"MMMMM":return l("narrow",!1);case"y":return s?o({year:"numeric"},"year"):this.num(t.year);case"yy":return s?o({year:"2-digit"},"year"):this.num(t.year.toString().slice(-2),2);case"yyyy":return s?o({year:"numeric"},"year"):this.num(t.year,4);case"yyyyyy":return s?o({year:"numeric"},"year"):this.num(t.year,6);case"G":return u("short");case"GG":return u("long");case"GGGGG":return u("narrow");case"kk":return this.num(t.weekYear.toString().slice(-2),2);case"kkkk":return this.num(t.weekYear,4);case"W":return this.num(t.weekNumber);case"WW":return this.num(t.weekNumber,2);case"n":return this.num(t.localWeekNumber);case"nn":return this.num(t.localWeekNumber,2);case"ii":return this.num(t.localWeekYear.toString().slice(-2),2);case"iiii":return this.num(t.localWeekYear,4);case"o":return this.num(t.ordinal);case"ooo":return this.num(t.ordinal,3);case"q":return this.num(t.quarter);case"qq":return this.num(t.quarter,2);case"X":return this.num(Math.floor(t.ts/1e3));case"x":return this.num(t.ts);default:return h(f)}};return Ma(ct.parseFormat(e),d)}formatDurationFromString(t,e){const i=l=>{switch(l[0]){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":return"hour";case"d":return"day";case"w":return"week";case"M":return"month";case"y":return"year";default:return null}},s=l=>c=>{const h=i(c);return h?this.num(l.get(h),c.length):c},o=ct.parseFormat(e),r=o.reduce((l,{literal:c,val:h})=>c?l:l.concat(h),[]),a=t.shiftTo(...r.map(i).filter(l=>l));return Ma(o,s(a))}}const Hc=/[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;function Ze(...n){const t=n.reduce((e,i)=>e+i.source,"");return RegExp(`^${t}$`)}function qe(...n){return t=>n.reduce(([e,i,s],o)=>{const[r,a,l]=o(t,s);return[{...e,...r},a||i,l]},[{},null,1]).slice(0,2)}function Ge(n,...t){if(n==null)return[null,null];for(const[e,i]of t){const s=e.exec(n);if(s)return i(s)}return[null,null]}function $c(...n){return(t,e)=>{const i={};let s;for(s=0;s<n.length;s++)i[n[s]]=Gt(t[e+s]);return[i,null,e+s]}}const jc=/(?:(Z)|([+-]\d\d)(?::?(\d\d))?)/,Fp=`(?:${jc.source}?(?:\\[(${Hc.source})\\])?)?`,Io=/(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,Yc=RegExp(`${Io.source}${Fp}`),Lo=RegExp(`(?:T${Yc.source})?`),Np=/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/,zp=/(\d{4})-?W(\d\d)(?:-?(\d))?/,Wp=/(\d{4})-?(\d{3})/,Vp=$c("weekYear","weekNumber","weekDay"),Bp=$c("year","ordinal"),Hp=/(\d{4})-(\d\d)-(\d\d)/,Uc=RegExp(`${Io.source} ?(?:${jc.source}|(${Hc.source}))?`),$p=RegExp(`(?: ${Uc.source})?`);function Be(n,t,e){const i=n[t];return P(i)?e:Gt(i)}function jp(n,t){return[{year:Be(n,t),month:Be(n,t+1,1),day:Be(n,t+2,1)},null,t+3]}function Je(n,t){return[{hours:Be(n,t,0),minutes:Be(n,t+1,0),seconds:Be(n,t+2,0),milliseconds:Ao(n[t+3])},null,t+4]}function jn(n,t){const e=!n[t]&&!n[t+1],i=Gi(n[t+1],n[t+2]),s=e?null:ut.instance(i);return[{},s,t+3]}function Yn(n,t){const e=n[t]?Yt.create(n[t]):null;return[{},e,t+1]}const Yp=RegExp(`^T?${Io.source}$`),Up=/^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;function Xp(n){const[t,e,i,s,o,r,a,l,c]=n,h=t[0]==="-",u=l&&l[0]==="-",d=(f,g=!1)=>f!==void 0&&(g||f&&h)?-f:f;return[{years:d(pe(e)),months:d(pe(i)),weeks:d(pe(s)),days:d(pe(o)),hours:d(pe(r)),minutes:d(pe(a)),seconds:d(pe(l),l==="-0"),milliseconds:d(Ao(c),u)}]}const Zp={GMT:0,EDT:-4*60,EST:-5*60,CDT:-5*60,CST:-6*60,MDT:-6*60,MST:-7*60,PDT:-7*60,PST:-8*60};function Ro(n,t,e,i,s,o,r){const a={year:t.length===2?Qs(Gt(t)):Gt(t),month:Rc.indexOf(e)+1,day:Gt(i),hour:Gt(s),minute:Gt(o)};return r&&(a.second=Gt(r)),n&&(a.weekday=n.length>3?Nc.indexOf(n)+1:zc.indexOf(n)+1),a}const qp=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;function Gp(n){const[,t,e,i,s,o,r,a,l,c,h,u]=n,d=Ro(t,s,i,e,o,r,a);let f;return l?f=Zp[l]:c?f=0:f=Gi(h,u),[d,new ut(f)]}function Jp(n){return n.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim()}const Kp=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,Qp=/^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,ty=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;function Ta(n){const[,t,e,i,s,o,r,a]=n;return[Ro(t,s,i,e,o,r,a),ut.utcInstance]}function ey(n){const[,t,e,i,s,o,r,a]=n;return[Ro(t,a,e,i,s,o,r),ut.utcInstance]}const ny=Ze(Np,Lo),iy=Ze(zp,Lo),sy=Ze(Wp,Lo),oy=Ze(Yc),Xc=qe(jp,Je,jn,Yn),ry=qe(Vp,Je,jn,Yn),ay=qe(Bp,Je,jn,Yn),ly=qe(Je,jn,Yn);function cy(n){return Ge(n,[ny,Xc],[iy,ry],[sy,ay],[oy,ly])}function hy(n){return Ge(Jp(n),[qp,Gp])}function uy(n){return Ge(n,[Kp,Ta],[Qp,Ta],[ty,ey])}function dy(n){return Ge(n,[Up,Xp])}const fy=qe(Je);function gy(n){return Ge(n,[Yp,fy])}const my=Ze(Hp,$p),py=Ze(Uc),yy=qe(Je,jn,Yn);function by(n){return Ge(n,[my,Xc],[py,yy])}const Oa="Invalid Duration",Zc={weeks:{days:7,hours:7*24,minutes:7*24*60,seconds:7*24*60*60,milliseconds:7*24*60*60*1e3},days:{hours:24,minutes:24*60,seconds:24*60*60,milliseconds:24*60*60*1e3},hours:{minutes:60,seconds:60*60,milliseconds:60*60*1e3},minutes:{seconds:60,milliseconds:60*1e3},seconds:{milliseconds:1e3}},xy={years:{quarters:4,months:12,weeks:52,days:365,hours:365*24,minutes:365*24*60,seconds:365*24*60*60,milliseconds:365*24*60*60*1e3},quarters:{months:3,weeks:13,days:91,hours:91*24,minutes:91*24*60,seconds:91*24*60*60,milliseconds:91*24*60*60*1e3},months:{weeks:4,days:30,hours:30*24,minutes:30*24*60,seconds:30*24*60*60,milliseconds:30*24*60*60*1e3},...Zc},vt=146097/400,Le=146097/4800,_y={years:{quarters:4,months:12,weeks:vt/7,days:vt,hours:vt*24,minutes:vt*24*60,seconds:vt*24*60*60,milliseconds:vt*24*60*60*1e3},quarters:{months:3,weeks:vt/28,days:vt/4,hours:vt*24/4,minutes:vt*24*60/4,seconds:vt*24*60*60/4,milliseconds:vt*24*60*60*1e3/4},months:{weeks:Le/7,days:Le,hours:Le*24,minutes:Le*24*60,seconds:Le*24*60*60,milliseconds:Le*24*60*60*1e3},...Zc},_e=["years","quarters","months","weeks","days","hours","minutes","seconds","milliseconds"],wy=_e.slice(0).reverse();function Zt(n,t,e=!1){const i={values:e?t.values:{...n.values,...t.values||{}},loc:n.loc.clone(t.loc),conversionAccuracy:t.conversionAccuracy||n.conversionAccuracy,matrix:t.matrix||n.matrix};return new N(i)}function qc(n,t){let e=t.milliseconds??0;for(const i of wy.slice(1))t[i]&&(e+=t[i]*n[i].milliseconds);return e}function Da(n,t){const e=qc(n,t)<0?-1:1;_e.reduceRight((i,s)=>{if(P(t[s]))return i;if(i){const o=t[i]*e,r=n[s][i],a=Math.floor(o/r);t[s]+=a*e,t[i]-=a*r*e}return s},null),_e.reduce((i,s)=>{if(P(t[s]))return i;if(i){const o=t[i]%1;t[i]-=o,t[s]+=o*n[i][s]}return s},null)}function vy(n){const t={};for(const[e,i]of Object.entries(n))i!==0&&(t[e]=i);return t}class N{constructor(t){const e=t.conversionAccuracy==="longterm"||!1;let i=e?_y:xy;t.matrix&&(i=t.matrix),this.values=t.values,this.loc=t.loc||H.create(),this.conversionAccuracy=e?"longterm":"casual",this.invalid=t.invalid||null,this.matrix=i,this.isLuxonDuration=!0}static fromMillis(t,e){return N.fromObject({milliseconds:t},e)}static fromObject(t,e={}){if(t==null||typeof t!="object")throw new lt(`Duration.fromObject: argument expected to be an object, got ${t===null?"null":typeof t}`);return new N({values:Bi(t,N.normalizeUnit),loc:H.fromObject(e),conversionAccuracy:e.conversionAccuracy,matrix:e.matrix})}static fromDurationLike(t){if(oe(t))return N.fromMillis(t);if(N.isDuration(t))return t;if(typeof t=="object")return N.fromObject(t);throw new lt(`Unknown duration argument ${t} of type ${typeof t}`)}static fromISO(t,e){const[i]=dy(t);return i?N.fromObject(i,e):N.invalid("unparsable",`the input "${t}" can't be parsed as ISO 8601`)}static fromISOTime(t,e){const[i]=gy(t);return i?N.fromObject(i,e):N.invalid("unparsable",`the input "${t}" can't be parsed as ISO 8601`)}static invalid(t,e=null){if(!t)throw new lt("need to specify a reason the Duration is invalid");const i=t instanceof Pt?t:new Pt(t,e);if(K.throwOnInvalid)throw new Xm(i);return new N({invalid:i})}static normalizeUnit(t){const e={year:"years",years:"years",quarter:"quarters",quarters:"quarters",month:"months",months:"months",week:"weeks",weeks:"weeks",day:"days",days:"days",hour:"hours",hours:"hours",minute:"minutes",minutes:"minutes",second:"seconds",seconds:"seconds",millisecond:"milliseconds",milliseconds:"milliseconds"}[t&&t.toLowerCase()];if(!e)throw new ic(t);return e}static isDuration(t){return t&&t.isLuxonDuration||!1}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}toFormat(t,e={}){const i={...e,floor:e.round!==!1&&e.floor!==!1};return this.isValid?ct.create(this.loc,i).formatDurationFromString(this,t):Oa}toHuman(t={}){if(!this.isValid)return Oa;const e=_e.map(i=>{const s=this.values[i];return P(s)?null:this.loc.numberFormatter({style:"unit",unitDisplay:"long",...t,unit:i.slice(0,-1)}).format(s)}).filter(i=>i);return this.loc.listFormatter({type:"conjunction",style:t.listStyle||"narrow",...t}).format(e)}toObject(){return this.isValid?{...this.values}:{}}toISO(){if(!this.isValid)return null;let t="P";return this.years!==0&&(t+=this.years+"Y"),(this.months!==0||this.quarters!==0)&&(t+=this.months+this.quarters*3+"M"),this.weeks!==0&&(t+=this.weeks+"W"),this.days!==0&&(t+=this.days+"D"),(this.hours!==0||this.minutes!==0||this.seconds!==0||this.milliseconds!==0)&&(t+="T"),this.hours!==0&&(t+=this.hours+"H"),this.minutes!==0&&(t+=this.minutes+"M"),(this.seconds!==0||this.milliseconds!==0)&&(t+=Eo(this.seconds+this.milliseconds/1e3,3)+"S"),t==="P"&&(t+="T0S"),t}toISOTime(t={}){if(!this.isValid)return null;const e=this.toMillis();return e<0||e>=864e5?null:(t={suppressMilliseconds:!1,suppressSeconds:!1,includePrefix:!1,format:"extended",...t,includeOffset:!1},D.fromMillis(e,{zone:"UTC"}).toISOTime(t))}toJSON(){return this.toISO()}toString(){return this.toISO()}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`Duration { values: ${JSON.stringify(this.values)} }`:`Duration { Invalid, reason: ${this.invalidReason} }`}toMillis(){return this.isValid?qc(this.matrix,this.values):NaN}valueOf(){return this.toMillis()}plus(t){if(!this.isValid)return this;const e=N.fromDurationLike(t),i={};for(const s of _e)(Ue(e.values,s)||Ue(this.values,s))&&(i[s]=e.get(s)+this.get(s));return Zt(this,{values:i},!0)}minus(t){if(!this.isValid)return this;const e=N.fromDurationLike(t);return this.plus(e.negate())}mapUnits(t){if(!this.isValid)return this;const e={};for(const i of Object.keys(this.values))e[i]=Lc(t(this.values[i],i));return Zt(this,{values:e},!0)}get(t){return this[N.normalizeUnit(t)]}set(t){if(!this.isValid)return this;const e={...this.values,...Bi(t,N.normalizeUnit)};return Zt(this,{values:e})}reconfigure({locale:t,numberingSystem:e,conversionAccuracy:i,matrix:s}={}){const r={loc:this.loc.clone({locale:t,numberingSystem:e}),matrix:s,conversionAccuracy:i};return Zt(this,r)}as(t){return this.isValid?this.shiftTo(t).get(t):NaN}normalize(){if(!this.isValid)return this;const t=this.toObject();return Da(this.matrix,t),Zt(this,{values:t},!0)}rescale(){if(!this.isValid)return this;const t=vy(this.normalize().shiftToAll().toObject());return Zt(this,{values:t},!0)}shiftTo(...t){if(!this.isValid)return this;if(t.length===0)return this;t=t.map(r=>N.normalizeUnit(r));const e={},i={},s=this.toObject();let o;for(const r of _e)if(t.indexOf(r)>=0){o=r;let a=0;for(const c in i)a+=this.matrix[c][r]*i[c],i[c]=0;oe(s[r])&&(a+=s[r]);const l=Math.trunc(a);e[r]=l,i[r]=(a*1e3-l*1e3)/1e3}else oe(s[r])&&(i[r]=s[r]);for(const r in i)i[r]!==0&&(e[o]+=r===o?i[r]:i[r]/this.matrix[o][r]);return Da(this.matrix,e),Zt(this,{values:e},!0)}shiftToAll(){return this.isValid?this.shiftTo("years","months","weeks","days","hours","minutes","seconds","milliseconds"):this}negate(){if(!this.isValid)return this;const t={};for(const e of Object.keys(this.values))t[e]=this.values[e]===0?0:-this.values[e];return Zt(this,{values:t},!0)}get years(){return this.isValid?this.values.years||0:NaN}get quarters(){return this.isValid?this.values.quarters||0:NaN}get months(){return this.isValid?this.values.months||0:NaN}get weeks(){return this.isValid?this.values.weeks||0:NaN}get days(){return this.isValid?this.values.days||0:NaN}get hours(){return this.isValid?this.values.hours||0:NaN}get minutes(){return this.isValid?this.values.minutes||0:NaN}get seconds(){return this.isValid?this.values.seconds||0:NaN}get milliseconds(){return this.isValid?this.values.milliseconds||0:NaN}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}equals(t){if(!this.isValid||!t.isValid||!this.loc.equals(t.loc))return!1;function e(i,s){return i===void 0||i===0?s===void 0||s===0:i===s}for(const i of _e)if(!e(this.values[i],t.values[i]))return!1;return!0}}const Re="Invalid Interval";function ky(n,t){return!n||!n.isValid?J.invalid("missing or invalid start"):!t||!t.isValid?J.invalid("missing or invalid end"):t<n?J.invalid("end before start",`The end of an interval must be after its start, but you had start=${n.toISO()} and end=${t.toISO()}`):null}class J{constructor(t){this.s=t.start,this.e=t.end,this.invalid=t.invalid||null,this.isLuxonInterval=!0}static invalid(t,e=null){if(!t)throw new lt("need to specify a reason the Interval is invalid");const i=t instanceof Pt?t:new Pt(t,e);if(K.throwOnInvalid)throw new Um(i);return new J({invalid:i})}static fromDateTimes(t,e){const i=cn(t),s=cn(e),o=ky(i,s);return o??new J({start:i,end:s})}static after(t,e){const i=N.fromDurationLike(e),s=cn(t);return J.fromDateTimes(s,s.plus(i))}static before(t,e){const i=N.fromDurationLike(e),s=cn(t);return J.fromDateTimes(s.minus(i),s)}static fromISO(t,e){const[i,s]=(t||"").split("/",2);if(i&&s){let o,r;try{o=D.fromISO(i,e),r=o.isValid}catch{r=!1}let a,l;try{a=D.fromISO(s,e),l=a.isValid}catch{l=!1}if(r&&l)return J.fromDateTimes(o,a);if(r){const c=N.fromISO(s,e);if(c.isValid)return J.after(o,c)}else if(l){const c=N.fromISO(i,e);if(c.isValid)return J.before(a,c)}}return J.invalid("unparsable",`the input "${t}" can't be parsed as ISO 8601`)}static isInterval(t){return t&&t.isLuxonInterval||!1}get start(){return this.isValid?this.s:null}get end(){return this.isValid?this.e:null}get lastDateTime(){return this.isValid&&this.e?this.e.minus(1):null}get isValid(){return this.invalidReason===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}length(t="milliseconds"){return this.isValid?this.toDuration(t).get(t):NaN}count(t="milliseconds",e){if(!this.isValid)return NaN;const i=this.start.startOf(t,e);let s;return e!=null&&e.useLocaleWeeks?s=this.end.reconfigure({locale:i.locale}):s=this.end,s=s.startOf(t,e),Math.floor(s.diff(i,t).get(t))+(s.valueOf()!==this.end.valueOf())}hasSame(t){return this.isValid?this.isEmpty()||this.e.minus(1).hasSame(this.s,t):!1}isEmpty(){return this.s.valueOf()===this.e.valueOf()}isAfter(t){return this.isValid?this.s>t:!1}isBefore(t){return this.isValid?this.e<=t:!1}contains(t){return this.isValid?this.s<=t&&this.e>t:!1}set({start:t,end:e}={}){return this.isValid?J.fromDateTimes(t||this.s,e||this.e):this}splitAt(...t){if(!this.isValid)return[];const e=t.map(cn).filter(r=>this.contains(r)).sort((r,a)=>r.toMillis()-a.toMillis()),i=[];let{s}=this,o=0;for(;s<this.e;){const r=e[o]||this.e,a=+r>+this.e?this.e:r;i.push(J.fromDateTimes(s,a)),s=a,o+=1}return i}splitBy(t){const e=N.fromDurationLike(t);if(!this.isValid||!e.isValid||e.as("milliseconds")===0)return[];let{s:i}=this,s=1,o;const r=[];for(;i<this.e;){const a=this.start.plus(e.mapUnits(l=>l*s));o=+a>+this.e?this.e:a,r.push(J.fromDateTimes(i,o)),i=o,s+=1}return r}divideEqually(t){return this.isValid?this.splitBy(this.length()/t).slice(0,t):[]}overlaps(t){return this.e>t.s&&this.s<t.e}abutsStart(t){return this.isValid?+this.e==+t.s:!1}abutsEnd(t){return this.isValid?+t.e==+this.s:!1}engulfs(t){return this.isValid?this.s<=t.s&&this.e>=t.e:!1}equals(t){return!this.isValid||!t.isValid?!1:this.s.equals(t.s)&&this.e.equals(t.e)}intersection(t){if(!this.isValid)return this;const e=this.s>t.s?this.s:t.s,i=this.e<t.e?this.e:t.e;return e>=i?null:J.fromDateTimes(e,i)}union(t){if(!this.isValid)return this;const e=this.s<t.s?this.s:t.s,i=this.e>t.e?this.e:t.e;return J.fromDateTimes(e,i)}static merge(t){const[e,i]=t.sort((s,o)=>s.s-o.s).reduce(([s,o],r)=>o?o.overlaps(r)||o.abutsStart(r)?[s,o.union(r)]:[s.concat([o]),r]:[s,r],[[],null]);return i&&e.push(i),e}static xor(t){let e=null,i=0;const s=[],o=t.map(l=>[{time:l.s,type:"s"},{time:l.e,type:"e"}]),r=Array.prototype.concat(...o),a=r.sort((l,c)=>l.time-c.time);for(const l of a)i+=l.type==="s"?1:-1,i===1?e=l.time:(e&&+e!=+l.time&&s.push(J.fromDateTimes(e,l.time)),e=null);return J.merge(s)}difference(...t){return J.xor([this].concat(t)).map(e=>this.intersection(e)).filter(e=>e&&!e.isEmpty())}toString(){return this.isValid?`[${this.s.toISO()} – ${this.e.toISO()})`:Re}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`Interval { start: ${this.s.toISO()}, end: ${this.e.toISO()} }`:`Interval { Invalid, reason: ${this.invalidReason} }`}toLocaleString(t=zi,e={}){return this.isValid?ct.create(this.s.loc.clone(e),t).formatInterval(this):Re}toISO(t){return this.isValid?`${this.s.toISO(t)}/${this.e.toISO(t)}`:Re}toISODate(){return this.isValid?`${this.s.toISODate()}/${this.e.toISODate()}`:Re}toISOTime(t){return this.isValid?`${this.s.toISOTime(t)}/${this.e.toISOTime(t)}`:Re}toFormat(t,{separator:e=" – "}={}){return this.isValid?`${this.s.toFormat(t)}${e}${this.e.toFormat(t)}`:Re}toDuration(t,e){return this.isValid?this.e.diff(this.s,t,e):N.invalid(this.invalidReason)}mapEndpoints(t){return J.fromDateTimes(t(this.s),t(this.e))}}class ui{static hasDST(t=K.defaultZone){const e=D.now().setZone(t).set({month:12});return!t.isUniversal&&e.offset!==e.set({month:6}).offset}static isValidIANAZone(t){return Yt.isValidZone(t)}static normalizeZone(t){return Jt(t,K.defaultZone)}static getStartOfWeek({locale:t=null,locObj:e=null}={}){return(e||H.create(t)).getStartOfWeek()}static getMinimumDaysInFirstWeek({locale:t=null,locObj:e=null}={}){return(e||H.create(t)).getMinDaysInFirstWeek()}static getWeekendWeekdays({locale:t=null,locObj:e=null}={}){return(e||H.create(t)).getWeekendDays().slice()}static months(t="long",{locale:e=null,numberingSystem:i=null,locObj:s=null,outputCalendar:o="gregory"}={}){return(s||H.create(e,i,o)).months(t)}static monthsFormat(t="long",{locale:e=null,numberingSystem:i=null,locObj:s=null,outputCalendar:o="gregory"}={}){return(s||H.create(e,i,o)).months(t,!0)}static weekdays(t="long",{locale:e=null,numberingSystem:i=null,locObj:s=null}={}){return(s||H.create(e,i,null)).weekdays(t)}static weekdaysFormat(t="long",{locale:e=null,numberingSystem:i=null,locObj:s=null}={}){return(s||H.create(e,i,null)).weekdays(t,!0)}static meridiems({locale:t=null}={}){return H.create(t).meridiems()}static eras(t="short",{locale:e=null}={}){return H.create(e,null,"gregory").eras(t)}static features(){return{relative:Ac(),localeWeek:Ec()}}}function Ca(n,t){const e=s=>s.toUTC(0,{keepLocalTime:!0}).startOf("day").valueOf(),i=e(t)-e(n);return Math.floor(N.fromMillis(i).as("days"))}function Sy(n,t,e){const i=[["years",(l,c)=>c.year-l.year],["quarters",(l,c)=>c.quarter-l.quarter+(c.year-l.year)*4],["months",(l,c)=>c.month-l.month+(c.year-l.year)*12],["weeks",(l,c)=>{const h=Ca(l,c);return(h-h%7)/7}],["days",Ca]],s={},o=n;let r,a;for(const[l,c]of i)e.indexOf(l)>=0&&(r=l,s[l]=c(n,t),a=o.plus(s),a>t?(s[l]--,n=o.plus(s),n>t&&(a=n,s[l]--,n=o.plus(s))):n=a);return[n,s,a,r]}function My(n,t,e,i){let[s,o,r,a]=Sy(n,t,e);const l=t-s,c=e.filter(u=>["hours","minutes","seconds","milliseconds"].indexOf(u)>=0);c.length===0&&(r<t&&(r=s.plus({[a]:1})),r!==s&&(o[a]=(o[a]||0)+l/(r-s)));const h=N.fromObject(o,i);return c.length>0?N.fromMillis(l,i).shiftTo(...c).plus(h):h}const Ty="missing Intl.DateTimeFormat.formatToParts support";function z(n,t=e=>e){return{regex:n,deser:([e])=>t(mp(e))}}const Oy=String.fromCharCode(160),Gc=`[ ${Oy}]`,Jc=new RegExp(Gc,"g");function Dy(n){return n.replace(/\./g,"\\.?").replace(Jc,Gc)}function Pa(n){return n.replace(/\./g,"").replace(Jc," ").toLowerCase()}function Ct(n,t){return n===null?null:{regex:RegExp(n.map(Dy).join("|")),deser:([e])=>n.findIndex(i=>Pa(e)===Pa(i))+t}}function Aa(n,t){return{regex:n,deser:([,e,i])=>Gi(e,i),groups:t}}function di(n){return{regex:n,deser:([t])=>t}}function Cy(n){return n.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function Py(n,t){const e=Dt(t),i=Dt(t,"{2}"),s=Dt(t,"{3}"),o=Dt(t,"{4}"),r=Dt(t,"{6}"),a=Dt(t,"{1,2}"),l=Dt(t,"{1,3}"),c=Dt(t,"{1,6}"),h=Dt(t,"{1,9}"),u=Dt(t,"{2,4}"),d=Dt(t,"{4,6}"),f=p=>({regex:RegExp(Cy(p.val)),deser:([y])=>y,literal:!0}),m=(p=>{if(n.literal)return f(p);switch(p.val){case"G":return Ct(t.eras("short"),0);case"GG":return Ct(t.eras("long"),0);case"y":return z(c);case"yy":return z(u,Qs);case"yyyy":return z(o);case"yyyyy":return z(d);case"yyyyyy":return z(r);case"M":return z(a);case"MM":return z(i);case"MMM":return Ct(t.months("short",!0),1);case"MMMM":return Ct(t.months("long",!0),1);case"L":return z(a);case"LL":return z(i);case"LLL":return Ct(t.months("short",!1),1);case"LLLL":return Ct(t.months("long",!1),1);case"d":return z(a);case"dd":return z(i);case"o":return z(l);case"ooo":return z(s);case"HH":return z(i);case"H":return z(a);case"hh":return z(i);case"h":return z(a);case"mm":return z(i);case"m":return z(a);case"q":return z(a);case"qq":return z(i);case"s":return z(a);case"ss":return z(i);case"S":return z(l);case"SSS":return z(s);case"u":return di(h);case"uu":return di(a);case"uuu":return z(e);case"a":return Ct(t.meridiems(),0);case"kkkk":return z(o);case"kk":return z(u,Qs);case"W":return z(a);case"WW":return z(i);case"E":case"c":return z(e);case"EEE":return Ct(t.weekdays("short",!1),1);case"EEEE":return Ct(t.weekdays("long",!1),1);case"ccc":return Ct(t.weekdays("short",!0),1);case"cccc":return Ct(t.weekdays("long",!0),1);case"Z":case"ZZ":return Aa(new RegExp(`([+-]${a.source})(?::(${i.source}))?`),2);case"ZZZ":return Aa(new RegExp(`([+-]${a.source})(${i.source})?`),2);case"z":return di(/[a-z_+-/]{1,256}?/i);case" ":return di(/[^\S\n\r]/);default:return f(p)}})(n)||{invalidReason:Ty};return m.token=n,m}const Ay={year:{"2-digit":"yy",numeric:"yyyyy"},month:{numeric:"M","2-digit":"MM",short:"MMM",long:"MMMM"},day:{numeric:"d","2-digit":"dd"},weekday:{short:"EEE",long:"EEEE"},dayperiod:"a",dayPeriod:"a",hour12:{numeric:"h","2-digit":"hh"},hour24:{numeric:"H","2-digit":"HH"},minute:{numeric:"m","2-digit":"mm"},second:{numeric:"s","2-digit":"ss"},timeZoneName:{long:"ZZZZZ",short:"ZZZ"}};function Ey(n,t,e){const{type:i,value:s}=n;if(i==="literal"){const l=/^\s+$/.test(s);return{literal:!l,val:l?" ":s}}const o=t[i];let r=i;i==="hour"&&(t.hour12!=null?r=t.hour12?"hour12":"hour24":t.hourCycle!=null?t.hourCycle==="h11"||t.hourCycle==="h12"?r="hour12":r="hour24":r=e.hour12?"hour12":"hour24");let a=Ay[r];if(typeof a=="object"&&(a=a[o]),a)return{literal:!1,val:a}}function Iy(n){return[`^${n.map(e=>e.regex).reduce((e,i)=>`${e}(${i.source})`,"")}$`,n]}function Ly(n,t,e){const i=n.match(t);if(i){const s={};let o=1;for(const r in e)if(Ue(e,r)){const a=e[r],l=a.groups?a.groups+1:1;!a.literal&&a.token&&(s[a.token.val[0]]=a.deser(i.slice(o,o+l))),o+=l}return[i,s]}else return[i,{}]}function Ry(n){const t=o=>{switch(o){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":case"H":return"hour";case"d":return"day";case"o":return"ordinal";case"L":case"M":return"month";case"y":return"year";case"E":case"c":return"weekday";case"W":return"weekNumber";case"k":return"weekYear";case"q":return"quarter";default:return null}};let e=null,i;return P(n.z)||(e=Yt.create(n.z)),P(n.Z)||(e||(e=new ut(n.Z)),i=n.Z),P(n.q)||(n.M=(n.q-1)*3+1),P(n.h)||(n.h<12&&n.a===1?n.h+=12:n.h===12&&n.a===0&&(n.h=0)),n.G===0&&n.y&&(n.y=-n.y),P(n.u)||(n.S=Ao(n.u)),[Object.keys(n).reduce((o,r)=>{const a=t(r);return a&&(o[a]=n[r]),o},{}),e,i]}let vs=null;function Fy(){return vs||(vs=D.fromMillis(1555555555555)),vs}function Ny(n,t){if(n.literal)return n;const e=ct.macroTokenToFormatOpts(n.val),i=eh(e,t);return i==null||i.includes(void 0)?n:i}function Kc(n,t){return Array.prototype.concat(...n.map(e=>Ny(e,t)))}class Qc{constructor(t,e){if(this.locale=t,this.format=e,this.tokens=Kc(ct.parseFormat(e),t),this.units=this.tokens.map(i=>Py(i,t)),this.disqualifyingUnit=this.units.find(i=>i.invalidReason),!this.disqualifyingUnit){const[i,s]=Iy(this.units);this.regex=RegExp(i,"i"),this.handlers=s}}explainFromTokens(t){if(this.isValid){const[e,i]=Ly(t,this.regex,this.handlers),[s,o,r]=i?Ry(i):[null,null,void 0];if(Ue(i,"a")&&Ue(i,"H"))throw new ze("Can't include meridiem when specifying 24-hour format");return{input:t,tokens:this.tokens,regex:this.regex,rawMatches:e,matches:i,result:s,zone:o,specificOffset:r}}else return{input:t,tokens:this.tokens,invalidReason:this.invalidReason}}get isValid(){return!this.disqualifyingUnit}get invalidReason(){return this.disqualifyingUnit?this.disqualifyingUnit.invalidReason:null}}function th(n,t,e){return new Qc(n,e).explainFromTokens(t)}function zy(n,t,e){const{result:i,zone:s,specificOffset:o,invalidReason:r}=th(n,t,e);return[i,s,o,r]}function eh(n,t){if(!n)return null;const i=ct.create(t,n).dtFormatter(Fy()),s=i.formatToParts(),o=i.resolvedOptions();return s.map(r=>Ey(r,n,o))}const ks="Invalid DateTime",Ea=864e13;function bn(n){return new Pt("unsupported zone",`the zone "${n.name}" is not supported`)}function Ss(n){return n.weekData===null&&(n.weekData=Wi(n.c)),n.weekData}function Ms(n){return n.localWeekData===null&&(n.localWeekData=Wi(n.c,n.loc.getMinDaysInFirstWeek(),n.loc.getStartOfWeek())),n.localWeekData}function ye(n,t){const e={ts:n.ts,zone:n.zone,c:n.c,o:n.o,loc:n.loc,invalid:n.invalid};return new D({...e,...t,old:e})}function nh(n,t,e){let i=n-t*60*1e3;const s=e.offset(i);if(t===s)return[i,t];i-=(s-t)*60*1e3;const o=e.offset(i);return s===o?[i,s]:[n-Math.min(s,o)*60*1e3,Math.max(s,o)]}function fi(n,t){n+=t*60*1e3;const e=new Date(n);return{year:e.getUTCFullYear(),month:e.getUTCMonth()+1,day:e.getUTCDate(),hour:e.getUTCHours(),minute:e.getUTCMinutes(),second:e.getUTCSeconds(),millisecond:e.getUTCMilliseconds()}}function Ti(n,t,e){return nh(qi(n),t,e)}function Ia(n,t){const e=n.o,i=n.c.year+Math.trunc(t.years),s=n.c.month+Math.trunc(t.months)+Math.trunc(t.quarters)*3,o={...n.c,year:i,month:s,day:Math.min(n.c.day,Vi(i,s))+Math.trunc(t.days)+Math.trunc(t.weeks)*7},r=N.fromObject({years:t.years-Math.trunc(t.years),quarters:t.quarters-Math.trunc(t.quarters),months:t.months-Math.trunc(t.months),weeks:t.weeks-Math.trunc(t.weeks),days:t.days-Math.trunc(t.days),hours:t.hours,minutes:t.minutes,seconds:t.seconds,milliseconds:t.milliseconds}).as("milliseconds"),a=qi(o);let[l,c]=nh(a,e,n.zone);return r!==0&&(l+=r,c=n.zone.offset(l)),{ts:l,o:c}}function Fe(n,t,e,i,s,o){const{setZone:r,zone:a}=e;if(n&&Object.keys(n).length!==0||t){const l=t||a,c=D.fromObject(n,{...e,zone:l,specificOffset:o});return r?c:c.setZone(a)}else return D.invalid(new Pt("unparsable",`the input "${s}" can't be parsed as ${i}`))}function gi(n,t,e=!0){return n.isValid?ct.create(H.create("en-US"),{allowZ:e,forceSimple:!0}).formatDateTimeFromString(n,t):null}function Ts(n,t){const e=n.c.year>9999||n.c.year<0;let i="";return e&&n.c.year>=0&&(i+="+"),i+=et(n.c.year,e?6:4),t?(i+="-",i+=et(n.c.month),i+="-",i+=et(n.c.day)):(i+=et(n.c.month),i+=et(n.c.day)),i}function La(n,t,e,i,s,o){let r=et(n.c.hour);return t?(r+=":",r+=et(n.c.minute),(n.c.millisecond!==0||n.c.second!==0||!e)&&(r+=":")):r+=et(n.c.minute),(n.c.millisecond!==0||n.c.second!==0||!e)&&(r+=et(n.c.second),(n.c.millisecond!==0||!i)&&(r+=".",r+=et(n.c.millisecond,3))),s&&(n.isOffsetFixed&&n.offset===0&&!o?r+="Z":n.o<0?(r+="-",r+=et(Math.trunc(-n.o/60)),r+=":",r+=et(Math.trunc(-n.o%60))):(r+="+",r+=et(Math.trunc(n.o/60)),r+=":",r+=et(Math.trunc(n.o%60)))),o&&(r+="["+n.zone.ianaName+"]"),r}const ih={month:1,day:1,hour:0,minute:0,second:0,millisecond:0},Wy={weekNumber:1,weekday:1,hour:0,minute:0,second:0,millisecond:0},Vy={ordinal:1,hour:0,minute:0,second:0,millisecond:0},sh=["year","month","day","hour","minute","second","millisecond"],By=["weekYear","weekNumber","weekday","hour","minute","second","millisecond"],Hy=["year","ordinal","hour","minute","second","millisecond"];function $y(n){const t={year:"year",years:"year",month:"month",months:"month",day:"day",days:"day",hour:"hour",hours:"hour",minute:"minute",minutes:"minute",quarter:"quarter",quarters:"quarter",second:"second",seconds:"second",millisecond:"millisecond",milliseconds:"millisecond",weekday:"weekday",weekdays:"weekday",weeknumber:"weekNumber",weeksnumber:"weekNumber",weeknumbers:"weekNumber",weekyear:"weekYear",weekyears:"weekYear",ordinal:"ordinal"}[n.toLowerCase()];if(!t)throw new ic(n);return t}function Ra(n){switch(n.toLowerCase()){case"localweekday":case"localweekdays":return"localWeekday";case"localweeknumber":case"localweeknumbers":return"localWeekNumber";case"localweekyear":case"localweekyears":return"localWeekYear";default:return $y(n)}}function jy(n){if(xn===void 0&&(xn=K.now()),n.type!=="iana")return n.offset(xn);const t=n.name;let e=to.get(t);return e===void 0&&(e=n.offset(xn),to.set(t,e)),e}function Fa(n,t){const e=Jt(t.zone,K.defaultZone);if(!e.isValid)return D.invalid(bn(e));const i=H.fromObject(t);let s,o;if(P(n.year))s=K.now();else{for(const l of sh)P(n[l])&&(n[l]=ih[l]);const r=Cc(n)||Pc(n);if(r)return D.invalid(r);const a=jy(e);[s,o]=Ti(n,a,e)}return new D({ts:s,zone:e,loc:i,o})}function Na(n,t,e){const i=P(e.round)?!0:e.round,s=(r,a)=>(r=Eo(r,i||e.calendary?0:2,!0),t.loc.clone(e).relFormatter(e).format(r,a)),o=r=>e.calendary?t.hasSame(n,r)?0:t.startOf(r).diff(n.startOf(r),r).get(r):t.diff(n,r).get(r);if(e.unit)return s(o(e.unit),e.unit);for(const r of e.units){const a=o(r);if(Math.abs(a)>=1)return s(a,r)}return s(n>t?-0:0,e.units[e.units.length-1])}function za(n){let t={},e;return n.length>0&&typeof n[n.length-1]=="object"?(t=n[n.length-1],e=Array.from(n).slice(0,n.length-1)):e=Array.from(n),[t,e]}let xn;const to=new Map;class D{constructor(t){const e=t.zone||K.defaultZone;let i=t.invalid||(Number.isNaN(t.ts)?new Pt("invalid input"):null)||(e.isValid?null:bn(e));this.ts=P(t.ts)?K.now():t.ts;let s=null,o=null;if(!i)if(t.old&&t.old.ts===this.ts&&t.old.zone.equals(e))[s,o]=[t.old.c,t.old.o];else{const a=oe(t.o)&&!t.old?t.o:e.offset(this.ts);s=fi(this.ts,a),i=Number.isNaN(s.year)?new Pt("invalid input"):null,s=i?null:s,o=i?null:a}this._zone=e,this.loc=t.loc||H.create(),this.invalid=i,this.weekData=null,this.localWeekData=null,this.c=s,this.o=o,this.isLuxonDateTime=!0}static now(){return new D({})}static local(){const[t,e]=za(arguments),[i,s,o,r,a,l,c]=e;return Fa({year:i,month:s,day:o,hour:r,minute:a,second:l,millisecond:c},t)}static utc(){const[t,e]=za(arguments),[i,s,o,r,a,l,c]=e;return t.zone=ut.utcInstance,Fa({year:i,month:s,day:o,hour:r,minute:a,second:l,millisecond:c},t)}static fromJSDate(t,e={}){const i=_p(t)?t.valueOf():NaN;if(Number.isNaN(i))return D.invalid("invalid input");const s=Jt(e.zone,K.defaultZone);return s.isValid?new D({ts:i,zone:s,loc:H.fromObject(e)}):D.invalid(bn(s))}static fromMillis(t,e={}){if(oe(t))return t<-Ea||t>Ea?D.invalid("Timestamp out of range"):new D({ts:t,zone:Jt(e.zone,K.defaultZone),loc:H.fromObject(e)});throw new lt(`fromMillis requires a numerical input, but received a ${typeof t} with value ${t}`)}static fromSeconds(t,e={}){if(oe(t))return new D({ts:t*1e3,zone:Jt(e.zone,K.defaultZone),loc:H.fromObject(e)});throw new lt("fromSeconds requires a numerical input")}static fromObject(t,e={}){t=t||{};const i=Jt(e.zone,K.defaultZone);if(!i.isValid)return D.invalid(bn(i));const s=H.fromObject(e),o=Bi(t,Ra),{minDaysInFirstWeek:r,startOfWeek:a}=va(o,s),l=K.now(),c=P(e.specificOffset)?i.offset(l):e.specificOffset,h=!P(o.ordinal),u=!P(o.year),d=!P(o.month)||!P(o.day),f=u||d,g=o.weekYear||o.weekNumber;if((f||h)&&g)throw new ze("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(d&&h)throw new ze("Can't mix ordinal dates with month/day");const m=g||o.weekday&&!f;let p,y,b=fi(l,c);m?(p=By,y=Wy,b=Wi(b,r,a)):h?(p=Hy,y=Vy,b=ws(b)):(p=sh,y=ih);let _=!1;for(const A of p){const L=o[A];P(L)?_?o[A]=y[A]:o[A]=b[A]:_=!0}const w=m?yp(o,r,a):h?bp(o):Cc(o),x=w||Pc(o);if(x)return D.invalid(x);const k=m?_a(o,r,a):h?wa(o):o,[S,M]=Ti(k,c,i),O=new D({ts:S,zone:i,o:M,loc:s});return o.weekday&&f&&t.weekday!==O.weekday?D.invalid("mismatched weekday",`you can't specify both a weekday of ${o.weekday} and a date of ${O.toISO()}`):O.isValid?O:D.invalid(O.invalid)}static fromISO(t,e={}){const[i,s]=cy(t);return Fe(i,s,e,"ISO 8601",t)}static fromRFC2822(t,e={}){const[i,s]=hy(t);return Fe(i,s,e,"RFC 2822",t)}static fromHTTP(t,e={}){const[i,s]=uy(t);return Fe(i,s,e,"HTTP",e)}static fromFormat(t,e,i={}){if(P(t)||P(e))throw new lt("fromFormat requires an input string and a format");const{locale:s=null,numberingSystem:o=null}=i,r=H.fromOpts({locale:s,numberingSystem:o,defaultToEN:!0}),[a,l,c,h]=zy(r,t,e);return h?D.invalid(h):Fe(a,l,i,`format ${e}`,t,c)}static fromString(t,e,i={}){return D.fromFormat(t,e,i)}static fromSQL(t,e={}){const[i,s]=by(t);return Fe(i,s,e,"SQL",t)}static invalid(t,e=null){if(!t)throw new lt("need to specify a reason the DateTime is invalid");const i=t instanceof Pt?t:new Pt(t,e);if(K.throwOnInvalid)throw new Ym(i);return new D({invalid:i})}static isDateTime(t){return t&&t.isLuxonDateTime||!1}static parseFormatForOpts(t,e={}){const i=eh(t,H.fromObject(e));return i?i.map(s=>s?s.val:null).join(""):null}static expandFormat(t,e={}){return Kc(ct.parseFormat(t),H.fromObject(e)).map(s=>s.val).join("")}static resetCache(){xn=void 0,to.clear()}get(t){return this[t]}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}get outputCalendar(){return this.isValid?this.loc.outputCalendar:null}get zone(){return this._zone}get zoneName(){return this.isValid?this.zone.name:null}get year(){return this.isValid?this.c.year:NaN}get quarter(){return this.isValid?Math.ceil(this.c.month/3):NaN}get month(){return this.isValid?this.c.month:NaN}get day(){return this.isValid?this.c.day:NaN}get hour(){return this.isValid?this.c.hour:NaN}get minute(){return this.isValid?this.c.minute:NaN}get second(){return this.isValid?this.c.second:NaN}get millisecond(){return this.isValid?this.c.millisecond:NaN}get weekYear(){return this.isValid?Ss(this).weekYear:NaN}get weekNumber(){return this.isValid?Ss(this).weekNumber:NaN}get weekday(){return this.isValid?Ss(this).weekday:NaN}get isWeekend(){return this.isValid&&this.loc.getWeekendDays().includes(this.weekday)}get localWeekday(){return this.isValid?Ms(this).weekday:NaN}get localWeekNumber(){return this.isValid?Ms(this).weekNumber:NaN}get localWeekYear(){return this.isValid?Ms(this).weekYear:NaN}get ordinal(){return this.isValid?ws(this.c).ordinal:NaN}get monthShort(){return this.isValid?ui.months("short",{locObj:this.loc})[this.month-1]:null}get monthLong(){return this.isValid?ui.months("long",{locObj:this.loc})[this.month-1]:null}get weekdayShort(){return this.isValid?ui.weekdays("short",{locObj:this.loc})[this.weekday-1]:null}get weekdayLong(){return this.isValid?ui.weekdays("long",{locObj:this.loc})[this.weekday-1]:null}get offset(){return this.isValid?+this.o:NaN}get offsetNameShort(){return this.isValid?this.zone.offsetName(this.ts,{format:"short",locale:this.locale}):null}get offsetNameLong(){return this.isValid?this.zone.offsetName(this.ts,{format:"long",locale:this.locale}):null}get isOffsetFixed(){return this.isValid?this.zone.isUniversal:null}get isInDST(){return this.isOffsetFixed?!1:this.offset>this.set({month:1,day:1}).offset||this.offset>this.set({month:5}).offset}getPossibleOffsets(){if(!this.isValid||this.isOffsetFixed)return[this];const t=864e5,e=6e4,i=qi(this.c),s=this.zone.offset(i-t),o=this.zone.offset(i+t),r=this.zone.offset(i-s*e),a=this.zone.offset(i-o*e);if(r===a)return[this];const l=i-r*e,c=i-a*e,h=fi(l,r),u=fi(c,a);return h.hour===u.hour&&h.minute===u.minute&&h.second===u.second&&h.millisecond===u.millisecond?[ye(this,{ts:l}),ye(this,{ts:c})]:[this]}get isInLeapYear(){return $n(this.year)}get daysInMonth(){return Vi(this.year,this.month)}get daysInYear(){return this.isValid?Ve(this.year):NaN}get weeksInWeekYear(){return this.isValid?Fn(this.weekYear):NaN}get weeksInLocalWeekYear(){return this.isValid?Fn(this.localWeekYear,this.loc.getMinDaysInFirstWeek(),this.loc.getStartOfWeek()):NaN}resolvedLocaleOptions(t={}){const{locale:e,numberingSystem:i,calendar:s}=ct.create(this.loc.clone(t),t).resolvedOptions(this);return{locale:e,numberingSystem:i,outputCalendar:s}}toUTC(t=0,e={}){return this.setZone(ut.instance(t),e)}toLocal(){return this.setZone(K.defaultZone)}setZone(t,{keepLocalTime:e=!1,keepCalendarTime:i=!1}={}){if(t=Jt(t,K.defaultZone),t.equals(this.zone))return this;if(t.isValid){let s=this.ts;if(e||i){const o=t.offset(this.ts),r=this.toObject();[s]=Ti(r,o,t)}return ye(this,{ts:s,zone:t})}else return D.invalid(bn(t))}reconfigure({locale:t,numberingSystem:e,outputCalendar:i}={}){const s=this.loc.clone({locale:t,numberingSystem:e,outputCalendar:i});return ye(this,{loc:s})}setLocale(t){return this.reconfigure({locale:t})}set(t){if(!this.isValid)return this;const e=Bi(t,Ra),{minDaysInFirstWeek:i,startOfWeek:s}=va(e,this.loc),o=!P(e.weekYear)||!P(e.weekNumber)||!P(e.weekday),r=!P(e.ordinal),a=!P(e.year),l=!P(e.month)||!P(e.day),c=a||l,h=e.weekYear||e.weekNumber;if((c||r)&&h)throw new ze("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(l&&r)throw new ze("Can't mix ordinal dates with month/day");let u;o?u=_a({...Wi(this.c,i,s),...e},i,s):P(e.ordinal)?(u={...this.toObject(),...e},P(e.day)&&(u.day=Math.min(Vi(u.year,u.month),u.day))):u=wa({...ws(this.c),...e});const[d,f]=Ti(u,this.o,this.zone);return ye(this,{ts:d,o:f})}plus(t){if(!this.isValid)return this;const e=N.fromDurationLike(t);return ye(this,Ia(this,e))}minus(t){if(!this.isValid)return this;const e=N.fromDurationLike(t).negate();return ye(this,Ia(this,e))}startOf(t,{useLocaleWeeks:e=!1}={}){if(!this.isValid)return this;const i={},s=N.normalizeUnit(t);switch(s){case"years":i.month=1;case"quarters":case"months":i.day=1;case"weeks":case"days":i.hour=0;case"hours":i.minute=0;case"minutes":i.second=0;case"seconds":i.millisecond=0;break}if(s==="weeks")if(e){const o=this.loc.getStartOfWeek(),{weekday:r}=this;r<o&&(i.weekNumber=this.weekNumber-1),i.weekday=o}else i.weekday=1;if(s==="quarters"){const o=Math.ceil(this.month/3);i.month=(o-1)*3+1}return this.set(i)}endOf(t,e){return this.isValid?this.plus({[t]:1}).startOf(t,e).minus(1):this}toFormat(t,e={}){return this.isValid?ct.create(this.loc.redefaultToEN(e)).formatDateTimeFromString(this,t):ks}toLocaleString(t=zi,e={}){return this.isValid?ct.create(this.loc.clone(e),t).formatDateTime(this):ks}toLocaleParts(t={}){return this.isValid?ct.create(this.loc.clone(t),t).formatDateTimeParts(this):[]}toISO({format:t="extended",suppressSeconds:e=!1,suppressMilliseconds:i=!1,includeOffset:s=!0,extendedZone:o=!1}={}){if(!this.isValid)return null;const r=t==="extended";let a=Ts(this,r);return a+="T",a+=La(this,r,e,i,s,o),a}toISODate({format:t="extended"}={}){return this.isValid?Ts(this,t==="extended"):null}toISOWeekDate(){return gi(this,"kkkk-'W'WW-c")}toISOTime({suppressMilliseconds:t=!1,suppressSeconds:e=!1,includeOffset:i=!0,includePrefix:s=!1,extendedZone:o=!1,format:r="extended"}={}){return this.isValid?(s?"T":"")+La(this,r==="extended",e,t,i,o):null}toRFC2822(){return gi(this,"EEE, dd LLL yyyy HH:mm:ss ZZZ",!1)}toHTTP(){return gi(this.toUTC(),"EEE, dd LLL yyyy HH:mm:ss 'GMT'")}toSQLDate(){return this.isValid?Ts(this,!0):null}toSQLTime({includeOffset:t=!0,includeZone:e=!1,includeOffsetSpace:i=!0}={}){let s="HH:mm:ss.SSS";return(e||t)&&(i&&(s+=" "),e?s+="z":t&&(s+="ZZ")),gi(this,s,!0)}toSQL(t={}){return this.isValid?`${this.toSQLDate()} ${this.toSQLTime(t)}`:null}toString(){return this.isValid?this.toISO():ks}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`DateTime { ts: ${this.toISO()}, zone: ${this.zone.name}, locale: ${this.locale} }`:`DateTime { Invalid, reason: ${this.invalidReason} }`}valueOf(){return this.toMillis()}toMillis(){return this.isValid?this.ts:NaN}toSeconds(){return this.isValid?this.ts/1e3:NaN}toUnixInteger(){return this.isValid?Math.floor(this.ts/1e3):NaN}toJSON(){return this.toISO()}toBSON(){return this.toJSDate()}toObject(t={}){if(!this.isValid)return{};const e={...this.c};return t.includeConfig&&(e.outputCalendar=this.outputCalendar,e.numberingSystem=this.loc.numberingSystem,e.locale=this.loc.locale),e}toJSDate(){return new Date(this.isValid?this.ts:NaN)}diff(t,e="milliseconds",i={}){if(!this.isValid||!t.isValid)return N.invalid("created by diffing an invalid DateTime");const s={locale:this.locale,numberingSystem:this.numberingSystem,...i},o=wp(e).map(N.normalizeUnit),r=t.valueOf()>this.valueOf(),a=r?this:t,l=r?t:this,c=My(a,l,o,s);return r?c.negate():c}diffNow(t="milliseconds",e={}){return this.diff(D.now(),t,e)}until(t){return this.isValid?J.fromDateTimes(this,t):this}hasSame(t,e,i){if(!this.isValid)return!1;const s=t.valueOf(),o=this.setZone(t.zone,{keepLocalTime:!0});return o.startOf(e,i)<=s&&s<=o.endOf(e,i)}equals(t){return this.isValid&&t.isValid&&this.valueOf()===t.valueOf()&&this.zone.equals(t.zone)&&this.loc.equals(t.loc)}toRelative(t={}){if(!this.isValid)return null;const e=t.base||D.fromObject({},{zone:this.zone}),i=t.padding?this<e?-t.padding:t.padding:0;let s=["years","months","days","hours","minutes","seconds"],o=t.unit;return Array.isArray(t.unit)&&(s=t.unit,o=void 0),Na(e,this.plus(i),{...t,numeric:"always",units:s,unit:o})}toRelativeCalendar(t={}){return this.isValid?Na(t.base||D.fromObject({},{zone:this.zone}),this,{...t,numeric:"auto",units:["years","months","days"],calendary:!0}):null}static min(...t){if(!t.every(D.isDateTime))throw new lt("min requires all arguments be DateTimes");return ka(t,e=>e.valueOf(),Math.min)}static max(...t){if(!t.every(D.isDateTime))throw new lt("max requires all arguments be DateTimes");return ka(t,e=>e.valueOf(),Math.max)}static fromFormatExplain(t,e,i={}){const{locale:s=null,numberingSystem:o=null}=i,r=H.fromOpts({locale:s,numberingSystem:o,defaultToEN:!0});return th(r,t,e)}static fromStringExplain(t,e,i={}){return D.fromFormatExplain(t,e,i)}static buildFormatParser(t,e={}){const{locale:i=null,numberingSystem:s=null}=e,o=H.fromOpts({locale:i,numberingSystem:s,defaultToEN:!0});return new Qc(o,t)}static fromFormatParser(t,e,i={}){if(P(t)||P(e))throw new lt("fromFormatParser requires an input string and a format parser");const{locale:s=null,numberingSystem:o=null}=i,r=H.fromOpts({locale:s,numberingSystem:o,defaultToEN:!0});if(!r.equals(e.locale))throw new lt(`fromFormatParser called with a locale of ${r}, but the format parser was created for ${e.locale}`);const{result:a,zone:l,specificOffset:c,invalidReason:h}=e.explainFromTokens(t);return h?D.invalid(h):Fe(a,l,i,`format ${e.format}`,t,c)}static get DATE_SHORT(){return zi}static get DATE_MED(){return sc}static get DATE_MED_WITH_WEEKDAY(){return Zm}static get DATE_FULL(){return oc}static get DATE_HUGE(){return rc}static get TIME_SIMPLE(){return ac}static get TIME_WITH_SECONDS(){return lc}static get TIME_WITH_SHORT_OFFSET(){return cc}static get TIME_WITH_LONG_OFFSET(){return hc}static get TIME_24_SIMPLE(){return uc}static get TIME_24_WITH_SECONDS(){return dc}static get TIME_24_WITH_SHORT_OFFSET(){return fc}static get TIME_24_WITH_LONG_OFFSET(){return gc}static get DATETIME_SHORT(){return mc}static get DATETIME_SHORT_WITH_SECONDS(){return pc}static get DATETIME_MED(){return yc}static get DATETIME_MED_WITH_SECONDS(){return bc}static get DATETIME_MED_WITH_WEEKDAY(){return qm}static get DATETIME_FULL(){return xc}static get DATETIME_FULL_WITH_SECONDS(){return _c}static get DATETIME_HUGE(){return wc}static get DATETIME_HUGE_WITH_SECONDS(){return vc}}function cn(n){if(D.isDateTime(n))return n;if(n&&n.valueOf&&oe(n.valueOf()))return D.fromJSDate(n);if(n&&typeof n=="object")return D.fromObject(n);throw new lt(`Unknown datetime argument: ${n}, of type ${typeof n}`)}/*!
 * chartjs-adapter-luxon v1.3.1
 * https://www.chartjs.org
 * (c) 2023 chartjs-adapter-luxon Contributors
 * Released under the MIT license
 */const Yy={datetime:D.DATETIME_MED_WITH_SECONDS,millisecond:"h:mm:ss.SSS a",second:D.TIME_WITH_SECONDS,minute:D.TIME_SIMPLE,hour:{hour:"numeric"},day:{day:"numeric",month:"short"},week:"DD",month:{month:"short",year:"numeric"},quarter:"'Q'q - yyyy",year:{year:"numeric"}};Fl._date.override({_id:"luxon",_create:function(n){return D.fromMillis(n,this.options)},init(n){this.options.locale||(this.options.locale=n.locale)},formats:function(){return Yy},parse:function(n,t){const e=this.options,i=typeof n;return n===null||i==="undefined"?null:(i==="number"?n=this._create(n):i==="string"?typeof t=="string"?n=D.fromFormat(n,t,e):n=D.fromISO(n,e):n instanceof Date?n=D.fromJSDate(n,e):i==="object"&&!(n instanceof D)&&(n=D.fromObject(n,e)),n.isValid?n.valueOf():null)},format:function(n,t){const e=this._create(n);return typeof t=="string"?e.toFormat(t):e.toLocaleString(t)},add:function(n,t,e){const i={};return i[e]=t,this._create(n).plus(i).valueOf()},diff:function(n,t,e){return this._create(n).diff(this._create(t)).as(e).valueOf()},startOf:function(n,t,e){if(t==="isoWeek"){e=Math.trunc(Math.min(Math.max(0,e),6));const i=this._create(n);return i.minus({days:(i.weekday-e+7)%7}).startOf("day").valueOf()}return t?this._create(n).startOf(t).valueOf():n},endOf:function(n,t){return this._create(n).endOf(t).valueOf()}});/*!
 * chartjs-plugin-datalabels v2.2.0
 * https://chartjs-plugin-datalabels.netlify.app
 * (c) 2017-2022 chartjs-plugin-datalabels contributors
 * Released under the MIT license
 */var Wa=function(){if(typeof window<"u"){if(window.devicePixelRatio)return window.devicePixelRatio;var n=window.screen;if(n)return(n.deviceXDPI||1)/(n.logicalXDPI||1)}return 1}(),Cn={toTextLines:function(n){var t=[],e;for(n=[].concat(n);n.length;)e=n.pop(),typeof e=="string"?t.unshift.apply(t,e.split(`
`)):Array.isArray(e)?n.push.apply(n,e):I(n)||t.unshift(""+e);return t},textSize:function(n,t,e){var i=[].concat(t),s=i.length,o=n.font,r=0,a;for(n.font=e.string,a=0;a<s;++a)r=Math.max(n.measureText(i[a]).width,r);return n.font=o,{height:s*e.lineHeight,width:r}},bound:function(n,t,e){return Math.max(n,Math.min(t,e))},arrayDiff:function(n,t){var e=n.slice(),i=[],s,o,r,a;for(s=0,r=t.length;s<r;++s)a=t[s],o=e.indexOf(a),o===-1?i.push([a,1]):e.splice(o,1);for(s=0,r=e.length;s<r;++s)i.push([e[s],-1]);return i},rasterize:function(n){return Math.round(n*Wa)/Wa}};function Os(n,t){var e=t.x,i=t.y;if(e===null)return{x:0,y:-1};if(i===null)return{x:1,y:0};var s=n.x-e,o=n.y-i,r=Math.sqrt(s*s+o*o);return{x:r?s/r:0,y:r?o/r:-1}}function Uy(n,t,e,i,s){switch(s){case"center":e=i=0;break;case"bottom":e=0,i=1;break;case"right":e=1,i=0;break;case"left":e=-1,i=0;break;case"top":e=0,i=-1;break;case"start":e=-e,i=-i;break;case"end":break;default:s*=Math.PI/180,e=Math.cos(s),i=Math.sin(s);break}return{x:n,y:t,vx:e,vy:i}}var Xy=0,oh=1,rh=2,ah=4,lh=8;function mi(n,t,e){var i=Xy;return n<e.left?i|=oh:n>e.right&&(i|=rh),t<e.top?i|=lh:t>e.bottom&&(i|=ah),i}function Zy(n,t){for(var e=n.x0,i=n.y0,s=n.x1,o=n.y1,r=mi(e,i,t),a=mi(s,o,t),l,c,h;!(!(r|a)||r&a);)l=r||a,l&lh?(c=e+(s-e)*(t.top-i)/(o-i),h=t.top):l&ah?(c=e+(s-e)*(t.bottom-i)/(o-i),h=t.bottom):l&rh?(h=i+(o-i)*(t.right-e)/(s-e),c=t.right):l&oh&&(h=i+(o-i)*(t.left-e)/(s-e),c=t.left),l===r?(e=c,i=h,r=mi(e,i,t)):(s=c,o=h,a=mi(s,o,t));return{x0:e,x1:s,y0:i,y1:o}}function pi(n,t){var e=t.anchor,i=n,s,o;return t.clamp&&(i=Zy(i,t.area)),e==="start"?(s=i.x0,o=i.y0):e==="end"?(s=i.x1,o=i.y1):(s=(i.x0+i.x1)/2,o=(i.y0+i.y1)/2),Uy(s,o,n.vx,n.vy,t.align)}var yi={arc:function(n,t){var e=(n.startAngle+n.endAngle)/2,i=Math.cos(e),s=Math.sin(e),o=n.innerRadius,r=n.outerRadius;return pi({x0:n.x+i*o,y0:n.y+s*o,x1:n.x+i*r,y1:n.y+s*r,vx:i,vy:s},t)},point:function(n,t){var e=Os(n,t.origin),i=e.x*n.options.radius,s=e.y*n.options.radius;return pi({x0:n.x-i,y0:n.y-s,x1:n.x+i,y1:n.y+s,vx:e.x,vy:e.y},t)},bar:function(n,t){var e=Os(n,t.origin),i=n.x,s=n.y,o=0,r=0;return n.horizontal?(i=Math.min(n.x,n.base),o=Math.abs(n.base-n.x)):(s=Math.min(n.y,n.base),r=Math.abs(n.base-n.y)),pi({x0:i,y0:s+r,x1:i+o,y1:s,vx:e.x,vy:e.y},t)},fallback:function(n,t){var e=Os(n,t.origin);return pi({x0:n.x,y0:n.y,x1:n.x+(n.width||0),y1:n.y+(n.height||0),vx:e.x,vy:e.y},t)}},jt=Cn.rasterize;function qy(n){var t=n.borderWidth||0,e=n.padding,i=n.size.height,s=n.size.width,o=-s/2,r=-i/2;return{frame:{x:o-e.left-t,y:r-e.top-t,w:s+e.width+t*2,h:i+e.height+t*2},text:{x:o,y:r,w:s,h:i}}}function Gy(n,t){var e=t.chart.getDatasetMeta(t.datasetIndex).vScale;if(!e)return null;if(e.xCenter!==void 0&&e.yCenter!==void 0)return{x:e.xCenter,y:e.yCenter};var i=e.getBasePixel();return n.horizontal?{x:i,y:null}:{x:null,y:i}}function Jy(n){return n instanceof Ne?yi.arc:n instanceof Tn?yi.point:n instanceof On?yi.bar:yi.fallback}function Ky(n,t,e,i,s,o){var r=Math.PI/2;if(o){var a=Math.min(o,s/2,i/2),l=t+a,c=e+a,h=t+i-a,u=e+s-a;n.moveTo(t,c),l<h&&c<u?(n.arc(l,c,a,-Math.PI,-r),n.arc(h,c,a,-r,0),n.arc(h,u,a,0,r),n.arc(l,u,a,r,Math.PI)):l<h?(n.moveTo(l,e),n.arc(h,c,a,-r,r),n.arc(l,c,a,r,Math.PI+r)):c<u?(n.arc(l,c,a,-Math.PI,0),n.arc(l,u,a,0,Math.PI)):n.arc(l,c,a,-Math.PI,Math.PI),n.closePath(),n.moveTo(t,e)}else n.rect(t,e,i,s)}function Qy(n,t,e){var i=e.backgroundColor,s=e.borderColor,o=e.borderWidth;!i&&(!s||!o)||(n.beginPath(),Ky(n,jt(t.x)+o/2,jt(t.y)+o/2,jt(t.w)-o,jt(t.h)-o,e.borderRadius),n.closePath(),i&&(n.fillStyle=i,n.fill()),s&&o&&(n.strokeStyle=s,n.lineWidth=o,n.lineJoin="miter",n.stroke()))}function tb(n,t,e){var i=e.lineHeight,s=n.w,o=n.x,r=n.y+i/2;return t==="center"?o+=s/2:(t==="end"||t==="right")&&(o+=s),{h:i,w:s,x:o,y:r}}function eb(n,t,e){var i=n.shadowBlur,s=e.stroked,o=jt(e.x),r=jt(e.y),a=jt(e.w);s&&n.strokeText(t,o,r,a),e.filled&&(i&&s&&(n.shadowBlur=0),n.fillText(t,o,r,a),i&&s&&(n.shadowBlur=i))}function nb(n,t,e,i){var s=i.textAlign,o=i.color,r=!!o,a=i.font,l=t.length,c=i.textStrokeColor,h=i.textStrokeWidth,u=c&&h,d;if(!(!l||!r&&!u))for(e=tb(e,s,a),n.font=a.string,n.textAlign=s,n.textBaseline="middle",n.shadowBlur=i.textShadowBlur,n.shadowColor=i.textShadowColor,r&&(n.fillStyle=o),u&&(n.lineJoin="round",n.lineWidth=h,n.strokeStyle=c),d=0,l=t.length;d<l;++d)eb(n,t[d],{stroked:u,filled:r,w:e.w,x:e.x,y:e.y+e.h*d})}var ch=function(n,t,e,i){var s=this;s._config=n,s._index=i,s._model=null,s._rects=null,s._ctx=t,s._el=e};Ft(ch.prototype,{_modelize:function(n,t,e,i){var s=this,o=s._index,r=G(U([e.font,{}],i,o)),a=U([e.color,Y.color],i,o);return{align:U([e.align,"center"],i,o),anchor:U([e.anchor,"center"],i,o),area:i.chart.chartArea,backgroundColor:U([e.backgroundColor,null],i,o),borderColor:U([e.borderColor,null],i,o),borderRadius:U([e.borderRadius,0],i,o),borderWidth:U([e.borderWidth,0],i,o),clamp:U([e.clamp,!1],i,o),clip:U([e.clip,!1],i,o),color:a,display:n,font:r,lines:t,offset:U([e.offset,4],i,o),opacity:U([e.opacity,1],i,o),origin:Gy(s._el,i),padding:tt(U([e.padding,4],i,o)),positioner:Jy(s._el),rotation:U([e.rotation,0],i,o)*(Math.PI/180),size:Cn.textSize(s._ctx,t,r),textAlign:U([e.textAlign,"start"],i,o),textShadowBlur:U([e.textShadowBlur,0],i,o),textShadowColor:U([e.textShadowColor,a],i,o),textStrokeColor:U([e.textStrokeColor,a],i,o),textStrokeWidth:U([e.textStrokeWidth,0],i,o)}},update:function(n){var t=this,e=null,i=null,s=t._index,o=t._config,r,a,l,c=U([o.display,!0],n,s);c&&(r=n.dataset.data[s],a=C(W(o.formatter,[r,n]),r),l=I(a)?[]:Cn.toTextLines(a),l.length&&(e=t._modelize(c,l,o,n),i=qy(e))),t._model=e,t._rects=i},geometry:function(){return this._rects?this._rects.frame:{}},rotation:function(){return this._model?this._model.rotation:0},visible:function(){return this._model&&this._model.opacity},model:function(){return this._model},draw:function(n,t){var e=this,i=n.ctx,s=e._model,o=e._rects,r;this.visible()&&(i.save(),s.clip&&(r=s.area,i.beginPath(),i.rect(r.left,r.top,r.right-r.left,r.bottom-r.top),i.clip()),i.globalAlpha=Cn.bound(0,s.opacity,1),i.translate(jt(t.x),jt(t.y)),i.rotate(s.rotation),Qy(i,o.frame,s),nb(i,s.lines,o.text,s),i.restore())}});var ib=Number.MIN_SAFE_INTEGER||-9007199254740991,sb=Number.MAX_SAFE_INTEGER||9007199254740991;function hn(n,t,e){var i=Math.cos(e),s=Math.sin(e),o=t.x,r=t.y;return{x:o+i*(n.x-o)-s*(n.y-r),y:r+s*(n.x-o)+i*(n.y-r)}}function Va(n,t){var e=sb,i=ib,s=t.origin,o,r,a,l,c;for(o=0;o<n.length;++o)r=n[o],a=r.x-s.x,l=r.y-s.y,c=t.vx*a+t.vy*l,e=Math.min(e,c),i=Math.max(i,c);return{min:e,max:i}}function bi(n,t){var e=t.x-n.x,i=t.y-n.y,s=Math.sqrt(e*e+i*i);return{vx:(t.x-n.x)/s,vy:(t.y-n.y)/s,origin:n,ln:s}}var hh=function(){this._rotation=0,this._rect={x:0,y:0,w:0,h:0}};Ft(hh.prototype,{center:function(){var n=this._rect;return{x:n.x+n.w/2,y:n.y+n.h/2}},update:function(n,t,e){this._rotation=e,this._rect={x:t.x+n.x,y:t.y+n.y,w:t.w,h:t.h}},contains:function(n){var t=this,e=1,i=t._rect;return n=hn(n,t.center(),-t._rotation),!(n.x<i.x-e||n.y<i.y-e||n.x>i.x+i.w+e*2||n.y>i.y+i.h+e*2)},intersects:function(n){var t=this._points(),e=n._points(),i=[bi(t[0],t[1]),bi(t[0],t[3])],s,o,r;for(this._rotation!==n._rotation&&i.push(bi(e[0],e[1]),bi(e[0],e[3])),s=0;s<i.length;++s)if(o=Va(t,i[s]),r=Va(e,i[s]),o.max<r.min||r.max<o.min)return!1;return!0},_points:function(){var n=this,t=n._rect,e=n._rotation,i=n.center();return[hn({x:t.x,y:t.y},i,e),hn({x:t.x+t.w,y:t.y},i,e),hn({x:t.x+t.w,y:t.y+t.h},i,e),hn({x:t.x,y:t.y+t.h},i,e)]}});function uh(n,t,e){var i=t.positioner(n,t),s=i.vx,o=i.vy;if(!s&&!o)return{x:i.x,y:i.y};var r=e.w,a=e.h,l=t.rotation,c=Math.abs(r/2*Math.cos(l))+Math.abs(a/2*Math.sin(l)),h=Math.abs(r/2*Math.sin(l))+Math.abs(a/2*Math.cos(l)),u=1/Math.max(Math.abs(s),Math.abs(o));return c*=s*u,h*=o*u,c+=t.offset*s,h+=t.offset*o,{x:i.x+c,y:i.y+h}}function ob(n,t){var e,i,s,o;for(e=n.length-1;e>=0;--e)for(s=n[e].$layout,i=e-1;i>=0&&s._visible;--i)o=n[i].$layout,o._visible&&s._box.intersects(o._box)&&t(s,o);return n}function rb(n){var t,e,i,s,o,r,a;for(t=0,e=n.length;t<e;++t)i=n[t],s=i.$layout,s._visible&&(a=new Proxy(i._el,{get:(l,c)=>l.getProps([c],!0)[c]}),o=i.geometry(),r=uh(a,i.model(),o),s._box.update(r,o,i.rotation()));return ob(n,function(l,c){var h=l._hidable,u=c._hidable;h&&u||u?c._visible=!1:h&&(l._visible=!1)})}var Pn={prepare:function(n){var t=[],e,i,s,o,r;for(e=0,s=n.length;e<s;++e)for(i=0,o=n[e].length;i<o;++i)r=n[e][i],t.push(r),r.$layout={_box:new hh,_hidable:!1,_visible:!0,_set:e,_idx:r._index};return t.sort(function(a,l){var c=a.$layout,h=l.$layout;return c._idx===h._idx?h._set-c._set:h._idx-c._idx}),this.update(t),t},update:function(n){var t=!1,e,i,s,o,r;for(e=0,i=n.length;e<i;++e)s=n[e],o=s.model(),r=s.$layout,r._hidable=o&&o.display==="auto",r._visible=s.visible(),t|=r._hidable;t&&rb(n)},lookup:function(n,t){var e,i;for(e=n.length-1;e>=0;--e)if(i=n[e].$layout,i&&i._visible&&i._box.contains(t))return n[e];return null},draw:function(n,t){var e,i,s,o,r,a;for(e=0,i=t.length;e<i;++e)s=t[e],o=s.$layout,o._visible&&(r=s.geometry(),a=uh(s._el,s.model(),r),o._box.update(a,r,s.rotation()),s.draw(n,a))}},ab=function(n){if(I(n))return null;var t=n,e,i,s;if(E(n))if(!I(n.label))t=n.label;else if(!I(n.r))t=n.r;else for(t="",e=Object.keys(n),s=0,i=e.length;s<i;++s)t+=(s!==0?", ":"")+e[s]+": "+n[e[s]];return""+t},lb={align:"center",anchor:"center",backgroundColor:null,borderColor:null,borderRadius:0,borderWidth:0,clamp:!1,clip:!1,color:void 0,display:!0,font:{family:void 0,lineHeight:1.2,size:void 0,style:void 0,weight:null},formatter:ab,labels:void 0,listeners:{},offset:4,opacity:1,padding:{top:4,right:4,bottom:4,left:4},rotation:0,textAlign:"start",textStrokeColor:void 0,textStrokeWidth:0,textShadowBlur:0,textShadowColor:void 0},ft="$datalabels",dh="$default";function cb(n,t){var e=n.datalabels,i={},s=[],o,r;return e===!1?null:(e===!0&&(e={}),t=Ft({},[t,e]),o=t.labels||{},r=Object.keys(o),delete t.labels,r.length?r.forEach(function(a){o[a]&&s.push(Ft({},[t,o[a],{_key:a}]))}):s.push(t),i=s.reduce(function(a,l){return B(l.listeners||{},function(c,h){a[h]=a[h]||{},a[h][l._key||dh]=c}),delete l.listeners,a},{}),{labels:s,listeners:i})}function eo(n,t,e,i){if(t){var s=e.$context,o=e.$groups,r;t[o._set]&&(r=t[o._set][o._key],r&&W(r,[s,i])===!0&&(n[ft]._dirty=!0,e.update(s)))}}function hb(n,t,e,i,s){var o,r;!e&&!i||(e?i?e!==i&&(r=o=!0):r=!0:o=!0,r&&eo(n,t.leave,e,s),o&&eo(n,t.enter,i,s))}function ub(n,t){var e=n[ft],i=e._listeners,s,o;if(!(!i.enter&&!i.leave)){if(t.type==="mousemove")o=Pn.lookup(e._labels,t);else if(t.type!=="mouseout")return;s=e._hovered,e._hovered=o,hb(n,i,s,o,t)}}function db(n,t){var e=n[ft],i=e._listeners.click,s=i&&Pn.lookup(e._labels,t);s&&eo(n,i,s,t)}var fb={id:"datalabels",defaults:lb,beforeInit:function(n){n[ft]={_actives:[]}},beforeUpdate:function(n){var t=n[ft];t._listened=!1,t._listeners={},t._datasets=[],t._labels=[]},afterDatasetUpdate:function(n,t,e){var i=t.index,s=n[ft],o=s._datasets[i]=[],r=n.isDatasetVisible(i),a=n.data.datasets[i],l=cb(a,e),c=t.meta.data||[],h=n.ctx,u,d,f,g,m,p,y,b;for(h.save(),u=0,f=c.length;u<f;++u)if(y=c[u],y[ft]=[],r&&y&&n.getDataVisibility(u)&&!y.skip)for(d=0,g=l.labels.length;d<g;++d)m=l.labels[d],p=m._key,b=new ch(m,h,y,u),b.$groups={_set:i,_key:p||dh},b.$context={active:!1,chart:n,dataIndex:u,dataset:a,datasetIndex:i},b.update(b.$context),y[ft].push(b),o.push(b);h.restore(),Ft(s._listeners,l.listeners,{merger:function(_,w,x){w[_]=w[_]||{},w[_][t.index]=x[_],s._listened=!0}})},afterUpdate:function(n){n[ft]._labels=Pn.prepare(n[ft]._datasets)},afterDatasetsDraw:function(n){Pn.draw(n,n[ft]._labels)},beforeEvent:function(n,t){if(n[ft]._listened){var e=t.event;switch(e.type){case"mousemove":case"mouseout":ub(n,e);break;case"click":db(n,e);break}}},afterEvent:function(n){var t=n[ft],e=t._actives,i=t._actives=n.getActiveElements(),s=Cn.arrayDiff(e,i),o,r,a,l,c,h,u;for(o=0,r=s.length;o<r;++o)if(c=s[o],c[1])for(u=c[0].element[ft]||[],a=0,l=u.length;a<l;++a)h=u[a],h.$context.active=c[1]===1,h.update(h.$context);(t._dirty||s.length)&&(Pn.update(t._labels),n.render()),delete t._dirty}};/*!
* chartjs-plugin-annotation v3.1.0
* https://www.chartjs.org/chartjs-plugin-annotation/index
 * (c) 2024 chartjs-plugin-annotation Contributors
 * Released under the MIT License
 */const Ba={modes:{point(n,t){return Oi(n,t,{intersect:!0})},nearest(n,t,e){return pb(n,t,e)},x(n,t,e){return Oi(n,t,{intersect:e.intersect,axis:"x"})},y(n,t,e){return Oi(n,t,{intersect:e.intersect,axis:"y"})}}};function Fo(n,t,e){return(Ba.modes[e.mode]||Ba.modes.nearest)(n,t,e)}function gb(n,t,e){return e!=="x"&&e!=="y"?n.inRange(t.x,t.y,"x",!0)||n.inRange(t.x,t.y,"y",!0):n.inRange(t.x,t.y,e,!0)}function mb(n,t,e){return e==="x"?{x:n.x,y:t.y}:e==="y"?{x:t.x,y:n.y}:t}function Oi(n,t,e){return n.filter(i=>e.intersect?i.inRange(t.x,t.y):gb(i,t,e.axis))}function pb(n,t,e){let i=Number.POSITIVE_INFINITY;return Oi(n,t,e).reduce((s,o)=>{const r=o.getCenterPoint(),a=mb(t,r,e.axis),l=He(t,a);return l<i?(s=[o],i=l):l===i&&s.push(o),s},[]).sort((s,o)=>s._index-o._index).slice(0,1)}function De(n,t,e){const i=Math.cos(e),s=Math.sin(e),o=t.x,r=t.y;return{x:o+i*(n.x-o)-s*(n.y-r),y:r+s*(n.x-o)+i*(n.y-r)}}const yb=(n,t)=>t>n||n.length>t.length&&n.slice(0,t.length)===t,we=.001,Ki=(n,t,e)=>Math.min(e,Math.max(t,n)),fh=(n,t)=>n.value>=n.start-t&&n.value<=n.end+t;function bb(n,t,e){for(const i of Object.keys(n))n[i]=Ki(n[i],t,e);return n}function xb(n,t,e,i){return!n||!t||e<=0?!1:Math.pow(n.x-t.x,2)+Math.pow(n.y-t.y,2)<=Math.pow(e+i,2)}function gh(n,{x:t,y:e,x2:i,y2:s},o,{borderWidth:r,hitTolerance:a}){const l=(r+a)/2,c=n.x>=t-l-we&&n.x<=i+l+we,h=n.y>=e-l-we&&n.y<=s+l+we;return o==="x"?c:(o==="y"||c)&&h}function mh(n,{rect:t,center:e},i,{rotation:s,borderWidth:o,hitTolerance:r}){const a=De(n,e,nt(-s));return gh(a,t,i,{borderWidth:o,hitTolerance:r})}function Ce(n,t){const{centerX:e,centerY:i}=n.getProps(["centerX","centerY"],t);return{x:e,y:i}}function _b(n,t,e,i=!0){const s=e.split(".");let o=0;for(const r of t.split(".")){const a=s[o++];if(parseInt(r,10)<parseInt(a,10))break;if(yb(a,r)){if(i)throw new Error(`${n} v${e} is not supported. v${t} or newer is required.`);return!1}}return!0}const ph=n=>typeof n=="string"&&n.endsWith("%"),yh=n=>parseFloat(n)/100,bh=n=>Ki(yh(n),0,1),un=(n,t)=>({x:n,y:t,x2:n,y2:t,width:0,height:0}),wb={box:n=>un(n.centerX,n.centerY),doughnutLabel:n=>un(n.centerX,n.centerY),ellipse:n=>({centerX:n.centerX,centerY:n.centerX,radius:0,width:0,height:0}),label:n=>un(n.centerX,n.centerY),line:n=>un(n.x,n.y),point:n=>({centerX:n.centerX,centerY:n.centerY,radius:0,width:0,height:0}),polygon:n=>un(n.centerX,n.centerY)};function No(n,t){return t==="start"?0:t==="end"?n:ph(t)?bh(t)*n:n/2}function ce(n,t,e=!0){return typeof t=="number"?t:ph(t)?(e?bh(t):yh(t))*n:n}function vb(n,t){const{x:e,width:i}=n,s=t.textAlign;return s==="center"?e+i/2:s==="end"||s==="right"?e+i:e}function xh(n,t,{borderWidth:e,position:i,xAdjust:s,yAdjust:o},r){const a=E(r),l=t.width+(a?r.width:0)+e,c=t.height+(a?r.height:0)+e,h=zo(i),u=Ha(n.x,l,s,h.x),d=Ha(n.y,c,o,h.y);return{x:u,y:d,x2:u+l,y2:d+c,width:l,height:c,centerX:u+l/2,centerY:d+c/2}}function zo(n,t="center"){return E(n)?{x:C(n.x,t),y:C(n.y,t)}:(n=C(n,t),{x:n,y:n})}const _h=(n,t)=>n&&n.autoFit&&t<1;function wh(n,t){const e=n.font,i=V(e)?e:[e];return _h(n,t)?i.map(function(s){const o=G(s);return o.size=Math.floor(s.size*t),o.lineHeight=s.lineHeight,G(o)}):i.map(s=>G(s))}function vh(n){return n&&(pt(n.xValue)||pt(n.yValue))}function Ha(n,t,e=0,i){return n-No(t,i)+e}function Ke(n,t,e){const i=e.init;if(i){if(i===!0)return Sh(t,e)}else return;return kb(n,t,e)}function kh(n,t,e){let i=!1;return t.forEach(s=>{mt(n[s])?(i=!0,e[s]=n[s]):pt(e[s])&&delete e[s]}),i}function Sh(n,t){const e=t.type||"line";return wb[e](n)}function kb(n,t,e){const i=W(e.init,[{chart:n,properties:t,options:e}]);if(i===!0)return Sh(t,e);if(E(i))return i}const Ds=new Map,Sb=n=>isNaN(n)||n<=0,Mb=n=>n.reduce(function(t,e){return t+=e.string,t},"");function Qi(n){if(n&&typeof n=="object"){const t=n.toString();return t==="[object HTMLImageElement]"||t==="[object HTMLCanvasElement]"}}function ts(n,{x:t,y:e},i){i&&(n.translate(t,e),n.rotate(nt(i)),n.translate(-t,-e))}function Ut(n,t){if(t&&t.borderWidth)return n.lineCap=t.borderCapStyle||"butt",n.setLineDash(t.borderDash),n.lineDashOffset=t.borderDashOffset,n.lineJoin=t.borderJoinStyle||"miter",n.lineWidth=t.borderWidth,n.strokeStyle=t.borderColor,!0}function Qe(n,t){n.shadowColor=t.backgroundShadowColor,n.shadowBlur=t.shadowBlur,n.shadowOffsetX=t.shadowOffsetX,n.shadowOffsetY=t.shadowOffsetY}function es(n,t){const e=t.content;if(Qi(e))return{width:ce(e.width,t.width),height:ce(e.height,t.height)};const i=wh(t),s=t.textStrokeWidth,o=V(e)?e:[e],r=o.join()+Mb(i)+s+(n._measureText?"-spriting":"");return Ds.has(r)||Ds.set(r,Cb(n,o,i,s)),Ds.get(r)}function Mh(n,t,e){const{x:i,y:s,width:o,height:r}=t;n.save(),Qe(n,e);const a=Ut(n,e);n.fillStyle=e.backgroundColor,n.beginPath(),$e(n,{x:i,y:s,w:o,h:r,radius:bb(se(e.borderRadius),0,Math.min(o,r)/2)}),n.closePath(),n.fill(),a&&(n.shadowColor=e.borderShadowColor,n.stroke()),n.restore()}function Th(n,t,e,i){const s=e.content;if(Qi(s)){n.save(),n.globalAlpha=Eb(e.opacity,s.style.opacity),n.drawImage(s,t.x,t.y,t.width,t.height),n.restore();return}const o=V(s)?s:[s],r=wh(e,i),a=e.color,l=V(a)?a:[a],c=vb(t,e),h=t.y+e.textStrokeWidth/2;n.save(),n.textBaseline="middle",n.textAlign=e.textAlign,Tb(n,e)&&Pb(n,{x:c,y:h},o,r),Ab(n,{x:c,y:h},o,{fonts:r,colors:l}),n.restore()}function Tb(n,t){if(t.textStrokeWidth>0)return n.lineJoin="round",n.miterLimit=2,n.lineWidth=t.textStrokeWidth,n.strokeStyle=t.textStrokeColor,!0}function Ob(n,t,e,i){const{radius:s,options:o}=t,r=o.pointStyle,a=o.rotation;let l=(a||0)*go;if(Qi(r)){n.save(),n.translate(e,i),n.rotate(l),n.drawImage(r,-r.width/2,-r.height/2,r.width,r.height),n.restore();return}Sb(s)||Db(n,{x:e,y:i,radius:s,rotation:a,style:r,rad:l})}function Db(n,{x:t,y:e,radius:i,rotation:s,style:o,rad:r}){let a,l,c,h;switch(n.beginPath(),o){default:n.arc(t,e,i,0,j),n.closePath();break;case"triangle":n.moveTo(t+Math.sin(r)*i,e-Math.cos(r)*i),r+=Ai,n.lineTo(t+Math.sin(r)*i,e-Math.cos(r)*i),r+=Ai,n.lineTo(t+Math.sin(r)*i,e-Math.cos(r)*i),n.closePath();break;case"rectRounded":h=i*.516,c=i-h,a=Math.cos(r+kt)*c,l=Math.sin(r+kt)*c,n.arc(t-a,e-l,h,r-R,r-X),n.arc(t+l,e-a,h,r-X,r),n.arc(t+a,e+l,h,r,r+X),n.arc(t-l,e+a,h,r+X,r+R),n.closePath();break;case"rect":if(!s){c=Math.SQRT1_2*i,n.rect(t-c,e-c,2*c,2*c);break}r+=kt;case"rectRot":a=Math.cos(r)*i,l=Math.sin(r)*i,n.moveTo(t-a,e-l),n.lineTo(t+l,e-a),n.lineTo(t+a,e+l),n.lineTo(t-l,e+a),n.closePath();break;case"crossRot":r+=kt;case"cross":a=Math.cos(r)*i,l=Math.sin(r)*i,n.moveTo(t-a,e-l),n.lineTo(t+a,e+l),n.moveTo(t+l,e-a),n.lineTo(t-l,e+a);break;case"star":a=Math.cos(r)*i,l=Math.sin(r)*i,n.moveTo(t-a,e-l),n.lineTo(t+a,e+l),n.moveTo(t+l,e-a),n.lineTo(t-l,e+a),r+=kt,a=Math.cos(r)*i,l=Math.sin(r)*i,n.moveTo(t-a,e-l),n.lineTo(t+a,e+l),n.moveTo(t+l,e-a),n.lineTo(t-l,e+a);break;case"line":a=Math.cos(r)*i,l=Math.sin(r)*i,n.moveTo(t-a,e-l),n.lineTo(t+a,e+l);break;case"dash":n.moveTo(t,e),n.lineTo(t+Math.cos(r)*i,e+Math.sin(r)*i);break}n.fill()}function Cb(n,t,e,i){n.save();const s=t.length;let o=0,r=i;for(let a=0;a<s;a++){const l=e[Math.min(a,e.length-1)];n.font=l.string;const c=t[a];o=Math.max(o,n.measureText(c).width+i),r+=l.lineHeight}return n.restore(),{width:o,height:r}}function Pb(n,{x:t,y:e},i,s){n.beginPath();let o=0;i.forEach(function(r,a){const l=s[Math.min(a,s.length-1)],c=l.lineHeight;n.font=l.string,n.strokeText(r,t,e+c/2+o),o+=c}),n.stroke()}function Ab(n,{x:t,y:e},i,{fonts:s,colors:o}){let r=0;i.forEach(function(a,l){const c=o[Math.min(l,o.length-1)],h=s[Math.min(l,s.length-1)],u=h.lineHeight;n.beginPath(),n.font=h.string,n.fillStyle=c,n.fillText(a,t,e+u/2+r),r+=u,n.fill()})}function Eb(n,t){const e=le(n)?n:t;return le(e)?Ki(e,0,1):1}const Oh=["left","bottom","top","right"];function Ib(n,t){const{pointX:e,pointY:i,options:s}=t,o=s.callout,r=o&&o.display&&zb(t,o);if(!r||Vb(t,o,r))return;if(n.save(),n.beginPath(),!Ut(n,o))return n.restore();const{separatorStart:l,separatorEnd:c}=Lb(t,r),{sideStart:h,sideEnd:u}=Fb(t,r,l);(o.margin>0||s.borderWidth===0)&&(n.moveTo(l.x,l.y),n.lineTo(c.x,c.y)),n.moveTo(h.x,h.y),n.lineTo(u.x,u.y);const d=De({x:e,y:i},t.getCenterPoint(),nt(-t.rotation));n.lineTo(d.x,d.y),n.stroke(),n.restore()}function Lb(n,t){const{x:e,y:i,x2:s,y2:o}=n,r=Rb(n,t);let a,l;return t==="left"||t==="right"?(a={x:e+r,y:i},l={x:a.x,y:o}):(a={x:e,y:i+r},l={x:s,y:a.y}),{separatorStart:a,separatorEnd:l}}function Rb(n,t){const{width:e,height:i,options:s}=n,o=s.callout.margin+s.borderWidth/2;return t==="right"?e+o:t==="bottom"?i+o:-o}function Fb(n,t,e){const{y:i,width:s,height:o,options:r}=n,a=r.callout.start,l=Nb(t,r.callout);let c,h;return t==="left"||t==="right"?(c={x:e.x,y:i+ce(o,a)},h={x:c.x+l,y:c.y}):(c={x:e.x+ce(s,a),y:e.y},h={x:c.x,y:c.y+l}),{sideStart:c,sideEnd:h}}function Nb(n,t){const e=t.side;return n==="left"||n==="top"?-e:e}function zb(n,t){const e=t.position;return Oh.includes(e)?e:Wb(n,t)}function Wb(n,t){const{x:e,y:i,x2:s,y2:o,width:r,height:a,pointX:l,pointY:c,centerX:h,centerY:u,rotation:d}=n,f={x:h,y:u},g=t.start,m=ce(r,g),p=ce(a,g),y=[e,e+m,e+m,s],b=[i+p,o,i,o],_=[];for(let w=0;w<4;w++){const x=De({x:y[w],y:b[w]},f,nt(d));_.push({position:Oh[w],distance:He(x,{x:l,y:c})})}return _.sort((w,x)=>w.distance-x.distance)[0].position}function Vb(n,t,e){const{pointX:i,pointY:s}=n,o=t.margin;let r=i,a=s;return e==="left"?r+=o:e==="right"?r-=o:e==="top"?a+=o:e==="bottom"&&(a-=o),n.inRange(r,a)}const $a={xScaleID:{min:"xMin",max:"xMax",start:"left",end:"right",startProp:"x",endProp:"x2"},yScaleID:{min:"yMin",max:"yMax",start:"bottom",end:"top",startProp:"y",endProp:"y2"}};function Xe(n,t,e){return t=typeof t=="number"?t:n.parse(t),q(t)?n.getPixelForValue(t):e}function Me(n,t,e){const i=t[e];if(i||e==="scaleID")return i;const s=e.charAt(0),o=Object.values(n).filter(r=>r.axis&&r.axis===s);return o.length?o[0].id:s}function Dh(n,t){if(n){const e=n.options.reverse,i=Xe(n,t.min,e?t.end:t.start),s=Xe(n,t.max,e?t.start:t.end);return{start:i,end:s}}}function Ch(n,t){const{chartArea:e,scales:i}=n,s=i[Me(i,t,"xScaleID")],o=i[Me(i,t,"yScaleID")];let r=e.width/2,a=e.height/2;return s&&(r=Xe(s,t.xValue,s.left+s.width/2)),o&&(a=Xe(o,t.yValue,o.top+o.height/2)),{x:r,y:a}}function Wo(n,t){const e=n.scales,i=e[Me(e,t,"xScaleID")],s=e[Me(e,t,"yScaleID")];if(!i&&!s)return{};let{left:o,right:r}=i||n.chartArea,{top:a,bottom:l}=s||n.chartArea;const c=ja(i,{min:t.xMin,max:t.xMax,start:o,end:r});o=c.start,r=c.end;const h=ja(s,{min:t.yMin,max:t.yMax,start:l,end:a});return a=h.start,l=h.end,{x:o,y:a,x2:r,y2:l,width:r-o,height:l-a,centerX:o+(r-o)/2,centerY:a+(l-a)/2}}function Ph(n,t){if(!vh(t)){const e=Wo(n,t);let i=t.radius;(!i||isNaN(i))&&(i=Math.min(e.width,e.height)/2,t.radius=i);const s=i*2,o=e.centerX+t.xAdjust,r=e.centerY+t.yAdjust;return{x:o-i,y:r-i,x2:o+i,y2:r+i,centerX:o,centerY:r,width:s,height:s,radius:i}}return Hb(n,t)}function Bb(n,t){const{scales:e,chartArea:i}=n,s=e[t.scaleID],o={x:i.left,y:i.top,x2:i.right,y2:i.bottom};return s?$b(s,o,t):jb(e,o,t),o}function Ah(n,t){const e=Wo(n,t);return e.initProperties=Ke(n,e,t),e.elements=[{type:"label",optionScope:"label",properties:Xb(n,e,t),initProperties:e.initProperties}],e}function Hb(n,t){const e=Ch(n,t),i=t.radius*2;return{x:e.x-t.radius+t.xAdjust,y:e.y-t.radius+t.yAdjust,x2:e.x+t.radius+t.xAdjust,y2:e.y+t.radius+t.yAdjust,centerX:e.x+t.xAdjust,centerY:e.y+t.yAdjust,radius:t.radius,width:i,height:i}}function ja(n,t){const e=Dh(n,t)||t;return{start:Math.min(e.start,e.end),end:Math.max(e.start,e.end)}}function $b(n,t,e){const i=Xe(n,e.value,NaN),s=Xe(n,e.endValue,i);n.isHorizontal()?(t.x=i,t.x2=s):(t.y=i,t.y2=s)}function jb(n,t,e){for(const i of Object.keys($a)){const s=n[Me(n,e,i)];if(s){const{min:o,max:r,start:a,end:l,startProp:c,endProp:h}=$a[i],u=Dh(s,{min:e[o],max:e[r],start:s[a],end:s[l]});t[c]=u.start,t[h]=u.end}}}function Yb({properties:n,options:t},e,i,s){const{x:o,x2:r,width:a}=n;return Eh({start:o,end:r,size:a,borderWidth:t.borderWidth},{position:i.x,padding:{start:s.left,end:s.right},adjust:t.label.xAdjust,size:e.width})}function Ub({properties:n,options:t},e,i,s){const{y:o,y2:r,height:a}=n;return Eh({start:o,end:r,size:a,borderWidth:t.borderWidth},{position:i.y,padding:{start:s.top,end:s.bottom},adjust:t.label.yAdjust,size:e.height})}function Eh(n,t){const{start:e,end:i,borderWidth:s}=n,{position:o,padding:{start:r,end:a},adjust:l}=t,c=i-s-e-r-a-t.size;return e+s/2+l+No(c,o)}function Xb(n,t,e){const i=e.label;i.backgroundColor="transparent",i.callout.display=!1;const s=zo(i.position),o=tt(i.padding),r=es(n.ctx,i),a=Yb({properties:t,options:e},r,s,o),l=Ub({properties:t,options:e},r,s,o),c=r.width+o.width,h=r.height+o.height;return{x:a,y:l,x2:a+c,y2:l+h,width:c,height:h,centerX:a+c/2,centerY:l+h/2,rotation:i.rotation}}const no=["enter","leave"],Vo=no.concat("click");function Zb(n,t,e){t.listened=kh(e,Vo,t.listeners),t.moveListened=!1,no.forEach(i=>{mt(e[i])&&(t.moveListened=!0)}),(!t.listened||!t.moveListened)&&t.annotations.forEach(i=>{!t.listened&&mt(i.click)&&(t.listened=!0),t.moveListened||no.forEach(s=>{mt(i[s])&&(t.listened=!0,t.moveListened=!0)})})}function qb(n,t,e){if(n.listened)switch(t.type){case"mousemove":case"mouseout":return Gb(n,t,e);case"click":return Jb(n,t,e)}}function Gb(n,t,e){if(!n.moveListened)return;let i;t.type==="mousemove"?i=Fo(n.visibleElements,t,e.interaction):i=[];const s=n.hovered;n.hovered=i;const o={state:n,event:t};let r=Ya(o,"leave",s,i);return Ya(o,"enter",i,s)||r}function Ya({state:n,event:t},e,i,s){let o;for(const r of i)s.indexOf(r)<0&&(o=Ih(r.options[e]||n.listeners[e],r,t)||o);return o}function Jb(n,t,e){const i=n.listeners,s=Fo(n.visibleElements,t,e.interaction);let o;for(const r of s)o=Ih(r.options.click||i.click,r,t)||o;return o}function Ih(n,t,e){return W(n,[t.$context,e])===!0}const Hi=["afterDraw","beforeDraw"];function Kb(n,t,e){const i=t.visibleElements;t.hooked=kh(e,Hi,t.hooks),t.hooked||i.forEach(s=>{t.hooked||Hi.forEach(o=>{mt(s.options[o])&&(t.hooked=!0)})})}function Ua(n,t,e){if(n.hooked){const i=t.options[e]||n.hooks[e];return W(i,[t.$context])}}function Qb(n,t,e){const i=s0(n.scales,t,e);let s=Xa(t,i,"min","suggestedMin");s=Xa(t,i,"max","suggestedMax")||s,s&&mt(t.handleTickRangeOptions)&&t.handleTickRangeOptions()}function t0(n,t){for(const e of n)n0(e,t)}function Xa(n,t,e,i){if(q(t[e])&&!e0(n.options,e,i)){const s=n[e]!==t[e];return n[e]=t[e],s}}function e0(n,t,e){return pt(n[t])||pt(n[e])}function n0(n,t){for(const e of["scaleID","xScaleID","yScaleID"]){const i=Me(t,n,e);i&&!t[i]&&i0(n,e)&&console.warn(`No scale found with id '${i}' for annotation '${n.id}'`)}}function i0(n,t){if(t==="scaleID")return!0;const e=t.charAt(0);for(const i of["Min","Max","Value"])if(pt(n[e+i]))return!0;return!1}function s0(n,t,e){const i=t.axis,s=t.id,o=i+"ScaleID",r={min:C(t.min,Number.NEGATIVE_INFINITY),max:C(t.max,Number.POSITIVE_INFINITY)};for(const a of e)a.scaleID===s?Za(a,t,["value","endValue"],r):Me(n,a,o)===s&&Za(a,t,[i+"Min",i+"Max",i+"Value"],r);return r}function Za(n,t,e,i){for(const s of e){const o=n[s];if(pt(o)){const r=t.parse(o);i.min=Math.min(i.min,r),i.max=Math.max(i.max,r)}}}class tn extends st{inRange(t,e,i,s){const{x:o,y:r}=De({x:t,y:e},this.getCenterPoint(s),nt(-this.options.rotation));return gh({x:o,y:r},this.getProps(["x","y","x2","y2"],s),i,this.options)}getCenterPoint(t){return Ce(this,t)}draw(t){t.save(),ts(t,this.getCenterPoint(),this.options.rotation),Mh(t,this,this.options),t.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(t,e){return Ah(t,e)}}tn.id="boxAnnotation";tn.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:0,borderShadowColor:"transparent",borderWidth:1,display:!0,init:void 0,hitTolerance:0,label:{backgroundColor:"transparent",borderWidth:0,callout:{display:!1},color:"black",content:null,display:!1,drawTime:void 0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:"bold"},height:void 0,hitTolerance:void 0,opacity:void 0,padding:6,position:"center",rotation:void 0,textAlign:"start",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0,z:void 0},rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0};tn.defaultRoutes={borderColor:"color",backgroundColor:"color"};tn.descriptors={label:{_fallback:!0}};class ns extends st{inRange(t,e,i,s){return mh({x:t,y:e},{rect:this.getProps(["x","y","x2","y2"],s),center:this.getCenterPoint(s)},i,{rotation:this.rotation,borderWidth:0,hitTolerance:this.options.hitTolerance})}getCenterPoint(t){return Ce(this,t)}draw(t){const e=this.options;!e.display||!e.content||(h0(t,this),t.save(),ts(t,this.getCenterPoint(),this.rotation),Th(t,this,e,this._fitRatio),t.restore())}resolveElementProperties(t,e){const i=o0(t,e);if(!i)return{};const{controllerMeta:s,point:o,radius:r}=a0(t,e,i);let a=es(t.ctx,e);const l=l0(a,r);_h(e,l)&&(a={width:a.width*l,height:a.height*l});const{position:c,xAdjust:h,yAdjust:u}=e,d=xh(o,a,{borderWidth:0,position:c,xAdjust:h,yAdjust:u});return{initProperties:Ke(t,d,e),...d,...s,rotation:e.rotation,_fitRatio:l}}}ns.id="doughnutLabelAnnotation";ns.defaults={autoFit:!0,autoHide:!0,backgroundColor:"transparent",backgroundShadowColor:"transparent",borderColor:"transparent",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderShadowColor:"transparent",borderWidth:0,color:"black",content:null,display:!0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:void 0},height:void 0,hitTolerance:0,init:void 0,opacity:void 0,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,spacing:1,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0};ns.defaultRoutes={};function o0(n,t){return n.getSortedVisibleDatasetMetas().reduce(function(e,i){const s=i.controller;return s instanceof te&&r0(n,t,i.data)&&(!e||s.innerRadius<e.controller.innerRadius)&&s.options.circumference>=90?i:e},void 0)}function r0(n,t,e){if(!t.autoHide)return!0;for(let i=0;i<e.length;i++)if(!e[i].hidden&&n.getDataVisibility(i))return!0}function a0({chartArea:n},t,e){const{left:i,top:s,right:o,bottom:r}=n,{innerRadius:a,offsetX:l,offsetY:c}=e.controller,h=(i+o)/2+l,u=(s+r)/2+c,d={left:Math.max(h-a,i),right:Math.min(h+a,o),top:Math.max(u-a,s),bottom:Math.min(u+a,r)},f={x:(d.left+d.right)/2,y:(d.top+d.bottom)/2},g=t.spacing+t.borderWidth/2,m=a-g,p=f.y>u,y=p?s+g:r-g,b=c0(y,h,u,m);return{controllerMeta:{_centerX:h,_centerY:u,_radius:m,_counterclockwise:p,...b},point:f,radius:Math.min(a,Math.min(d.right-d.left,d.bottom-d.top)/2)}}function l0({width:n,height:t},e){const i=Math.sqrt(Math.pow(n,2)+Math.pow(t,2));return e*2/i}function c0(n,t,e,i){const s=Math.pow(e-n,2),o=Math.pow(i,2),r=t*-2,a=Math.pow(t,2)+s-o,l=Math.pow(r,2)-4*a;if(l<=0)return{_startAngle:0,_endAngle:j};const c=(-r-Math.sqrt(l))/2,h=(-r+Math.sqrt(l))/2;return{_startAngle:Ei({x:t,y:e},{x:c,y:n}).angle,_endAngle:Ei({x:t,y:e},{x:h,y:n}).angle}}function h0(n,t){const{_centerX:e,_centerY:i,_radius:s,_startAngle:o,_endAngle:r,_counterclockwise:a,options:l}=t;n.save();const c=Ut(n,l);n.fillStyle=l.backgroundColor,n.beginPath(),n.arc(e,i,s,o,r,a),n.closePath(),n.fill(),c&&n.stroke(),n.restore()}class Un extends st{inRange(t,e,i,s){return mh({x:t,y:e},{rect:this.getProps(["x","y","x2","y2"],s),center:this.getCenterPoint(s)},i,{rotation:this.rotation,borderWidth:this.options.borderWidth,hitTolerance:this.options.hitTolerance})}getCenterPoint(t){return Ce(this,t)}draw(t){const e=this.options,i=!pt(this._visible)||this._visible;!e.display||!e.content||!i||(t.save(),ts(t,this.getCenterPoint(),this.rotation),Ib(t,this),Mh(t,this,e),Th(t,u0(this),e),t.restore())}resolveElementProperties(t,e){let i;if(vh(e))i=Ch(t,e);else{const{centerX:a,centerY:l}=Wo(t,e);i={x:a,y:l}}const s=tt(e.padding),o=es(t.ctx,e),r=xh(i,o,e,s);return{initProperties:Ke(t,r,e),pointX:i.x,pointY:i.y,...r,rotation:e.rotation}}}Un.id="labelAnnotation";Un.defaults={adjustScaleRange:!0,backgroundColor:"transparent",backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:0,borderShadowColor:"transparent",borderWidth:0,callout:{borderCapStyle:"butt",borderColor:void 0,borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:1,display:!1,margin:5,position:"auto",side:5,start:"50%"},color:"black",content:null,display:!0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:void 0},height:void 0,hitTolerance:0,init:void 0,opacity:void 0,padding:6,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0};Un.defaultRoutes={borderColor:"color"};function u0({x:n,y:t,width:e,height:i,options:s}){const o=s.borderWidth/2,r=tt(s.padding);return{x:n+r.left+o,y:t+r.top+o,width:e-r.left-r.right-s.borderWidth,height:i-r.top-r.bottom-s.borderWidth}}const Bo=(n,t,e)=>({x:n.x+e*(t.x-n.x),y:n.y+e*(t.y-n.y)}),io=(n,t,e)=>Bo(t,e,Math.abs((n-t.y)/(e.y-t.y))).x,qa=(n,t,e)=>Bo(t,e,Math.abs((n-t.x)/(e.x-t.x))).y,_n=n=>n*n,d0=(n,t,{x:e,y:i,x2:s,y2:o},r)=>r==="y"?{start:Math.min(i,o),end:Math.max(i,o),value:t}:{start:Math.min(e,s),end:Math.max(e,s),value:n},Ga=(n,t,e,i)=>(1-i)*(1-i)*n+2*(1-i)*i*t+i*i*e,so=(n,t,e,i)=>({x:Ga(n.x,t.x,e.x,i),y:Ga(n.y,t.y,e.y,i)}),Ja=(n,t,e,i)=>2*(1-i)*(t-n)+2*i*(e-t),Ka=(n,t,e,i)=>-Math.atan2(Ja(n.x,t.x,e.x,i),Ja(n.y,t.y,e.y,i))+.5*R;class Xn extends st{inRange(t,e,i,s){const o=(this.options.borderWidth+this.options.hitTolerance)/2;if(i!=="x"&&i!=="y"){const r={mouseX:t,mouseY:e},{path:a,ctx:l}=this;if(a){Ut(l,this.options),l.lineWidth+=this.options.hitTolerance;const{chart:h}=this.$context,u=t*h.currentDevicePixelRatio,d=e*h.currentDevicePixelRatio,f=l.isPointInStroke(a,u,d)||oo(this,r,s);return l.restore(),f}const c=_n(o);return p0(this,r,c,s)||oo(this,r,s)}return f0(this,{mouseX:t,mouseY:e},i,{hitSize:o,useFinalPosition:s})}getCenterPoint(t){return Ce(this,t)}draw(t){const{x:e,y:i,x2:s,y2:o,cp:r,options:a}=this;if(t.save(),!Ut(t,a))return t.restore();Qe(t,a);const l=Math.sqrt(Math.pow(s-e,2)+Math.pow(o-i,2));if(a.curve&&r)return S0(t,this,r,l),t.restore();const{startOpts:c,endOpts:h,startAdjust:u,endAdjust:d}=Lh(this),f=Math.atan2(o-i,s-e);t.translate(e,i),t.rotate(f),t.beginPath(),t.moveTo(0+u,0),t.lineTo(l-d,0),t.shadowColor=a.borderShadowColor,t.stroke(),ro(t,0,u,c),ro(t,l,-d,h),t.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(t,e){const i=Bb(t,e),{x:s,y:o,x2:r,y2:a}=i,l=g0(i,t.chartArea),c=l?m0({x:s,y:o},{x:r,y:a},t.chartArea):{x:s,y:o,x2:r,y2:a,width:Math.abs(r-s),height:Math.abs(a-o)};if(c.centerX=(r+s)/2,c.centerY=(a+o)/2,c.initProperties=Ke(t,c,e),e.curve){const u={x:c.x,y:c.y},d={x:c.x2,y:c.y2};c.cp=k0(c,e,He(u,d))}const h=y0(t,c,e.label);return h._visible=l,c.elements=[{type:"label",optionScope:"label",properties:h,initProperties:c.initProperties}],c}}Xn.id="lineAnnotation";const Qa={backgroundColor:void 0,backgroundShadowColor:void 0,borderColor:void 0,borderDash:void 0,borderDashOffset:void 0,borderShadowColor:void 0,borderWidth:void 0,display:void 0,fill:void 0,length:void 0,shadowBlur:void 0,shadowOffsetX:void 0,shadowOffsetY:void 0,width:void 0};Xn.defaults={adjustScaleRange:!0,arrowHeads:{display:!1,end:Object.assign({},Qa),fill:!1,length:12,start:Object.assign({},Qa),width:6},borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:2,curve:!1,controlPoint:{y:"-50%"},display:!0,endValue:void 0,init:void 0,hitTolerance:0,label:{backgroundColor:"rgba(0,0,0,0.8)",backgroundShadowColor:"transparent",borderCapStyle:"butt",borderColor:"black",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:6,borderShadowColor:"transparent",borderWidth:0,callout:Object.assign({},Un.defaults.callout),color:"#fff",content:null,display:!1,drawTime:void 0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:"bold"},height:void 0,hitTolerance:void 0,opacity:void 0,padding:6,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0,z:void 0},scaleID:void 0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,value:void 0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0};Xn.descriptors={arrowHeads:{start:{_fallback:!0},end:{_fallback:!0},_fallback:!0}};Xn.defaultRoutes={borderColor:"color"};function f0(n,{mouseX:t,mouseY:e},i,{hitSize:s,useFinalPosition:o}){const r=d0(t,e,n.getProps(["x","y","x2","y2"],o),i);return fh(r,s)||oo(n,{mouseX:t,mouseY:e},o,i)}function g0({x:n,y:t,x2:e,y2:i},{top:s,right:o,bottom:r,left:a}){return!(n<a&&e<a||n>o&&e>o||t<s&&i<s||t>r&&i>r)}function tl({x:n,y:t},e,{top:i,right:s,bottom:o,left:r}){return n<r&&(t=qa(r,{x:n,y:t},e),n=r),n>s&&(t=qa(s,{x:n,y:t},e),n=s),t<i&&(n=io(i,{x:n,y:t},e),t=i),t>o&&(n=io(o,{x:n,y:t},e),t=o),{x:n,y:t}}function m0(n,t,e){const{x:i,y:s}=tl(n,t,e),{x:o,y:r}=tl(t,n,e);return{x:i,y:s,x2:o,y2:r,width:Math.abs(o-i),height:Math.abs(r-s)}}function p0(n,{mouseX:t,mouseY:e},i=we,s){const{x:o,y:r,x2:a,y2:l}=n.getProps(["x","y","x2","y2"],s),c=a-o,h=l-r,u=_n(c)+_n(h),d=u===0?-1:((t-o)*c+(e-r)*h)/u;let f,g;return d<0?(f=o,g=r):d>1?(f=a,g=l):(f=o+d*c,g=r+d*h),_n(t-f)+_n(e-g)<=i}function oo(n,{mouseX:t,mouseY:e},i,s){const o=n.label;return o.options.display&&o.inRange(t,e,s,i)}function y0(n,t,e){const i=e.borderWidth,s=tt(e.padding),o=es(n.ctx,e),r=o.width+s.width+i,a=o.height+s.height+i;return x0(t,e,{width:r,height:a,padding:s},n.chartArea)}function b0(n){const{x:t,y:e,x2:i,y2:s}=n,o=Math.atan2(s-e,i-t);return o>R/2?o-R:o<R/-2?o+R:o}function x0(n,t,e,i){const{width:s,height:o,padding:r}=e,{xAdjust:a,yAdjust:l}=t,c={x:n.x,y:n.y},h={x:n.x2,y:n.y2},u=t.rotation==="auto"?b0(n):nt(t.rotation),d=_0(s,o,u),f=w0(n,t,{labelSize:d,padding:r},i),g=n.cp?so(c,n.cp,h,f):Bo(c,h,f),m={size:d.w,min:i.left,max:i.right,padding:r.left},p={size:d.h,min:i.top,max:i.bottom,padding:r.top},y=nl(g.x,m)+a,b=nl(g.y,p)+l;return{x:y-s/2,y:b-o/2,x2:y+s/2,y2:b+o/2,centerX:y,centerY:b,pointX:g.x,pointY:g.y,width:s,height:o,rotation:$i(u)}}function _0(n,t,e){const i=Math.cos(e),s=Math.sin(e);return{w:Math.abs(n*i)+Math.abs(t*s),h:Math.abs(n*s)+Math.abs(t*i)}}function w0(n,t,e,i){let s;const o=v0(n,i);return t.position==="start"?s=el({w:n.x2-n.x,h:n.y2-n.y},e,t,o):t.position==="end"?s=1-el({w:n.x-n.x2,h:n.y-n.y2},e,t,o):s=No(1,t.position),s}function el(n,t,e,i){const{labelSize:s,padding:o}=t,r=n.w*i.dx,a=n.h*i.dy,l=r>0&&(s.w/2+o.left-i.x)/r,c=a>0&&(s.h/2+o.top-i.y)/a;return Ki(Math.max(l,c),0,.25)}function v0(n,t){const{x:e,x2:i,y:s,y2:o}=n,r=Math.min(s,o)-t.top,a=Math.min(e,i)-t.left,l=t.bottom-Math.max(s,o),c=t.right-Math.max(e,i);return{x:Math.min(a,c),y:Math.min(r,l),dx:a<=c?1:-1,dy:r<=l?1:-1}}function nl(n,t){const{size:e,min:i,max:s,padding:o}=t,r=e/2;return e>s-i?(s+i)/2:(i>=n-o-r&&(n=i+o+r),s<=n+o+r&&(n=s-o-r),n)}function Lh(n){const t=n.options,e=t.arrowHeads&&t.arrowHeads.start,i=t.arrowHeads&&t.arrowHeads.end;return{startOpts:e,endOpts:i,startAdjust:il(n,e),endAdjust:il(n,i)}}function il(n,t){if(!t||!t.display)return 0;const{length:e,width:i}=t,s=n.options.borderWidth/2,o={x:e,y:i+s};return Math.abs(io(0,o,{x:0,y:s}))}function ro(n,t,e,i){if(!i||!i.display)return;const{length:s,width:o,fill:r,backgroundColor:a,borderColor:l}=i,c=Math.abs(t-s)+e;n.beginPath(),Qe(n,i),Ut(n,i),n.moveTo(c,-o),n.lineTo(t+e,0),n.lineTo(c,o),r===!0?(n.fillStyle=a||l,n.closePath(),n.fill(),n.shadowColor="transparent"):n.shadowColor=i.borderShadowColor,n.stroke()}function k0(n,t,e){const{x:i,y:s,x2:o,y2:r,centerX:a,centerY:l}=n,c=Math.atan2(r-s,o-i),h=zo(t.controlPoint,0),u={x:a+ce(e,h.x,!1),y:l+ce(e,h.y,!1)};return De(u,{x:a,y:l},c)}function sl(n,{x:t,y:e},{angle:i,adjust:s},o){!o||!o.display||(n.save(),n.translate(t,e),n.rotate(i),ro(n,0,-s,o),n.restore())}function S0(n,t,e,i){const{x:s,y:o,x2:r,y2:a,options:l}=t,{startOpts:c,endOpts:h,startAdjust:u,endAdjust:d}=Lh(t),f={x:s,y:o},g={x:r,y:a},m=Ka(f,e,g,0),p=Ka(f,e,g,1)-R,y=so(f,e,g,u/i),b=so(f,e,g,1-d/i),_=new Path2D;n.beginPath(),_.moveTo(y.x,y.y),_.quadraticCurveTo(e.x,e.y,b.x,b.y),n.shadowColor=l.borderShadowColor,n.stroke(_),t.path=_,t.ctx=n,sl(n,y,{angle:m,adjust:u},c),sl(n,b,{angle:p,adjust:d},h)}class Zn extends st{inRange(t,e,i,s){const o=this.options.rotation,r=(this.options.borderWidth+this.options.hitTolerance)/2;if(i!=="x"&&i!=="y")return M0({x:t,y:e},this.getProps(["width","height","centerX","centerY"],s),o,r);const{x:a,y:l,x2:c,y2:h}=this.getProps(["x","y","x2","y2"],s),u=i==="y"?{start:l,end:h}:{start:a,end:c},d=De({x:t,y:e},this.getCenterPoint(s),nt(-o));return d[i]>=u.start-r-we&&d[i]<=u.end+r+we}getCenterPoint(t){return Ce(this,t)}draw(t){const{width:e,height:i,centerX:s,centerY:o,options:r}=this;t.save(),ts(t,this.getCenterPoint(),r.rotation),Qe(t,this.options),t.beginPath(),t.fillStyle=r.backgroundColor;const a=Ut(t,r);t.ellipse(s,o,i/2,e/2,R/2,0,2*R),t.fill(),a&&(t.shadowColor=r.borderShadowColor,t.stroke()),t.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(t,e){return Ah(t,e)}}Zn.id="ellipseAnnotation";Zn.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:1,display:!0,hitTolerance:0,init:void 0,label:Object.assign({},tn.defaults.label),rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0};Zn.defaultRoutes={borderColor:"color",backgroundColor:"color"};Zn.descriptors={label:{_fallback:!0}};function M0(n,t,e,i){const{width:s,height:o,centerX:r,centerY:a}=t,l=s/2,c=o/2;if(l<=0||c<=0)return!1;const h=nt(e||0),u=Math.cos(h),d=Math.sin(h),f=Math.pow(u*(n.x-r)+d*(n.y-a),2),g=Math.pow(d*(n.x-r)-u*(n.y-a),2);return f/Math.pow(l+i,2)+g/Math.pow(c+i,2)<=1.0001}class is extends st{inRange(t,e,i,s){const{x:o,y:r,x2:a,y2:l,width:c}=this.getProps(["x","y","x2","y2","width"],s),h=(this.options.borderWidth+this.options.hitTolerance)/2;return i!=="x"&&i!=="y"?xb({x:t,y:e},this.getCenterPoint(s),c/2,h):fh(i==="y"?{start:r,end:l,value:e}:{start:o,end:a,value:t},h)}getCenterPoint(t){return Ce(this,t)}draw(t){const e=this.options,i=e.borderWidth;if(e.radius<.1)return;t.save(),t.fillStyle=e.backgroundColor,Qe(t,e);const s=Ut(t,e);Ob(t,this,this.centerX,this.centerY),s&&!Qi(e.pointStyle)&&(t.shadowColor=e.borderShadowColor,t.stroke()),t.restore(),e.borderWidth=i}resolveElementProperties(t,e){const i=Ph(t,e);return i.initProperties=Ke(t,i,e),i}}is.id="pointAnnotation";is.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:1,display:!0,hitTolerance:0,init:void 0,pointStyle:"circle",radius:10,rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0};is.defaultRoutes={borderColor:"color",backgroundColor:"color"};class ss extends st{inRange(t,e,i,s){if(i!=="x"&&i!=="y")return this.options.radius>=.1&&this.elements.length>1&&O0(this.elements,t,e,s);const o=De({x:t,y:e},this.getCenterPoint(s),nt(-this.options.rotation)),r=this.elements.map(c=>i==="y"?c.bY:c.bX),a=Math.min(...r),l=Math.max(...r);return o[i]>=a&&o[i]<=l}getCenterPoint(t){return Ce(this,t)}draw(t){const{elements:e,options:i}=this;t.save(),t.beginPath(),t.fillStyle=i.backgroundColor,Qe(t,i);const s=Ut(t,i);let o=!0;for(const r of e)o?(t.moveTo(r.x,r.y),o=!1):t.lineTo(r.x,r.y);t.closePath(),t.fill(),s&&(t.shadowColor=i.borderShadowColor,t.stroke()),t.restore()}resolveElementProperties(t,e){const i=Ph(t,e),{sides:s,rotation:o}=e,r=[],a=2*R/s;let l=o*go;for(let c=0;c<s;c++,l+=a){const h=T0(i,e,l);h.initProperties=Ke(t,i,e),r.push(h)}return i.elements=r,i}}ss.id="polygonAnnotation";ss.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderShadowColor:"transparent",borderWidth:1,display:!0,hitTolerance:0,init:void 0,point:{radius:0},radius:10,rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,sides:3,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0};ss.defaultRoutes={borderColor:"color",backgroundColor:"color"};function T0({centerX:n,centerY:t},{radius:e,borderWidth:i,hitTolerance:s},o){const r=(i+s)/2,a=Math.sin(o),l=Math.cos(o),c={x:n+a*e,y:t-l*e};return{type:"point",optionScope:"point",properties:{x:c.x,y:c.y,centerX:c.x,centerY:c.y,bX:n+a*(e+r),bY:t-l*(e+r)}}}function O0(n,t,e,i){let s=!1,o=n[n.length-1].getProps(["bX","bY"],i);for(const r of n){const a=r.getProps(["bX","bY"],i);a.bY>e!=o.bY>e&&t<(o.bX-a.bX)*(e-a.bY)/(o.bY-a.bY)+a.bX&&(s=!s),o=a}return s}const re={box:tn,doughnutLabel:ns,ellipse:Zn,label:Un,line:Xn,point:is,polygon:ss};Object.keys(re).forEach(n=>{Y.describe(`elements.${re[n].id}`,{_fallback:"plugins.annotation.common"})});const D0={update:Object.assign},C0=Vo.concat(Hi),ol=(n,t)=>E(t)?lo(n,t):n,ao=n=>n==="color"||n==="font";function Ho(n="line"){return re[n]?n:(console.warn(`Unknown annotation type: '${n}', defaulting to 'line'`),"line")}function P0(n,t,e,i){const s=E0(n,e.animations,i),o=t.annotations,r=R0(t.elements,o);for(let a=0;a<o.length;a++){const l=o[a],c=Rh(r,a,l.type),h=l.setContext(L0(n,c,r,l)),u=c.resolveElementProperties(n,h);u.skip=A0(u),"elements"in u&&(I0(c,u.elements,h,s),delete u.elements),pt(c.x)||Object.assign(c,u),Object.assign(c,u.initProperties),u.options=Fh(h),s.update(c,u)}}function A0(n){return isNaN(n.x)||isNaN(n.y)}function E0(n,t,e){return e==="reset"||e==="none"||e==="resize"?D0:new So(n,t)}function I0(n,t,e,i){const s=n.elements||(n.elements=[]);s.length=t.length;for(let o=0;o<t.length;o++){const r=t[o],a=r.properties,l=Rh(s,o,r.type,r.initProperties),c=e[r.optionScope].override(r);a.options=Fh(c),i.update(l,a)}}function Rh(n,t,e,i){const s=re[Ho(e)];let o=n[t];return(!o||!(o instanceof s))&&(o=n[t]=new s,Object.assign(o,i)),o}function Fh(n){const t=re[Ho(n.type)],e={};e.id=n.id,e.type=n.type,e.drawTime=n.drawTime,Object.assign(e,lo(n,t.defaults),lo(n,t.defaultRoutes));for(const i of C0)e[i]=n[i];return e}function lo(n,t){const e={};for(const i of Object.keys(t)){const s=t[i],o=n[i];ao(i)&&V(o)?e[i]=o.map(r=>ol(r,s)):e[i]=ol(o,s)}return e}function L0(n,t,e,i){return t.$context||(t.$context=Object.assign(Object.create(n.getContext()),{element:t,get elements(){return e.filter(s=>s&&s.options)},id:i.id,type:"annotation"}))}function R0(n,t){const e=t.length,i=n.length;if(i<e){const s=e-i;n.splice(i,0,...new Array(s))}else i>e&&n.splice(e,i-e);return n}var F0="3.1.0";const qt=new Map,rl=n=>n.type!=="doughnutLabel",N0=Vo.concat(Hi);var z0={id:"annotation",version:F0,beforeRegister(){_b("chart.js","4.0",St.version)},afterRegister(){St.register(re)},afterUnregister(){St.unregister(re)},beforeInit(n){qt.set(n,{annotations:[],elements:[],visibleElements:[],listeners:{},listened:!1,moveListened:!1,hooks:{},hooked:!1,hovered:[]})},beforeUpdate(n,t,e){const i=qt.get(n),s=i.annotations=[];let o=e.annotations;E(o)?Object.keys(o).forEach(r=>{const a=o[r];E(a)&&(a.id=r,s.push(a))}):V(o)&&s.push(...o),t0(s.filter(rl),n.scales)},afterDataLimits(n,t){const e=qt.get(n);Qb(n,t.scale,e.annotations.filter(rl).filter(i=>i.display&&i.adjustScaleRange))},afterUpdate(n,t,e){const i=qt.get(n);Zb(n,i,e),P0(n,i,e,t.mode),i.visibleElements=i.elements.filter(s=>!s.skip&&s.options.display),Kb(n,i,e)},beforeDatasetsDraw(n,t,e){dn(n,"beforeDatasetsDraw",e.clip)},afterDatasetsDraw(n,t,e){dn(n,"afterDatasetsDraw",e.clip)},beforeDatasetDraw(n,t,e){dn(n,t.index,e.clip)},beforeDraw(n,t,e){dn(n,"beforeDraw",e.clip)},afterDraw(n,t,e){dn(n,"afterDraw",e.clip)},beforeEvent(n,t,e){const i=qt.get(n);qb(i,t.event,e)&&(t.changed=!0)},afterDestroy(n){qt.delete(n)},getAnnotations(n){const t=qt.get(n);return t?t.elements:[]},_getAnnotationElementsAtEventForMode(n,t,e){return Fo(n,t,e)},defaults:{animations:{numbers:{properties:["x","y","x2","y2","width","height","centerX","centerY","pointX","pointY","radius"],type:"number"},colors:{properties:["backgroundColor","borderColor"],type:"color"}},clip:!0,interaction:{mode:void 0,axis:void 0,intersect:void 0},common:{drawTime:"afterDatasetsDraw",init:!1,label:{}}},descriptors:{_indexable:!1,_scriptable:n=>!N0.includes(n)&&n!=="init",annotations:{_allKeys:!1,_fallback:(n,t)=>`elements.${re[Ho(t.type)].id}`},interaction:{_fallback:!0},common:{label:{_indexable:ao,_fallback:!0},_indexable:ao}},additionalOptionScopes:[""]};function dn(n,t,e){const{ctx:i,chartArea:s}=n,o=qt.get(n);e&&Wn(i,s);const r=W0(o.visibleElements,t).sort((a,l)=>a.element.options.z-l.element.options.z);for(const a of r)V0(i,s,o,a);e&&Vn(i)}function W0(n,t){const e=[];for(const i of n)if(i.options.drawTime===t&&e.push({element:i,main:!0}),i.elements&&i.elements.length)for(const s of i.elements)s.options.display&&s.options.drawTime===t&&e.push({element:s});return e}function V0(n,t,e,i){const s=i.element;i.main?(Ua(e,s,"beforeDraw"),s.draw(n,t),Ua(e,s,"afterDraw")):s.draw(n,t)}St.register(fb,z0);document.addEventListener("DOMContentLoaded",()=>{B0(),H0()});function B0(){const n=document.getElementById("sidebarToggle"),t=document.querySelector(".sidebar");n&&t&&n.addEventListener("click",()=>{t.classList.toggle("sidebar-open")}),document.addEventListener("click",e=>{window.innerWidth<=992&&t&&t.classList.contains("sidebar-open")&&!t.contains(e.target)&&e.target!==n&&t.classList.remove("sidebar-open")})}function H0(){document.querySelectorAll(".status-box, .card, .data-visualization").forEach(e=>{e.addEventListener("mouseenter",()=>{e.classList.add("element-hover")}),e.addEventListener("mouseleave",()=>{e.classList.remove("element-hover")})}),document.querySelectorAll(".status-indicator").forEach(e=>{e.classList.contains("online")&&(e.style.animation="pulse 2s infinite")})}const j0={formatNumber:(n,t=3)=>{if(n==null)return"0".padEnd(t+2,"0");try{return parseFloat(n).toFixed(t)}catch{return"0".padEnd(t+2,"0")}},formatTimestamp:n=>{if(!n)return"N/A";try{let t=D.fromSQL(n);return t.isValid||(t=D.fromISO(n)),t.isValid||(t=D.fromJSDate(new Date(n))),t.isValid?t.setZone("Asia/Kolkata").toFormat("dd LLL yyyy, hh:mm:ss a"):(console.warn("Could not parse timestamp:",n),n)}catch(t){return console.error("Error formatting timestamp:",t),n}},showMessage:(n,t="info",e,i=5e3)=>{if(!e)return;const s=document.createElement("div");s.className=`message ${t}`,s.innerHTML=n,e.innerHTML="",e.appendChild(s),i>0&&(t==="success"||t==="info")&&setTimeout(()=>{s.style.opacity="0",s.style.transform="translateY(-10px)",setTimeout(()=>{e.contains(s)&&e.removeChild(s)},300)},i)},debounce:(n,t=300)=>{let e;return function(...s){const o=()=>{clearTimeout(e),n(...s)};clearTimeout(e),e=setTimeout(o,t)}}};export{St as C,D,j0 as u};
