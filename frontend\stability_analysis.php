<?php
// Initialize the session
session_start();

// Check if the user is logged in, if not redirect to login page
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: login.php");
    exit;
}

// Include Vite helper functions
require_once 'vite-helper.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Stability Analysis - Online Data Logger System</title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="images/online-data-logger-logo-new.svg">
    
    <!-- Vite Assets -->
    <?php echo viteClientScript(); ?>
    <?php echo viteCssTag('stability'); ?>
    <?php echo viteScriptTag('stability'); ?>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar Navigation -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="images/online-data-logger-sidebar.svg" alt="Online Data Logger System" class="company-logo" width="180">
            </div>

            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="advanced_dashboard.php">
                            <span class="material-icons-round">dashboard</span>
                            <span>Live Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="history.php">
                            <span class="material-icons-round">history</span>
                            <span>Historical Data</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="stability_analysis.php">
                            <span class="material-icons-round">analytics</span>
                            <span>Stability Analysis</span>
                        </a>
                    </li>
                    <li>
                        <a href="alerts_events.php">
                            <span class="material-icons-round">warning</span>
                            <span>Alerts & Events</span>
                        </a>
                    </li>
                    <li>
                        <a href="reports.php">
                            <span class="material-icons-round">insights</span>
                            <span>Reports</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" id="settingsLink">
                            <span class="material-icons-round">settings</span>
                            <span>System Settings</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <div class="system-status">
                    <span class="status-indicator online"></span>
                    <span>System Online</span>
                </div>
                <a href="logout.php" class="logout-button">
                    <span class="material-icons-round">logout</span>
                    <span>Logout</span>
                </a>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Status Header -->
            <header class="status-header">
                <div class="status-title">
                    <div class="header-logo-container">
                        <img src="images/online-data-logger-logo-new.svg" alt="Online Data Logger System" class="header-logo">
                    </div>
                    <div class="subtitle">Electrical System Stability Analysis</div>
                </div>

                <div class="status-boxes">
                    <div class="status-box system">
                        <div class="status-icon">
                            <span class="material-icons-round">analytics</span>
                        </div>
                        <div class="status-text">
                            <div class="status-label">Analysis Status</div>
                            <div class="status-value">Ready</div>
                        </div>
                    </div>

                    <div class="status-box">
                        <div class="status-icon">
                            <span class="material-icons-round">schedule</span>
                        </div>
                        <div class="status-text">
                            <div class="status-label">Timezone</div>
                            <div class="status-value">IST (UTC+5:30)</div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Message Container -->
            <div id="messageContainer"></div>

            <!-- Analysis Controls -->
            <div class="stability-analysis-container">
                <div class="analysis-controls">
                    <div class="control-group">
                        <label>Analysis Type</label>
                        <div class="analysis-type-selector">
                            <button class="analysis-type-btn active" data-type="comprehensive">
                                <span class="material-icons-round">analytics</span>
                                Comprehensive
                            </button>
                            <button class="analysis-type-btn" data-type="voltage">
                                <span class="material-icons-round">bolt</span>
                                Voltage
                            </button>
                            <button class="analysis-type-btn" data-type="current">
                                <span class="material-icons-round">electric_bolt</span>
                                Current
                            </button>
                            <button class="analysis-type-btn" data-type="power">
                                <span class="material-icons-round">power</span>
                                Power
                            </button>
                            <button class="analysis-type-btn" data-type="frequency">
                                <span class="material-icons-round">speed</span>
                                Frequency
                            </button>
                            <button class="analysis-type-btn" data-type="harmonics">
                                <span class="material-icons-round">graphic_eq</span>
                                Harmonics
                            </button>
                            <button class="analysis-type-btn" data-type="power_quality">
                                <span class="material-icons-round">high_quality</span>
                                Power Quality
                            </button>
                            <button class="analysis-type-btn" data-type="predictive">
                                <span class="material-icons-round">psychology</span>
                                Predictive
                            </button>
                        </div>
                    </div>

                    <div class="control-group">
                        <label for="startDate">Start Date & Time</label>
                        <input type="datetime-local" id="startDate" name="startDate">
                    </div>

                    <div class="control-group">
                        <label for="endDate">End Date & Time</label>
                        <input type="datetime-local" id="endDate" name="endDate">
                    </div>

                    <div class="control-group">
                        <label for="timeRange">Quick Range</label>
                        <select id="timeRange">
                            <option value="custom">Custom Range</option>
                            <option value="1h" selected>Last 1 Hour</option>
                            <option value="6h">Last 6 Hours</option>
                            <option value="24h">Last 24 Hours</option>
                            <option value="7d">Last 7 Days</option>
                            <option value="30d">Last 30 Days</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label>&nbsp;</label>
                        <button id="runAnalysisBtn" class="button button-primary">
                            <span class="material-icons-round">play_arrow</span>
                            Run Analysis
                        </button>
                    </div>
                </div>

                <!-- Loading Indicator -->
                <div id="loadingIndicator" class="loading-spinner" style="display: none;">
                    <span>Analyzing electrical system stability...</span>
                </div>

                <!-- Stability Metrics -->
                <div class="stability-metrics-grid" id="metricsContainer">
                    <!-- Metrics will be populated by JavaScript -->
                </div>

                <!-- Analysis Charts -->
                <div class="analysis-charts-container" id="chartsContainer">
                    <!-- Charts will be populated by JavaScript -->
                </div>

                <!-- Recommendations -->
                <div class="recommendations-section" id="recommendationsContainer">
                    <!-- Recommendations will be populated by JavaScript -->
                </div>
            </div>
        </main>
    </div>
</body>
</html>
