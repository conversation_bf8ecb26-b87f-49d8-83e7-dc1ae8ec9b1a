// Import styles
import '../scss/main.scss';
import '../scss/history.scss';

// Import utilities from main
import { utils } from './main';
import { DateTime } from 'luxon';
import Chart from 'chart.js/auto';

// DOM Elements
let elements = {};

// State
const state = {
  currentPage: 1,
  totalPages: 1,
  data: [],
  chartInstance: null,
  chartType: 'line',
  activeParameter: 'voltage_1',
  isVisualizationVisible: true
};

/**
 * Initialize the history page
 */
function initHistoryPage() {
  // Get DOM elements
  elements = {
    startDate: document.getElementById('startDate'),
    endDate: document.getElementById('endDate'),
    limit: document.getElementById('limit'),
    filterBtn: document.getElementById('filterBtn'),
    exportBtn: document.getElementById('exportBtn'),
    dataRows: document.getElementById('dataRows'),
    prevBtn: document.getElementById('prevBtn'),
    nextBtn: document.getElementById('nextBtn'),
    pageInfo: document.getElementById('pageInfo'),
    messageContainer: document.getElementById('messageContainer'),
    historyChart: document.getElementById('historyChart'),
    lineChartBtn: document.getElementById('lineChartBtn'),
    barChartBtn: document.getElementById('barChartBtn'),
    tableViewBtn: document.getElementById('tableViewBtn'),
    quickFilters: document.querySelectorAll('.quick-filter')
  };

  // Set default date range to current time
  setTimeRange('1h');
  updateActiveQuickFilter('1h');

  // Add event listeners
  bindEvents();
}

/**
 * Bind event listeners
 */
function bindEvents() {
  if (elements.filterBtn) {
    elements.filterBtn.addEventListener('click', fetchHistoricalData);
  }
  
  if (elements.exportBtn) {
    elements.exportBtn.addEventListener('click', exportCSV);
  }

  if (elements.prevBtn) {
    elements.prevBtn.addEventListener('click', () => {
      if (state.currentPage > 1) {
        state.currentPage--;
        fetchHistoricalData();
      }
    });
  }

  if (elements.nextBtn) {
    elements.nextBtn.addEventListener('click', () => {
      if (state.currentPage < state.totalPages) {
        state.currentPage++;
        fetchHistoricalData();
      }
    });
  }
  
  // Chart type buttons
  if (elements.lineChartBtn) {
    elements.lineChartBtn.addEventListener('click', () => {
      state.chartType = 'line';
      updateChart();
    });
  }
  
  if (elements.barChartBtn) {
    elements.barChartBtn.addEventListener('click', () => {
      state.chartType = 'bar';
      updateChart();
    });
  }
  
  if (elements.tableViewBtn) {
    elements.tableViewBtn.addEventListener('click', () => {
      const visualizationContainer = document.querySelector('.data-visualization');
      if (!visualizationContainer) return;
      
      state.isVisualizationVisible = !state.isVisualizationVisible;
      
      if (state.isVisualizationVisible) {
        visualizationContainer.style.display = 'block';
        elements.tableViewBtn.innerHTML = '<span class="material-icons-round">table_chart</span>';
      } else {
        visualizationContainer.style.display = 'none';
        elements.tableViewBtn.innerHTML = '<span class="material-icons-round">insert_chart</span>';
      }
    });
  }
  
  // Quick filters
  if (elements.quickFilters) {
    elements.quickFilters.forEach(filter => {
      filter.addEventListener('click', () => {
        const range = filter.dataset.range;
        updateActiveQuickFilter(range);
        setTimeRange(range);
      });
    });
  }
}

/**
 * Set time range based on quick filter
 * @param {string} range - Time range identifier
 */
function setTimeRange(range) {
  // Get current time
  const now = DateTime.now().setZone('Asia/Kolkata');
  
  // Set end date to current time
  const endDate = now;
  
  // Set start date based on range
  let startDate = now;
  
  switch(range) {
    case '1h':
      startDate = now.minus({ hours: 1 });
      break;
    case '6h':
      startDate = now.minus({ hours: 6 });
      break;
    case '24h':
      startDate = now.minus({ hours: 24 });
      break;
    case '7d':
      startDate = now.minus({ days: 7 });
      break;
    case '30d':
      startDate = now.minus({ days: 30 });
      break;
    case 'custom':
      // Don't change dates, just return
      return;
    default:
      startDate = now.minus({ hours: 1 });
  }
  
  // Format dates for the input fields
  if (elements.startDate) {
    elements.startDate.value = startDate.toFormat("yyyy-MM-dd'T'HH:mm");
  }
  
  if (elements.endDate) {
    elements.endDate.value = endDate.toFormat("yyyy-MM-dd'T'HH:mm");
  }
  
  // Fetch data with the new date range
  fetchHistoricalData();
}

/**
 * Update active quick filter
 * @param {string} activeFilter - Active filter identifier
 */
function updateActiveQuickFilter(activeFilter) {
  if (!elements.quickFilters) return;
  
  elements.quickFilters.forEach(filter => {
    filter.classList.remove('active');
    if (filter.dataset.range === activeFilter) {
      filter.classList.add('active');
    }
  });
}

/**
 * Fetch historical data from the backend
 */
async function fetchHistoricalData() {
  if (!elements.startDate || !elements.endDate || !elements.dataRows) return;
  
  try {
    clearMessage();

    // Show loading state
    elements.dataRows.innerHTML = '<tr><td colspan="12" class="loading">Loading data...</td></tr>';

    // Get the input values
    const startDate = elements.startDate.value;
    const endDate = elements.endDate.value;

    // Validate inputs
    if (!startDate || !endDate) {
      showMessage('Please select both start and end dates', 'error');
      return null;
    }

    // Parse dates from input fields
    const startDateObj = DateTime.fromISO(startDate);
    const endDateObj = DateTime.fromISO(endDate);

    // Format dates for server in YYYY-MM-DD HH:MM:SS format
    const startFormatted = startDateObj.toFormat('yyyy-MM-dd HH:mm:ss');
    const endFormatted = endDateObj.toFormat('yyyy-MM-dd HH:mm:ss');

    // Prepare request parameters
    const limit = elements.limit ? elements.limit.value : 25;
    const offset = (state.currentPage - 1) * parseInt(limit);
    const cacheBuster = new Date().getTime();

    // Build URL
    const url = `../backend/get_historical_data.php?start=${encodeURIComponent(startFormatted)}&end=${encodeURIComponent(endFormatted)}&limit=${limit}&offset=${offset}&_=${cacheBuster}`;

    // Fetch data
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    // Parse response
    const responseText = await response.text();
    
    let data;
    try {
      data = JSON.parse(responseText);
    } catch (parseError) {
      throw new Error(`Failed to parse JSON response: ${parseError.message}`);
    }

    // Handle error in response
    if (data.error) {
      showMessage(`Error: ${data.error}`, 'error');
      return null;
    }

    // Process data
    state.data = Array.isArray(data.data) ? data.data : [];
    state.totalPages = Math.ceil(data.total / parseInt(limit));

    // Check if we have no data but sample data exists
    if (state.data.length === 0 && data.debug && data.debug.test_query_result && data.debug.test_query_result.length > 0) {
      const sampleData = data.debug.test_query_result[0];
      const sampleTimestamp = sampleData.timestamp;

      showMessage(`
        <h3>No data found for the selected time range</h3>
        <p>There is data in the database, but not for the specific time range you selected.</p>
        <p>We found data at: <strong>${sampleTimestamp}</strong></p>
        <button id="useSampleDateBtn" class="button button-primary" style="margin-top: 10px;">
          <span class="material-icons-round">schedule</span>
          View Data Around This Time
        </button>
      `, 'warning');

      // Add event listener to the button
      document.getElementById('useSampleDateBtn').addEventListener('click', () => {
        const sampleDate = DateTime.fromSQL(sampleTimestamp);
        
        // Set start date to 1 hour before sample time
        const startDate = sampleDate.minus({ hours: 1 });
        
        // Set end date to 1 hour after sample time
        const endDate = sampleDate.plus({ hours: 1 });
        
        // Format dates for the input fields
        elements.startDate.value = startDate.toFormat("yyyy-MM-dd'T'HH:mm");
        elements.endDate.value = endDate.toFormat("yyyy-MM-dd'T'HH:mm");
        
        // Fetch data with the new date range
        fetchHistoricalData();
      });
    } else if (state.data.length === 0) {
      showMessage('No data found for the selected period', 'warning');
    } else {
      showMessage(`Found ${state.data.length} records`, 'success');
    }

    // Update UI
    renderTable();
    updatePagination();
    updateChart();

    return data;
  } catch (error) {
    console.error('Error fetching historical data:', error);
    showMessage(`Error: ${error.message}`, 'error');
    return null;
  }
}

/**
 * Render the data table
 */
function renderTable() {
  if (!elements.dataRows) return;
  
  if (state.data.length === 0) {
    elements.dataRows.innerHTML = '<tr><td colspan="12" style="text-align: center;">No data available</td></tr>';
    return;
  }

  let html = '';
  state.data.forEach(row => {
    const timestamp = utils.formatTimestamp(row.timestamp);
    html += `<tr>
      <td>${timestamp}</td>
      <td>${utils.formatNumber(row.voltage_1)}</td>
      <td>${utils.formatNumber(row.voltage_2)}</td>
      <td>${utils.formatNumber(row.voltage_3)}</td>
      <td>${utils.formatNumber(row.current_1)}</td>
      <td>${utils.formatNumber(row.current_2)}</td>
      <td>${utils.formatNumber(row.current_3)}</td>
      <td>${utils.formatNumber(row.pf_1)}</td>
      <td>${utils.formatNumber(row.pf_2)}</td>
      <td>${utils.formatNumber(row.pf_3)}</td>
      <td>${utils.formatNumber(row.total_kw)}</td>
      <td>${utils.formatNumber(row.frequency)}</td>
    </tr>`;
  });

  elements.dataRows.innerHTML = html;
}

/**
 * Update pagination controls
 */
function updatePagination() {
  if (!elements.pageInfo || !elements.prevBtn || !elements.nextBtn) return;
  
  elements.pageInfo.textContent = `Page ${state.currentPage} of ${state.totalPages || 1}`;
  elements.prevBtn.disabled = state.currentPage <= 1;
  elements.nextBtn.disabled = state.currentPage >= state.totalPages;
}

/**
 * Update chart with data
 */
function updateChart() {
  if (!elements.historyChart) return;
  
  // Destroy existing chart if it exists
  if (state.chartInstance) {
    state.chartInstance.destroy();
  }
  
  if (state.data.length === 0) {
    return;
  }
  
  // Prepare data for chart
  const labels = state.data.map(d => DateTime.fromSQL(d.timestamp).toJSDate());
  
  // Get data for the active parameter
  const getDataset = (paramName, label, color) => {
    return {
      label: label,
      data: state.data.map(d => d[paramName]),
      borderColor: color,
      backgroundColor: color + '33', // Add 20% opacity
      borderWidth: 2,
      tension: 0.4,
      pointRadius: 0, // Hide dots by default
      pointHoverRadius: 6, // Show dots on hover
      pointBackgroundColor: '#0f172a',
      pointBorderWidth: 2,
      pointBorderColor: color,
      shadowBlur: 10,
      shadowColor: color
    };
  };
  
  // Create datasets based on chart type
  let datasets = [];
  
  if (state.chartType === 'line' || state.chartType === 'bar') {
    datasets = [
      getDataset('voltage_1', 'V1N (V)', '#06b6d4'),
      getDataset('voltage_2', 'V2N (V)', '#3b82f6'),
      getDataset('voltage_3', 'V3N (V)', '#8b5cf6')
    ];
  }
  
  // Create chart
  const ctx = elements.historyChart.getContext('2d');
  state.chartInstance = new Chart(ctx, {
    type: state.chartType,
    data: {
      labels: labels,
      datasets: datasets
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        tooltip: {
          mode: 'index',
          intersect: false,
          backgroundColor: 'rgba(15, 23, 42, 0.85)',
          titleColor: '#f8fafc',
          bodyColor: '#cbd5e1',
          borderColor: 'rgba(148, 163, 184, 0.2)',
          borderWidth: 1,
          padding: 12,
          displayColors: true,
          cornerRadius: 8,
          callbacks: {
            label: (context) => {
              let label = context.dataset.label || '';
              if (label) {
                label += ': ';
              }
              if (context.parsed.y !== null) {
                label += context.parsed.y.toFixed(3);
              }
              return label;
            },
            labelTextColor: (context) => {
              return context.dataset.borderColor;
            }
          }
        },
        legend: {
          position: 'top',
          labels: {
            color: '#f8fafc',
            usePointStyle: true,
            pointStyle: 'rectRounded',
            padding: 15,
            font: {
              family: "'JetBrains Mono', monospace",
              size: 11
            }
          }
        }
      },
      scales: {
        x: {
          type: 'time',
          time: {
            unit: 'minute',
            displayFormats: {
              minute: 'HH:mm'
            },
            tooltipFormat: 'yyyy-MM-dd HH:mm:ss'
          },
          grid: {
            color: 'rgba(148, 163, 184, 0.15)',
            lineWidth: 0.5
          },
          border: {
            display: false
          },
          ticks: {
            color: '#cbd5e1',
            font: {
              family: "'JetBrains Mono', monospace",
              size: 10
            },
            maxRotation: 0
          }
        },
        y: {
          grid: {
            color: 'rgba(148, 163, 184, 0.15)',
            lineWidth: 0.5
          },
          border: {
            display: false
          },
          ticks: {
            color: '#cbd5e1',
            font: {
              family: "'JetBrains Mono', monospace",
              size: 10
            },
            callback: function(value) {
              return value.toFixed(2);
            }
          }
        }
      },
      animation: {
        duration: 1000,
        easing: 'easeOutQuart'
      },
      elements: {
        line: {
          tension: 0.4
        },
        point: {
          radius: 0, // Hide dots by default
          hoverRadius: 6 // Show dots on hover
        }
      },
      interaction: {
        mode: 'index',
        intersect: false
      }
    }
  });
}

/**
 * Export data as CSV
 */
async function exportCSV() {
  if (!elements.startDate || !elements.endDate || !elements.exportBtn) return;
  
  try {
    // Show loading message and disable the export button
    showMessage('Preparing CSV export of all data...', 'info');

    // Save original button text and disable the button
    const originalBtnText = elements.exportBtn.innerHTML;
    elements.exportBtn.disabled = true;
    elements.exportBtn.innerHTML = `<span class="spinner"></span> Exporting...`;

    // Get the input values
    const startDate = elements.startDate.value;
    const endDate = elements.endDate.value;

    // Validate inputs
    if (!startDate || !endDate) {
      showMessage('Please select both start and end dates', 'error');
      elements.exportBtn.disabled = false;
      elements.exportBtn.innerHTML = originalBtnText;
      return;
    }

    // Parse dates from input fields
    const startDateObj = DateTime.fromISO(startDate);
    const endDateObj = DateTime.fromISO(endDate);

    // Format dates for server in YYYY-MM-DD HH:MM:SS format
    const startFormatted = startDateObj.toFormat('yyyy-MM-dd HH:mm:ss');
    const endFormatted = endDateObj.toFormat('yyyy-MM-dd HH:mm:ss');

    // Use the backend export_csv.php endpoint to get all data at once
    const url = `../backend/export_csv.php?parameter=all&start=${encodeURIComponent(startFormatted)}&end=${encodeURIComponent(endFormatted)}`;

    showMessage('Downloading all data as CSV...', 'info');

    // Create a hidden link and trigger the download
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = `electrical_data_${DateTime.now().toFormat('yyyy-MM-dd')}.csv`;
    document.body.appendChild(a);
    a.click();

    // Clean up and restore button after a delay
    setTimeout(() => {
      document.body.removeChild(a);
      elements.exportBtn.disabled = false;
      elements.exportBtn.innerHTML = originalBtnText;
      showMessage('CSV export initiated. If the file is large, it may take a moment to download.', 'success');
    }, 3000);
  } catch (error) {
    console.error('Error exporting CSV:', error);
    showMessage(`Error exporting CSV: ${error.message}`, 'error');

    // Restore button on error
    elements.exportBtn.disabled = false;
    elements.exportBtn.innerHTML = `<span class="material-icons-round">file_download</span> Export CSV`;
  }
}

/**
 * Show a message to the user
 * @param {string} message - Message content
 * @param {string} type - Message type (info, success, warning, error)
 */
function showMessage(message, type = 'info') {
  if (!elements.messageContainer) return;
  
  elements.messageContainer.innerHTML = `
    <div class="message ${type}">
      ${message}
    </div>
  `;
  
  // Auto-hide success messages after 5 seconds
  if (type === 'success') {
    setTimeout(() => {
      if (elements.messageContainer.querySelector('.message.success')) {
        clearMessage();
      }
    }, 5000);
  }
}

/**
 * Clear any displayed messages
 */
function clearMessage() {
  if (elements.messageContainer) {
    elements.messageContainer.innerHTML = '';
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initHistoryPage);
