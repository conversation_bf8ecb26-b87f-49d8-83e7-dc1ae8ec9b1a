<?php
// Initialize the session
session_start();

// Check if the user is logged in, if not redirect to login page
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: login.php");
    exit;
}

// Include Vite helper functions
require_once 'vite-helper.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Reports - Online Data Logger System</title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="images/online-data-logger-logo-new.svg">
    
    <!-- Vite Assets -->
    <?php echo viteClientScript(); ?>
    <?php echo viteCssTag('reports'); ?>
    <?php echo viteScriptTag('reports'); ?>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar Navigation -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="images/online-data-logger-sidebar.svg" alt="Online Data Logger System" class="company-logo" width="180">
            </div>

            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="advanced_dashboard.php">
                            <span class="material-icons-round">dashboard</span>
                            <span>Live Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="history.php">
                            <span class="material-icons-round">history</span>
                            <span>Historical Data</span>
                        </a>
                    </li>
                    <li>
                        <a href="stability_analysis.php">
                            <span class="material-icons-round">analytics</span>
                            <span>Stability Analysis</span>
                        </a>
                    </li>
                    <li>
                        <a href="alerts_events.php">
                            <span class="material-icons-round">warning</span>
                            <span>Alerts & Events</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="reports.php">
                            <span class="material-icons-round">insights</span>
                            <span>Reports</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" id="settingsLink">
                            <span class="material-icons-round">settings</span>
                            <span>System Settings</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <div class="system-status">
                    <span class="status-indicator online"></span>
                    <span>System Online</span>
                </div>
                <a href="logout.php" class="logout-button">
                    <span class="material-icons-round">logout</span>
                    <span>Logout</span>
                </a>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Status Header -->
            <header class="status-header">
                <div class="status-title">
                    <div class="header-logo-container">
                        <img src="images/online-data-logger-logo-new.svg" alt="Online Data Logger System" class="header-logo">
                    </div>
                    <div class="subtitle">System Reports & Analytics</div>
                </div>

                <div class="status-boxes">
                    <div class="status-box system">
                        <div class="status-icon">
                            <span class="material-icons-round">insights</span>
                        </div>
                        <div class="status-text">
                            <div class="status-label">Report Status</div>
                            <div class="status-value">Ready</div>
                        </div>
                    </div>

                    <div class="status-box">
                        <div class="status-icon">
                            <span class="material-icons-round">schedule</span>
                        </div>
                        <div class="status-text">
                            <div class="status-label">Timezone</div>
                            <div class="status-value">IST (UTC+5:30)</div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Message Container -->
            <div id="messageContainer"></div>

            <!-- Report Generation -->
            <div class="reports-container">
                <div class="report-generator">
                    <div class="section-header">
                        <h3>
                            <span class="material-icons-round">description</span>
                            Generate Reports
                        </h3>
                    </div>

                    <div class="report-controls">
                        <div class="control-group">
                            <label for="reportType">Report Type</label>
                            <select id="reportType">
                                <option value="daily">Daily Summary</option>
                                <option value="weekly">Weekly Analysis</option>
                                <option value="monthly">Monthly Report</option>
                                <option value="custom">Custom Period</option>
                                <option value="stability">Stability Analysis</option>
                                <option value="power_quality">Power Quality</option>
                                <option value="energy_efficiency">Energy Efficiency</option>
                                <option value="compliance">Compliance Report</option>
                            </select>
                        </div>

                        <div class="control-group">
                            <label for="startDate">Start Date & Time</label>
                            <input type="datetime-local" id="startDate" name="startDate">
                        </div>

                        <div class="control-group">
                            <label for="endDate">End Date & Time</label>
                            <input type="datetime-local" id="endDate" name="endDate">
                        </div>

                        <div class="control-group">
                            <label for="timeRange">Quick Range</label>
                            <select id="timeRange">
                                <option value="custom">Custom Range</option>
                                <option value="24h" selected>Last 24 Hours</option>
                                <option value="7d">Last 7 Days</option>
                                <option value="30d">Last 30 Days</option>
                                <option value="90d">Last 3 Months</option>
                                <option value="1y">Last Year</option>
                            </select>
                        </div>

                        <div class="control-group">
                            <label for="reportFormat">Format</label>
                            <select id="reportFormat">
                                <option value="pdf">PDF Report</option>
                                <option value="excel">Excel Spreadsheet</option>
                                <option value="csv">CSV Data</option>
                                <option value="html">HTML Report</option>
                            </select>
                        </div>

                        <div class="control-group">
                            <label>&nbsp;</label>
                            <button id="generateReportBtn" class="button button-primary">
                                <span class="material-icons-round">play_arrow</span>
                                Generate Report
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Report Templates -->
                <div class="report-templates">
                    <div class="section-header">
                        <h3>
                            <span class="material-icons-round">library_books</span>
                            Report Templates
                        </h3>
                    </div>

                    <div class="templates-grid" id="templatesContainer">
                        <!-- Templates will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Scheduled Reports -->
                <div class="scheduled-reports">
                    <div class="section-header">
                        <h3>
                            <span class="material-icons-round">schedule</span>
                            Scheduled Reports
                        </h3>
                        <div class="section-actions">
                            <button id="addScheduleBtn" class="button button-secondary">
                                <span class="material-icons-round">add</span>
                                Add Schedule
                            </button>
                        </div>
                    </div>

                    <div class="schedules-list" id="schedulesContainer">
                        <!-- Scheduled reports will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Recent Reports -->
                <div class="recent-reports">
                    <div class="section-header">
                        <h3>
                            <span class="material-icons-round">history</span>
                            Recent Reports
                        </h3>
                    </div>

                    <div class="reports-table-container">
                        <table class="reports-table">
                            <thead>
                                <tr>
                                    <th>Generated</th>
                                    <th>Report Type</th>
                                    <th>Period</th>
                                    <th>Format</th>
                                    <th>Size</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="reportsTableBody">
                                <!-- Reports will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="pagination">
                        <div class="page-info" id="pageInfo">Page 1 of 1</div>
                        <div class="pagination-controls">
                            <button id="prevBtn" class="button button-secondary" disabled>
                                <span class="material-icons-round">chevron_left</span>
                                Previous
                            </button>
                            <button id="nextBtn" class="button button-secondary" disabled>
                                <span class="material-icons-round">chevron_right</span>
                                Next
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Report Analytics -->
                <div class="report-analytics">
                    <div class="section-header">
                        <h3>
                            <span class="material-icons-round">trending_up</span>
                            Report Analytics
                        </h3>
                    </div>

                    <div class="analytics-grid" id="analyticsContainer">
                        <!-- Analytics will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Report Generation Modal -->
    <div id="reportModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h4>Generating Report</h4>
                <button class="modal-close" id="closeModal">
                    <span class="material-icons-round">close</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">Initializing...</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
