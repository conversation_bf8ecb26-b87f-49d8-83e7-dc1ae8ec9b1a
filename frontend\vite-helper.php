<?php
/**
 * Vite Helper Functions
 * 
 * This file provides helper functions to integrate Vite.js with PHP
 */

/**
 * Determine if we're in development mode
 * 
 * @return bool True if in development mode
 */
function isDev(): bool {
    // Check if the Vite dev server is running
    $devServerRunning = false;
    $handle = @fsockopen('localhost', 3000);
    if ($handle) {
        $devServerRunning = true;
        fclose($handle);
    }
    
    return $devServerRunning;
}

/**
 * Get the Vite client script tag for development
 * 
 * @return string The script tag for the Vite client
 */
function viteClientScript(): string {
    if (!isDev()) {
        return '';
    }
    
    return '<script type="module" src="http://localhost:3000/@vite/client"></script>';
}

/**
 * Get the script tag for a Vite entry point
 * 
 * @param string $entry The entry point name
 * @return string The script tag
 */
function viteScriptTag(string $entry): string {
    if (isDev()) {
        return sprintf(
            '<script type="module" src="http://localhost:3000/src/js/%s.js"></script>',
            $entry
        );
    }
    
    $manifest = getManifest();
    
    if (!isset($manifest[$entry])) {
        error_log("Entry point not found in manifest: $entry");
        return '';
    }

    $file = $manifest[$entry]['file'];

    return sprintf(
        '<script type="module" src="/online data logger/frontend/assets/%s"></script>',
        $file
    );
}

/**
 * Get the CSS link tag for a Vite entry point
 *
 * @param string $entry The entry point name
 * @return string The CSS link tag
 */
function viteCssTag(string $entry): string {
    if (isDev()) {
        // In dev mode, CSS is injected by Vite
        return '';
    }

    $manifest = getManifest();

    if (!isset($manifest[$entry])) {
        error_log("Entry point not found in manifest: $entry");
        // Return fallback CSS
        return '<link rel="stylesheet" href="/online data logger/frontend/fallback-styles.css">';
    }

    if (!isset($manifest[$entry]['css']) || empty($manifest[$entry]['css'])) {
        // Return fallback CSS if no CSS found
        return '<link rel="stylesheet" href="/online data logger/frontend/fallback-styles.css">';
    }

    $tags = '';
    foreach ($manifest[$entry]['css'] as $css) {
        $tags .= sprintf(
            '<link rel="stylesheet" href="/online data logger/frontend/assets/%s">',
            $css
        );
    }

    return $tags;
}

/**
 * Get the Vite manifest file
 * 
 * @return array The manifest data
 */
function getManifest(): array {
    static $manifest = null;
    
    if ($manifest !== null) {
        return $manifest;
    }
    
    $manifestPath = __DIR__ . '/assets/manifest.json';
    
    if (!file_exists($manifestPath)) {
        error_log("Vite manifest not found: $manifestPath");
        return [];
    }
    
    $manifestJson = file_get_contents($manifestPath);
    $manifest = json_decode($manifestJson, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("Error parsing Vite manifest: " . json_last_error_msg());
        return [];
    }
    
    // Convert entry points to a more usable format
    $result = [];
    foreach ($manifest as $key => $value) {
        // Extract entry name from path like "src/js/history.js" -> "history"
        if (preg_match('/src\/js\/([^.]+)\.js$/', $key, $matches)) {
            $entryName = $matches[1];
            $result[$entryName] = $value;
        }
    }

    return $result;
}
