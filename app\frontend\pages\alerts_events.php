<?php
// Include Vite helper functions
require_once 'vite-helper.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Alerts & Events - Online Data Logger System</title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="../assets/images/online-data-logger-logo-new.svg">
    
    <!-- Vite Assets -->
    <?php echo viteClientScript(); ?>
    <?php echo viteCssTag('alerts'); ?>
    <?php echo viteScriptTag('alerts'); ?>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar Navigation -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../assets/images/online-data-logger-sidebar.svg" alt="Online Data Logger System" class="company-logo" width="180">
            </div>

            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="advanced_dashboard.php">
                            <span class="material-icons-round">dashboard</span>
                            <span>Live Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="history.php">
                            <span class="material-icons-round">history</span>
                            <span>Historical Data</span>
                        </a>
                    </li>
                    <li>
                        <a href="stability_analysis.php">
                            <span class="material-icons-round">analytics</span>
                            <span>Stability Analysis</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="alerts_events.php">
                            <span class="material-icons-round">warning</span>
                            <span>Alerts & Events</span>
                        </a>
                    </li>
                    <li>
                        <a href="reports.php">
                            <span class="material-icons-round">insights</span>
                            <span>Reports</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" id="settingsLink">
                            <span class="material-icons-round">settings</span>
                            <span>System Settings</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <div class="system-status">
                    <span class="status-indicator online"></span>
                    <span>System Online</span>
                </div>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Status Header -->
            <header class="status-header">
                <div class="status-title">
                    <div class="header-logo-container">
                        <img src="../assets/images/online-data-logger-logo-new.svg" alt="Online Data Logger System" class="header-logo">
                    </div>
                    <div class="subtitle">System Alerts & Events Management</div>
                </div>

                <div class="status-boxes">
                    <div class="status-box critical" id="criticalAlertsBox">
                        <div class="status-icon">
                            <span class="material-icons-round">error</span>
                        </div>
                        <div class="status-text">
                            <div class="status-label">Critical Alerts</div>
                            <div class="status-value" id="criticalCount">0</div>
                        </div>
                    </div>

                    <div class="status-box warning" id="warningAlertsBox">
                        <div class="status-icon">
                            <span class="material-icons-round">warning</span>
                        </div>
                        <div class="status-text">
                            <div class="status-label">Warnings</div>
                            <div class="status-value" id="warningCount">0</div>
                        </div>
                    </div>

                    <div class="status-box info" id="infoAlertsBox">
                        <div class="status-icon">
                            <span class="material-icons-round">info</span>
                        </div>
                        <div class="status-text">
                            <div class="status-label">Info Events</div>
                            <div class="status-value" id="infoCount">0</div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Message Container -->
            <div id="messageContainer"></div>

            <!-- Alert Controls -->
            <div class="alerts-container">
                <div class="alert-controls">
                    <div class="control-group">
                        <label for="alertType">Alert Type</label>
                        <select id="alertType">
                            <option value="all">All Alerts</option>
                            <option value="critical">Critical</option>
                            <option value="warning">Warning</option>
                            <option value="info">Info</option>
                            <option value="resolved">Resolved</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label for="startDate">Start Date & Time</label>
                        <input type="datetime-local" id="startDate" name="startDate">
                    </div>

                    <div class="control-group">
                        <label for="endDate">End Date & Time</label>
                        <input type="datetime-local" id="endDate" name="endDate">
                    </div>

                    <div class="control-group">
                        <label for="timeRange">Quick Range</label>
                        <select id="timeRange">
                            <option value="custom">Custom Range</option>
                            <option value="1h" selected>Last 1 Hour</option>
                            <option value="6h">Last 6 Hours</option>
                            <option value="24h">Last 24 Hours</option>
                            <option value="7d">Last 7 Days</option>
                            <option value="30d">Last 30 Days</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label>&nbsp;</label>
                        <button id="refreshAlertsBtn" class="button button-primary">
                            <span class="material-icons-round">refresh</span>
                            Refresh Alerts
                        </button>
                    </div>
                </div>

                <!-- Alert Statistics -->
                <div class="alert-stats-grid" id="alertStatsContainer">
                    <!-- Stats will be populated by JavaScript -->
                </div>

                <!-- Active Alerts -->
                <div class="active-alerts-section">
                    <div class="section-header">
                        <h3>
                            <span class="material-icons-round">priority_high</span>
                            Active Alerts
                        </h3>
                        <div class="section-actions">
                            <button id="acknowledgeAllBtn" class="button button-secondary">
                                <span class="material-icons-round">check_circle</span>
                                Acknowledge All
                            </button>
                            <button id="clearResolvedBtn" class="button button-secondary">
                                <span class="material-icons-round">clear_all</span>
                                Clear Resolved
                            </button>
                        </div>
                    </div>
                    
                    <div class="alerts-list" id="activeAlertsList">
                        <!-- Active alerts will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Event Log -->
                <div class="event-log-section">
                    <div class="section-header">
                        <h3>
                            <span class="material-icons-round">event_note</span>
                            Event Log
                        </h3>
                        <div class="section-actions">
                            <button id="exportEventsBtn" class="button button-secondary">
                                <span class="material-icons-round">file_download</span>
                                Export Events
                            </button>
                        </div>
                    </div>
                    
                    <div class="events-table-container">
                        <table class="events-table">
                            <thead>
                                <tr>
                                    <th>Timestamp</th>
                                    <th>Type</th>
                                    <th>Severity</th>
                                    <th>Source</th>
                                    <th>Message</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="eventsTableBody">
                                <!-- Events will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="pagination">
                        <div class="page-info" id="pageInfo">Page 1 of 1</div>
                        <div class="pagination-controls">
                            <button id="prevBtn" class="button button-secondary" disabled>
                                <span class="material-icons-round">chevron_left</span>
                                Previous
                            </button>
                            <button id="nextBtn" class="button button-secondary" disabled>
                                <span class="material-icons-round">chevron_right</span>
                                Next
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Alert Configuration -->
                <div class="alert-config-section">
                    <div class="section-header">
                        <h3>
                            <span class="material-icons-round">tune</span>
                            Alert Configuration
                        </h3>
                    </div>
                    
                    <div class="config-grid" id="alertConfigContainer">
                        <!-- Configuration options will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
