@import "./src/scss/variables.scss";@import 'variables';

// Alerts & Events Page Styles
.alerts-container {
  background: rgba(30, 41, 59, 0.7);
  backdrop-filter: blur(10px);
  border-radius: $border-radius-lg;
  box-shadow: $futuristic-shadow;
  padding: 25px;
  margin-bottom: 25px;
  border: 1px solid $futuristic-border;
  transition: $transition-normal;

  &:hover {
    box-shadow: $futuristic-glow;
  }
}

// Alert Controls
.alert-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(15, 23, 42, 0.7);
  border-radius: $border-radius-md;
  border: 1px solid $futuristic-border;
}

.control-group {
  display: flex;
  flex-direction: column;

  label {
    color: $futuristic-text-secondary;
    font-size: $font-size-sm;
    font-weight: 500;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
  }

  select,
  input {
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid $futuristic-border;
    border-radius: $border-radius-sm;
    color: $futuristic-text;
    padding: 10px 12px;
    font-size: $font-size-sm;
    transition: $transition-fast;

    &:focus {
      outline: none;
      border-color: $futuristic-accent-1;
      box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.2);
    }

    &:hover {
      border-color: rgba(148, 163, 184, 0.4);
    }
  }
}

// Alert Statistics
.alert-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: rgba(15, 23, 42, 0.7);
  border-radius: $border-radius-md;
  padding: 20px;
  border: 1px solid $futuristic-border;
  display: flex;
  align-items: center;
  transition: $transition-normal;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: $border-radius-md;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;

  .material-icons-round {
    font-size: 24px;
  }

  &.critical {
    background: rgba(239, 68, 68, 0.15);
    color: #ef4444;
  }

  &.warning {
    background: rgba(245, 158, 11, 0.15);
    color: #f59e0b;
  }

  &.info {
    background: rgba(59, 130, 246, 0.15);
    color: #3b82f6;
  }

  &.resolved {
    background: rgba(16, 185, 129, 0.15);
    color: #10b981;
  }
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: $font-size-xl;
  font-weight: 700;
  color: $futuristic-text;
  margin-bottom: 4px;
}

.stat-label {
  font-size: $font-size-sm;
  color: $futuristic-text-secondary;
  font-weight: 500;
}

// Section Headers
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid $futuristic-border;

  h3 {
    display: flex;
    align-items: center;
    color: $futuristic-text;
    font-size: $font-size-lg;
    font-weight: 600;
    margin: 0;

    .material-icons-round {
      margin-right: 10px;
      color: $futuristic-accent-1;
    }
  }
}

.section-actions {
  display: flex;
  gap: 10px;
}

// Active Alerts
.active-alerts-section,
.event-log-section,
.alert-config-section {
  margin-bottom: 30px;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.alert-item {
  background: rgba(15, 23, 42, 0.7);
  border-radius: $border-radius-md;
  padding: 20px;
  border: 1px solid $futuristic-border;
  border-left: 4px solid $futuristic-border;
  display: flex;
  align-items: center;
  transition: $transition-normal;

  &:hover {
    background: rgba(15, 23, 42, 0.9);
    transform: translateX(5px);
  }

  &.critical {
    border-left-color: #ef4444;
    background: rgba(239, 68, 68, 0.05);
  }

  &.warning {
    border-left-color: #f59e0b;
    background: rgba(245, 158, 11, 0.05);
  }

  &.info {
    border-left-color: #3b82f6;
    background: rgba(59, 130, 246, 0.05);
  }
}

.alert-icon {
  width: 40px;
  height: 40px;
  border-radius: $border-radius-sm;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  background: rgba(6, 182, 212, 0.15);
  color: $futuristic-accent-1;

  .material-icons-round {
    font-size: 20px;
  }
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: $font-size-md;
  font-weight: 600;
  color: $futuristic-text;
  margin-bottom: 5px;
}

.alert-message {
  font-size: $font-size-sm;
  color: $futuristic-text-secondary;
  margin-bottom: 8px;
  line-height: 1.4;
}

.alert-meta {
  display: flex;
  gap: 15px;
  font-size: $font-size-xs;
  color: $futuristic-text-secondary;
}

.alert-source {
  font-family: 'JetBrains Mono', monospace;
}

.alert-time {
  color: $futuristic-accent-1;
}

.alert-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: $border-radius-sm;
  background: rgba(30, 41, 59, 0.7);
  color: $futuristic-text-secondary;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: $transition-fast;

  &:hover {
    background: rgba(6, 182, 212, 0.15);
    color: $futuristic-accent-1;
  }

  &.acknowledge:hover {
    background: rgba(16, 185, 129, 0.15);
    color: #10b981;
  }

  &.dismiss:hover {
    background: rgba(239, 68, 68, 0.15);
    color: #ef4444;
  }

  .material-icons-round {
    font-size: 18px;
  }
}

.no-alerts {
  text-align: center;
  padding: 40px;
  color: $futuristic-text-secondary;
  font-style: italic;
}

// Events Table
.events-table-container {
  background: rgba(15, 23, 42, 0.7);
  border-radius: $border-radius-md;
  border: 1px solid $futuristic-border;
  overflow: hidden;
  margin-bottom: 20px;
}

.events-table {
  width: 100%;
  border-collapse: collapse;

  th,
  td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  }

  th {
    background: rgba(30, 41, 59, 0.7);
    color: $futuristic-text;
    font-weight: 600;
    font-size: $font-size-sm;
    letter-spacing: 0.5px;
  }

  td {
    color: $futuristic-text-secondary;
    font-size: $font-size-sm;
  }

  .event-row {
    transition: $transition-fast;

    &:hover {
      background: rgba(30, 41, 59, 0.5);
    }

    &.critical {
      border-left: 3px solid #ef4444;
    }

    &.warning {
      border-left: 3px solid #f59e0b;
    }

    &.info {
      border-left: 3px solid #3b82f6;
    }
  }
}

.severity-badge,
.status-badge {
  padding: 4px 8px;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &.critical {
    background: rgba(239, 68, 68, 0.15);
    color: #ef4444;
  }

  &.warning {
    background: rgba(245, 158, 11, 0.15);
    color: #f59e0b;
  }

  &.info {
    background: rgba(59, 130, 246, 0.15);
    color: #3b82f6;
  }

  &.active {
    background: rgba(6, 182, 212, 0.15);
    color: $futuristic-accent-1;
  }

  &.resolved {
    background: rgba(16, 185, 129, 0.15);
    color: #10b981;
  }
}

.action-buttons {
  display: flex;
  gap: 5px;
}

// Alert Configuration
.config-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

.config-item {
  background: rgba(15, 23, 42, 0.7);
  border-radius: $border-radius-md;
  padding: 20px;
  border: 1px solid $futuristic-border;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr auto;
  gap: 20px;
  align-items: center;
  transition: $transition-normal;

  &:hover {
    background: rgba(15, 23, 42, 0.9);
  }
}

.config-parameter {
  font-weight: 600;
  color: $futuristic-text;
}

.config-threshold {
  font-family: 'JetBrains Mono', monospace;
  color: $futuristic-accent-1;
}

.config-status {
  .status-indicator {
    padding: 4px 8px;
    border-radius: $border-radius-sm;
    font-size: $font-size-xs;
    font-weight: 500;

    &.enabled {
      background: rgba(16, 185, 129, 0.15);
      color: #10b981;
    }

    &.disabled {
      background: rgba(148, 163, 184, 0.15);
      color: $futuristic-text-secondary;
    }
  }
}

.config-actions {
  .action-btn {
    &.edit:hover {
      background: rgba(245, 158, 11, 0.15);
      color: #f59e0b;
    }
  }
}

// Status boxes in header
.status-box {
  &.critical {
    .status-icon {
      background: rgba(239, 68, 68, 0.15);
      color: #ef4444;
    }
  }

  &.warning {
    .status-icon {
      background: rgba(245, 158, 11, 0.15);
      color: #f59e0b;
    }
  }

  &.info {
    .status-icon {
      background: rgba(59, 130, 246, 0.15);
      color: #3b82f6;
    }
  }
}

// Responsive Design
@media (max-width: $breakpoint-md) {
  .alert-controls {
    grid-template-columns: 1fr;
  }

  .alert-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .alert-item {
    flex-direction: column;
    align-items: flex-start;

    .alert-actions {
      margin-top: 15px;
      align-self: flex-end;
    }
  }

  .config-item {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .events-table {
    font-size: $font-size-xs;

    th,
    td {
      padding: 10px 8px;
    }
  }
}

@media (max-width: $breakpoint-sm) {
  .alert-stats-grid {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;

    .section-actions {
      margin-top: 10px;
    }
  }
}
