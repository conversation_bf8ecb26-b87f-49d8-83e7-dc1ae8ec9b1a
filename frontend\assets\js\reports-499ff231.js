import{D as i,u as d}from"./main-35d3972b.js";let t={};const r={reportType:"daily",reportFormat:"pdf",currentPage:1,totalPages:1,reports:[],schedules:[],isGenerating:!1};function y(){t={reportType:document.getElementById("reportType"),startDate:document.getElementById("startDate"),endDate:document.getElementById("endDate"),timeRange:document.getElementById("timeRange"),reportFormat:document.getElementById("reportFormat"),generateReportBtn:document.getElementById("generateReportBtn"),addScheduleBtn:document.getElementById("addScheduleBtn"),templatesContainer:document.getElementById("templatesContainer"),schedulesContainer:document.getElementById("schedulesContainer"),reportsTableBody:document.getElementById("reportsTableBody"),analyticsContainer:document.getElementById("analyticsContainer"),prevBtn:document.getElementById("prevBtn"),nextBtn:document.getElementById("nextBtn"),pageInfo:document.getElementById("pageInfo"),messageContainer:document.getElementById("messageContainer"),reportModal:document.getElementById("reportModal"),closeModal:document.getElementById("closeModal"),progressFill:document.getElementById("progressFill"),progressText:document.getElementById("progressText")},l("24h"),f(),v(),w(),u(),b()}function f(){t.timeRange&&t.timeRange.addEventListener("change",n=>{n.target.value!=="custom"&&l(n.target.value)}),t.reportType&&t.reportType.addEventListener("change",n=>{r.reportType=n.target.value}),t.reportFormat&&t.reportFormat.addEventListener("change",n=>{r.reportFormat=n.target.value}),t.generateReportBtn&&t.generateReportBtn.addEventListener("click",g),t.addScheduleBtn&&t.addScheduleBtn.addEventListener("click",addReportSchedule),t.closeModal&&t.closeModal.addEventListener("click",p),t.prevBtn&&t.prevBtn.addEventListener("click",()=>{}),t.nextBtn&&t.nextBtn.addEventListener("click",()=>{})}function l(n){const a=i.now().setZone("Asia/Kolkata"),e=a;let o=a;switch(n){case"24h":o=a.minus({hours:24});break;case"7d":o=a.minus({days:7});break;case"30d":o=a.minus({days:30});break;case"90d":o=a.minus({days:90});break;case"1y":o=a.minus({years:1});break;case"custom":return;default:o=a.minus({hours:24})}t.startDate&&(t.startDate.value=o.toFormat("yyyy-MM-dd'T'HH:mm")),t.endDate&&(t.endDate.value=e.toFormat("yyyy-MM-dd'T'HH:mm"))}async function g(){if(!r.isGenerating)try{r.isGenerating=!0;const n=h();E();const a={type:r.reportType,format:r.reportFormat,start:n.start,end:n.end};m(0,"Initializing report generation...");const e=await fetch("../backend/generate_report.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!e.ok)throw new Error(`HTTP error! Status: ${e.status}`);const o=await e.json();if(o.error)throw new Error(o.error);await B(o.reportId),s("Report generated successfully","success"),u()}catch(n){console.error("Error generating report:",n),s(`Error: ${n.message}`,"error")}finally{r.isGenerating=!1,p()}}function h(){if(!t.startDate||!t.endDate){const c=i.now().setZone("Asia/Kolkata");return{start:c.minus({hours:24}).toFormat("yyyy-MM-dd HH:mm:ss"),end:c.toFormat("yyyy-MM-dd HH:mm:ss")}}const n=t.startDate.value,a=t.endDate.value;if(!n||!a)throw new Error("Please select both start and end dates");const e=i.fromISO(n),o=i.fromISO(a);if(!e.isValid||!o.isValid)throw new Error("Invalid date format");if(e>=o)throw new Error("Start date must be before end date");return{start:e.toFormat("yyyy-MM-dd HH:mm:ss"),end:o.toFormat("yyyy-MM-dd HH:mm:ss")}}function v(){if(!t.templatesContainer)return;const n=[{name:"Daily Summary",description:"Comprehensive daily electrical system summary",icon:"today",type:"daily"},{name:"Power Quality Report",description:"Detailed power quality analysis and compliance",icon:"high_quality",type:"power_quality"},{name:"Energy Efficiency",description:"Energy consumption and efficiency analysis",icon:"energy_savings_leaf",type:"energy_efficiency"},{name:"Stability Analysis",description:"System stability and predictive maintenance",icon:"analytics",type:"stability"},{name:"Compliance Report",description:"Regulatory compliance and standards verification",icon:"verified",type:"compliance"},{name:"Custom Report",description:"Build your own custom report template",icon:"build",type:"custom"}];let a="";n.forEach(e=>{a+=`
            <div class="template-card" onclick="selectTemplate('${e.type}')">
                <div class="template-icon">
                    <span class="material-icons-round">${e.icon}</span>
                </div>
                <div class="template-content">
                    <h4>${e.name}</h4>
                    <p>${e.description}</p>
                </div>
                <div class="template-actions">
                    <button class="action-btn">
                        <span class="material-icons-round">play_arrow</span>
                    </button>
                </div>
            </div>
        `}),t.templatesContainer.innerHTML=a}function w(){if(!t.schedulesContainer)return;const n=[{id:1,name:"Daily Power Quality",type:"power_quality",frequency:"Daily at 6:00 AM",format:"PDF",status:"Active",nextRun:"2025-01-21 06:00:00"},{id:2,name:"Weekly Summary",type:"weekly",frequency:"Weekly on Monday",format:"Excel",status:"Active",nextRun:"2025-01-27 08:00:00"}];let a="";n.forEach(e=>{const o=d.formatTimestamp(e.nextRun);a+=`
            <div class="schedule-item">
                <div class="schedule-info">
                    <h4>${e.name}</h4>
                    <div class="schedule-details">
                        <span class="schedule-frequency">${e.frequency}</span>
                        <span class="schedule-format">${e.format}</span>
                        <span class="schedule-status ${e.status.toLowerCase()}">${e.status}</span>
                    </div>
                    <div class="schedule-next">Next run: ${o}</div>
                </div>
                <div class="schedule-actions">
                    <button class="action-btn" onclick="editSchedule(${e.id})">
                        <span class="material-icons-round">edit</span>
                    </button>
                    <button class="action-btn" onclick="runScheduleNow(${e.id})">
                        <span class="material-icons-round">play_arrow</span>
                    </button>
                    <button class="action-btn danger" onclick="deleteSchedule(${e.id})">
                        <span class="material-icons-round">delete</span>
                    </button>
                </div>
            </div>
        `}),t.schedulesContainer.innerHTML=a}function u(){if(!t.reportsTableBody)return;const n=[{id:1,generated:"2025-01-20 14:30:00",type:"Daily Summary",period:"2025-01-19 to 2025-01-20",format:"PDF",size:"2.4 MB",status:"Completed"},{id:2,generated:"2025-01-20 06:00:00",type:"Power Quality",period:"2025-01-19 to 2025-01-20",format:"Excel",size:"1.8 MB",status:"Completed"},{id:3,generated:"2025-01-19 18:45:00",type:"Energy Efficiency",period:"2025-01-12 to 2025-01-19",format:"PDF",size:"3.2 MB",status:"Completed"}];let a="";n.forEach(e=>{const o=d.formatTimestamp(e.generated);a+=`
            <tr>
                <td>${o}</td>
                <td>${e.type}</td>
                <td>${e.period}</td>
                <td>${e.format}</td>
                <td>${e.size}</td>
                <td><span class="status-badge ${e.status.toLowerCase()}">${e.status}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="downloadReport(${e.id})">
                            <span class="material-icons-round">file_download</span>
                        </button>
                        <button class="action-btn" onclick="viewReport(${e.id})">
                            <span class="material-icons-round">visibility</span>
                        </button>
                        <button class="action-btn" onclick="shareReport(${e.id})">
                            <span class="material-icons-round">share</span>
                        </button>
                        <button class="action-btn danger" onclick="deleteReport(${e.id})">
                            <span class="material-icons-round">delete</span>
                        </button>
                    </div>
                </td>
            </tr>
        `}),t.reportsTableBody.innerHTML=a}function b(){if(!t.analyticsContainer)return;const n=[{title:"Reports Generated",value:"156",change:"+12%",trend:"up",icon:"description"},{title:"Total Downloads",value:"1,234",change:"+8%",trend:"up",icon:"file_download"},{title:"Storage Used",value:"2.4 GB",change:"+15%",trend:"up",icon:"storage"},{title:"Scheduled Reports",value:"8",change:"+2",trend:"up",icon:"schedule"}];let a="";n.forEach(e=>{a+=`
            <div class="analytics-card">
                <div class="analytics-icon">
                    <span class="material-icons-round">${e.icon}</span>
                </div>
                <div class="analytics-content">
                    <div class="analytics-value">${e.value}</div>
                    <div class="analytics-title">${e.title}</div>
                    <div class="analytics-change ${e.trend}">
                        <span class="material-icons-round">${e.trend==="up"?"trending_up":"trending_down"}</span>
                        ${e.change}
                    </div>
                </div>
            </div>
        `}),t.analyticsContainer.innerHTML=a}function E(){t.reportModal&&(t.reportModal.style.display="flex")}function p(){t.reportModal&&(t.reportModal.style.display="none")}function m(n,a){t.progressFill&&(t.progressFill.style.width=`${n}%`),t.progressText&&(t.progressText.textContent=a)}async function B(n){const a=[{progress:20,text:"Collecting data..."},{progress:40,text:"Processing electrical parameters..."},{progress:60,text:"Generating charts and graphs..."},{progress:80,text:"Formatting report..."},{progress:100,text:"Report completed!"}];for(const e of a)m(e.progress,e.text),await new Promise(o=>setTimeout(o,1e3))}function s(n,a="info"){t.messageContainer&&d.showMessage(n,a,t.messageContainer)}window.selectTemplate=function(n){t.reportType&&(t.reportType.value=n,r.reportType=n),s(`Selected ${n} template`,"info")};window.addReportSchedule=function(){s("Add schedule feature coming soon","info")};window.editSchedule=function(n){s("Edit schedule feature coming soon","info")};window.runScheduleNow=function(n){s("Run schedule feature coming soon","info")};window.deleteSchedule=function(n){s("Delete schedule feature coming soon","info")};window.downloadReport=function(n){s("Download report feature coming soon","info")};window.viewReport=function(n){s("View report feature coming soon","info")};window.shareReport=function(n){s("Share report feature coming soon","info")};window.deleteReport=function(n){s("Delete report feature coming soon","info")};document.addEventListener("DOMContentLoaded",y);
