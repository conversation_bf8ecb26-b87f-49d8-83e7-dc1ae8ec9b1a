<?php
// Initialize the session
session_start();

// Check if the user is logged in, if not redirect to login page
if(!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: login.php");
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Online Data Logger System - Real-time Electrical Monitoring</title>

    <!-- Chart.js and plugins -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luxon@3.0.1/build/global/luxon.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-luxon@1.2.0/dist/chartjs-adapter-luxon.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-crosshair@1.2.0/dist/chartjs-plugin-crosshair.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@2.1.0/dist/chartjs-plugin-annotation.min.js"></script>

    <!-- Grid.js for data tables -->
    <script src="https://cdn.jsdelivr.net/npm/gridjs/dist/gridjs.umd.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/gridjs/dist/theme/mermaid.min.css" rel="stylesheet" />

    <!-- Custom scripts -->
    <script src="chart-config.js?v=<?php echo time(); ?>"></script>
    <script src="advanced-dashboard.js?v=<?php echo time(); ?>"></script>

    <!-- AI Analysis scripts -->
    <script src="ai-analysis.js?v=<?php echo time(); ?>"></script>
    <script src="ai-analysis-helpers.js?v=<?php echo time(); ?>"></script>
    <script src="ai-analysis-helpers2.js?v=<?php echo time(); ?>"></script>
    <script src="ai-analysis-helpers3.js?v=<?php echo time(); ?>"></script>
    <script src="ai-analysis-html-generators.js?v=<?php echo time(); ?>"></script>
    <script src="ai-analysis-html-generators2.js?v=<?php echo time(); ?>"></script>
    <script src="ai-analysis-ui.js?v=<?php echo time(); ?>"></script>

    <!-- Advanced styling -->
    <link rel="stylesheet" href="advanced-dashboard.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="ai-analysis.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="streamlined-topbar.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="dark-status-theme.css?v=<?php echo time(); ?>">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="images/online-data-logger-logo-new.svg">

    <!-- Meta tags for better SEO and sharing -->
    <meta name="description" content="Online Data Logger System for real-time electrical parameter monitoring. Advanced dashboard with AI analysis and historical data tracking.">
    <meta name="keywords" content="data logger, electrical monitoring, power monitoring, real-time data, energy management, power quality">
    <meta property="og:title" content="Online Data Logger System - Real-time Electrical Monitoring">
    <meta property="og:description" content="Online Data Logger System for real-time electrical parameter monitoring. Advanced dashboard with AI analysis and historical data tracking.">
    <meta property="og:type" content="website">
</head>
<body class="dark-theme">
    <div class="dashboard-container">
        <!-- Sidebar Navigation -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="images/online-data-logger-sidebar.svg" alt="Online Data Logger System" class="company-logo" width="180">
            </div>

            <nav class="sidebar-nav">
                <ul>
                    <li class="active">
                        <a href="#" id="dashboardLink">
                            <span class="material-icons-round">dashboard</span>
                            <span>Live Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="history.php" id="historyLink">
                            <span class="material-icons-round">history</span>
                            <span>Historical Data</span>
                        </a>
                    </li>
                    <li>
                        <a href="stability_analysis.php" id="analyticsLink">
                            <span class="material-icons-round">analytics</span>
                            <span>Stability Analysis</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" id="alertsLink">
                            <span class="material-icons-round">warning</span>
                            <span>Alerts & Events</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" id="reportsLink">
                            <span class="material-icons-round">insights</span>
                            <span>Reports</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" id="settingsLink">
                            <span class="material-icons-round">settings</span>
                            <span>System Settings</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <button id="themeToggle" class="theme-toggle">
                    <span class="material-icons-round">dark_mode</span>
                    <span>Dark Mode</span>
                </button>
                <div class="system-status">
                    <span class="status-indicator online"></span>
                    <span>System Online</span>
                </div>
                <a href="logout.php" class="logout-button">
                    <span class="material-icons-round">logout</span>
                    <span>Logout</span>
                </a>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Dark Status Header -->
            <header class="dark-status-header">
                <div class="dark-status-title">
                    
                    <div class="subtitle">Real-time electrical parameter monitoring</div>
                </div>

                <div class="dark-status-boxes">
                    <div class="dark-status-box system">
                        <div class="dark-status-icon">
                            <span class="material-icons-round">check_circle</span>
                        </div>
                        <div class="dark-status-text">
                            <div class="dark-status-label">System Status</div>
                            <div class="dark-status-value">Operational</div>
                        </div>
                    </div>

                    <div class="dark-status-box">
                        <div class="dark-status-icon">
                            <span class="material-icons-round">schedule</span>
                        </div>
                        <div class="dark-status-text">
                            <div class="dark-status-label">Last Updated</div>
                            <div class="dark-status-value" id="lastUpdatedTime">Loading...</div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Control Bar -->
            <div class="streamlined-topbar">
                <div class="topbar-controls">
                    <div class="time-controls">
                        <button id="prevHourBtn" class="nav-button" title="Previous Hour">
                            <span class="material-icons-round">arrow_back</span>
                        </button>
                        <span id="timeDisplay" class="time-display">Current</span>
                        <button id="nextHourBtn" class="nav-button" title="Next Hour">
                            <span class="material-icons-round">arrow_forward</span>
                        </button>
                    </div>

                    <div class="action-buttons">
                        <button id="autoRangeBtn" class="action-button">
                            <span class="material-icons-round icon">auto_fix_high</span>
                            <span>Auto Range</span>
                        </button>

                        <button id="aiAnalysisBtn" class="action-button primary">
                            <span class="material-icons-round icon">psychology</span>
                            <span>AI Analysis</span>
                        </button>

                        <button id="showAllDataBtn" class="action-button primary">
                            <span class="material-icons-round icon">history</span>
                            <span>Historical View</span>
                        </button>

                        <button id="exportDataBtn" class="action-button secondary">
                            <span class="material-icons-round icon">download</span>
                            <span>Export</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Dashboard Summary Cards -->
            <section class="summary-cards">
                <div class="summary-card voltage">
                    <div class="card-icon">
                        <span class="material-icons-round">bolt</span>
                    </div>
                    <div class="card-content">
                        <h3>Average Voltage</h3>
                        <p class="card-value" id="avgVoltage">-- V</p>
                        <div class="trend-indicator up">
                            <span class="material-icons-round">trending_up</span>
                            <span class="trend-value">0.5%</span>
                        </div>
                    </div>
                </div>

                <div class="summary-card current">
                    <div class="card-icon">
                        <span class="material-icons-round">electric_bolt</span>
                    </div>
                    <div class="card-content">
                        <h3>Total Current</h3>
                        <p class="card-value" id="totalCurrent">-- A</p>
                        <div class="trend-indicator down">
                            <span class="material-icons-round">trending_down</span>
                            <span class="trend-value">1.2%</span>
                        </div>
                    </div>
                </div>

                <div class="summary-card power">
                    <div class="card-icon">
                        <span class="material-icons-round">power</span>
                    </div>
                    <div class="card-content">
                        <h3>Total Power</h3>
                        <p class="card-value" id="totalPower">-- kW</p>
                        <div class="trend-indicator up">
                            <span class="material-icons-round">trending_up</span>
                            <span class="trend-value">2.3%</span>
                        </div>
                    </div>
                </div>

                <div class="summary-card frequency">
                    <div class="card-icon">
                        <span class="material-icons-round">speed</span>
                    </div>
                    <div class="card-content">
                        <h3>Frequency</h3>
                        <p class="card-value" id="freqValue">-- Hz</p>
                        <div class="trend-indicator stable">
                            <span class="material-icons-round">trending_flat</span>
                            <span class="trend-value">Stable</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Main Dashboard Grid -->
            <section class="dashboard-grid">
                <!-- Voltage Graph -->
                <div class="graph-widget" id="voltageWidget">
                    <div class="widget-header">
                        <div class="widget-title">
                            <span class="material-icons-round">bolt</span>
                            <h3>Line Voltages</h3>
                        </div>
                        <div class="widget-controls">
                            <button class="widget-control" onclick="toggleWidgetExpand('voltageWidget')">
                                <span class="material-icons-round">fullscreen</span>
                            </button>
                            <button class="widget-control" onclick="scrollChartBackward(voltageChart, 30)">
                                <span class="material-icons-round">arrow_back</span>
                            </button>
                            <button class="widget-control" onclick="scrollChartForward(voltageChart, 30)">
                                <span class="material-icons-round">arrow_forward</span>
                            </button>
                            <button class="widget-control" id="voltagePause" onclick="togglePause('voltage')" data-paused="false">
                                <span class="material-icons-round">pause</span>
                            </button>
                            <button class="widget-control" id="voltageAutoScroll" onclick="toggleAutoScroll('voltage')" data-auto-scroll="true">
                                <span class="material-icons-round">autorenew</span>
                            </button>
                        </div>
                    </div>
                    <div class="widget-content">
                        <canvas id="voltageChart"></canvas>
                        <div class="instant-values" id="voltageValues"></div>
                    </div>
                </div>

                <!-- Current Graph -->
                <div class="graph-widget" id="currentWidget">
                    <div class="widget-header">
                        <div class="widget-title">
                            <span class="material-icons-round">electric_bolt</span>
                            <h3>Phase Currents</h3>
                        </div>
                        <div class="widget-controls">
                            <button class="widget-control" onclick="toggleWidgetExpand('currentWidget')">
                                <span class="material-icons-round">fullscreen</span>
                            </button>
                            <button class="widget-control" onclick="scrollChartBackward(currentChart, 30)">
                                <span class="material-icons-round">arrow_back</span>
                            </button>
                            <button class="widget-control" onclick="scrollChartForward(currentChart, 30)">
                                <span class="material-icons-round">arrow_forward</span>
                            </button>
                            <button class="widget-control" id="currentPause" onclick="togglePause('current')" data-paused="false">
                                <span class="material-icons-round">pause</span>
                            </button>
                            <button class="widget-control" id="currentAutoScroll" onclick="toggleAutoScroll('current')" data-auto-scroll="true">
                                <span class="material-icons-round">autorenew</span>
                            </button>
                        </div>
                    </div>
                    <div class="widget-content">
                        <canvas id="currentChart"></canvas>
                        <div class="instant-values" id="currentValues"></div>
                    </div>
                </div>

                <!-- Power Factor Graph -->
                <div class="graph-widget" id="pfWidget">
                    <div class="widget-header">
                        <div class="widget-title">
                            <span class="material-icons-round">show_chart</span>
                            <h3>Power Factors</h3>
                        </div>
                        <div class="widget-controls">
                            <button class="widget-control" onclick="toggleWidgetExpand('pfWidget')">
                                <span class="material-icons-round">fullscreen</span>
                            </button>
                            <button class="widget-control" onclick="scrollChartBackward(pfChart, 30)">
                                <span class="material-icons-round">arrow_back</span>
                            </button>
                            <button class="widget-control" onclick="scrollChartForward(pfChart, 30)">
                                <span class="material-icons-round">arrow_forward</span>
                            </button>
                            <button class="widget-control" id="pfPause" onclick="togglePause('pf')" data-paused="false">
                                <span class="material-icons-round">pause</span>
                            </button>
                            <button class="widget-control" id="pfAutoScroll" onclick="toggleAutoScroll('pf')" data-auto-scroll="true">
                                <span class="material-icons-round">autorenew</span>
                            </button>
                        </div>
                    </div>
                    <div class="widget-content">
                        <canvas id="pfChart"></canvas>
                        <div class="instant-values" id="pfValues"></div>
                    </div>
                </div>

                <!-- KVA Graph -->
                <div class="graph-widget" id="kvaWidget">
                    <div class="widget-header">
                        <div class="widget-title">
                            <span class="material-icons-round">electric_meter</span>
                            <h3>KVA Values</h3>
                        </div>
                        <div class="widget-controls">
                            <button class="widget-control" onclick="toggleWidgetExpand('kvaWidget')">
                                <span class="material-icons-round">fullscreen</span>
                            </button>
                            <button class="widget-control" onclick="scrollChartBackward(kvaChart, 30)">
                                <span class="material-icons-round">arrow_back</span>
                            </button>
                            <button class="widget-control" onclick="scrollChartForward(kvaChart, 30)">
                                <span class="material-icons-round">arrow_forward</span>
                            </button>
                            <button class="widget-control" id="kvaPause" onclick="togglePause('kva')" data-paused="false">
                                <span class="material-icons-round">pause</span>
                            </button>
                            <button class="widget-control" id="kvaAutoScroll" onclick="toggleAutoScroll('kva')" data-auto-scroll="true">
                                <span class="material-icons-round">autorenew</span>
                            </button>
                        </div>
                    </div>
                    <div class="widget-content">
                        <canvas id="kvaChart"></canvas>
                        <div class="instant-values" id="kvaValues"></div>
                    </div>
                </div>

                <!-- Total Power Graph -->
                <div class="graph-widget" id="totalPowerWidget">
                    <div class="widget-header">
                        <div class="widget-title">
                            <span class="material-icons-round">power</span>
                            <h3>Total Power</h3>
                        </div>
                        <div class="widget-controls">
                            <button class="widget-control" onclick="toggleWidgetExpand('totalPowerWidget')">
                                <span class="material-icons-round">fullscreen</span>
                            </button>
                            <button class="widget-control" onclick="scrollChartBackward(totalPowerChart, 30)">
                                <span class="material-icons-round">arrow_back</span>
                            </button>
                            <button class="widget-control" onclick="scrollChartForward(totalPowerChart, 30)">
                                <span class="material-icons-round">arrow_forward</span>
                            </button>
                            <button class="widget-control" id="totalPowerPause" onclick="togglePause('totalPower')" data-paused="false">
                                <span class="material-icons-round">pause</span>
                            </button>
                            <button class="widget-control" id="totalPowerAutoScroll" onclick="toggleAutoScroll('totalPower')" data-auto-scroll="true">
                                <span class="material-icons-round">autorenew</span>
                            </button>
                        </div>
                    </div>
                    <div class="widget-content">
                        <canvas id="totalPowerChart"></canvas>
                        <div class="instant-values" id="totalPowerValues"></div>
                    </div>
                </div>

                <!-- Frequency Graph -->
                <div class="graph-widget" id="frequencyWidget">
                    <div class="widget-header">
                        <div class="widget-title">
                            <span class="material-icons-round">speed</span>
                            <h3>Frequency</h3>
                        </div>
                        <div class="widget-controls">
                            <button class="widget-control" onclick="toggleWidgetExpand('frequencyWidget')">
                                <span class="material-icons-round">fullscreen</span>
                            </button>
                            <button class="widget-control" onclick="scrollChartBackward(frequencyChart, 30)">
                                <span class="material-icons-round">arrow_back</span>
                            </button>
                            <button class="widget-control" onclick="scrollChartForward(frequencyChart, 30)">
                                <span class="material-icons-round">arrow_forward</span>
                            </button>
                            <button class="widget-control" id="frequencyPause" onclick="togglePause('frequency')" data-paused="false">
                                <span class="material-icons-round">pause</span>
                            </button>
                            <button class="widget-control" id="frequencyAutoScroll" onclick="toggleAutoScroll('frequency')" data-auto-scroll="true">
                                <span class="material-icons-round">autorenew</span>
                            </button>
                        </div>
                    </div>
                    <div class="widget-content">
                        <canvas id="frequencyChart"></canvas>
                        <div class="instant-values" id="frequencyValue"></div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Modals -->
    <div class="modal" id="settingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Dashboard Settings</h2>
                <button class="close-modal">
                    <span class="material-icons-round">close</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="settings-section">
                    <h3>Display Settings</h3>
                    <div class="setting-item">
                        <label for="refreshRate">Data Refresh Rate (seconds)</label>
                        <input type="number" id="refreshRate" min="1" max="60" value="2">
                    </div>
                    <div class="setting-item">
                        <label for="timeWindow">Default Time Window (minutes)</label>
                        <input type="number" id="timeWindow" min="1" max="60" value="1">
                    </div>
                    <div class="setting-item">
                        <label for="decimalPlaces">Decimal Places</label>
                        <input type="number" id="decimalPlaces" min="0" max="5" value="3">
                    </div>
                </div>

                <div class="settings-section">
                    <h3>Chart Settings</h3>
                    <div class="setting-item">
                        <label for="chartTheme">Chart Theme</label>
                        <select id="chartTheme">
                            <option value="professional">Professional</option>
                            <option value="material">Material</option>
                            <option value="pastel">Pastel</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label for="lineThickness">Line Thickness</label>
                        <input type="range" id="lineThickness" min="0.5" max="3" step="0.5" value="0.5">
                    </div>
                    <div class="setting-item checkbox">
                        <input type="checkbox" id="showGridLines" checked>
                        <label for="showGridLines">Show Grid Lines</label>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>Alert Settings</h3>
                    <div class="setting-item">
                        <label for="voltageAlertHigh">Voltage High Alert (V)</label>
                        <input type="number" id="voltageAlertHigh" value="450">
                    </div>
                    <div class="setting-item">
                        <label for="voltageAlertLow">Voltage Low Alert (V)</label>
                        <input type="number" id="voltageAlertLow" value="350">
                    </div>
                    <div class="setting-item">
                        <label for="frequencyAlertHigh">Frequency High Alert (Hz)</label>
                        <input type="number" id="frequencyAlertHigh" value="55">
                    </div>
                    <div class="setting-item">
                        <label for="frequencyAlertLow">Frequency Low Alert (Hz)</label>
                        <input type="number" id="frequencyAlertLow" value="45">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-button secondary" id="resetSettings">Reset to Defaults</button>
                <button class="modal-button primary" id="saveSettings">Save Settings</button>
            </div>
        </div>
    </div>

    <div class="modal" id="exportModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Export Data</h2>
                <button class="close-modal">
                    <span class="material-icons-round">close</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="export-options">
                    <div class="export-option">
                        <input type="radio" name="exportType" id="exportCSV" value="csv" checked>
                        <label for="exportCSV">
                            <span class="material-icons-round">description</span>
                            <div>
                                <h4>CSV File</h4>
                                <p>Export data as comma-separated values</p>
                            </div>
                        </label>
                    </div>

                    <div class="export-option">
                        <input type="radio" name="exportType" id="exportJSON" value="json">
                        <label for="exportJSON">
                            <span class="material-icons-round">data_object</span>
                            <div>
                                <h4>JSON File</h4>
                                <p>Export data in JSON format</p>
                            </div>
                        </label>
                    </div>

                    <div class="export-option">
                        <input type="radio" name="exportType" id="exportPDF" value="pdf">
                        <label for="exportPDF">
                            <span class="material-icons-round">picture_as_pdf</span>
                            <div>
                                <h4>PDF Report</h4>
                                <p>Export data as a PDF report</p>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="export-settings">
                    <h3>Export Settings</h3>
                    <div class="setting-item">
                        <label for="exportTimeRange">Time Range</label>
                        <select id="exportTimeRange">
                            <option value="visible">Currently Visible Data</option>
                            <option value="hour">Last Hour</option>
                            <option value="day">Last 24 Hours</option>
                            <option value="week">Last Week</option>
                            <option value="custom">Custom Range</option>
                        </select>
                    </div>

                    <div class="date-range" id="customDateRange" style="display: none;">
                        <div class="setting-item">
                            <label for="exportStartDate">Start Date</label>
                            <input type="datetime-local" id="exportStartDate">
                        </div>
                        <div class="setting-item">
                            <label for="exportEndDate">End Date</label>
                            <input type="datetime-local" id="exportEndDate">
                        </div>
                    </div>

                    <div class="setting-item checkbox">
                        <input type="checkbox" id="includeAllParameters" checked>
                        <label for="includeAllParameters">Include All Parameters</label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-button secondary" id="cancelExport">Cancel</button>
                <button class="modal-button primary" id="confirmExport">Export Data</button>
            </div>
        </div>
    </div>

    <!-- Notification System -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- Status Indicator -->
    <div id="statusIndicator"></div>

    <!-- Fullscreen Widget Container -->
    <div class="fullscreen-widget" id="fullscreenWidget">
        <div class="fullscreen-header">
            <div class="fullscreen-title">
                <span class="material-icons-round" id="fullscreenIcon"></span>
                <h3 id="fullscreenTitle"></h3>
            </div>
            <div class="fullscreen-controls">
                <button class="fullscreen-control" id="fullscreenClose">
                    <span class="material-icons-round">close</span>
                </button>
            </div>
        </div>
        <div class="fullscreen-content" id="fullscreenContent"></div>
    </div>
</body>
</html>
